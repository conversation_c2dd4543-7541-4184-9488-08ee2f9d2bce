<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Booking;
use App\Models\Destination;
use App\Models\ChatSession;
use App\Models\Activity;
use App\Models\Hotel;
use App\Models\Transfer;

class TestDashboardData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:dashboard-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test dashboard data and relationships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Travel Platform Dashboard Data...');
        $this->line('');

        // Test basic counts
        $this->info('📊 Basic Data Counts:');
        $this->line("Users: " . User::count());
        $this->line("Bookings: " . Booking::count());
        $this->line("Destinations: " . Destination::count());
        $this->line("Activities: " . Activity::count());
        $this->line("Hotels: " . Hotel::count());
        $this->line("Transfers: " . Transfer::count());
        $this->line("Chat Sessions: " . ChatSession::count());
        $this->line('');

        // Test booking types
        $this->info('📋 Booking Distribution:');
        $activityBookings = Booking::where('bookable_type', Activity::class)->count();
        $hotelBookings = Booking::where('bookable_type', Hotel::class)->count();
        $transferBookings = Booking::where('bookable_type', Transfer::class)->count();
        
        $this->line("Activity Bookings: {$activityBookings}");
        $this->line("Hotel Bookings: {$hotelBookings}");
        $this->line("Transfer Bookings: {$transferBookings}");
        $this->line('');

        // Test booking statuses
        $this->info('📈 Booking Status Distribution:');
        $confirmed = Booking::where('status', 'confirmed')->count();
        $pending = Booking::where('status', 'pending')->count();
        $cancelled = Booking::where('status', 'cancelled')->count();
        
        $this->line("Confirmed: {$confirmed}");
        $this->line("Pending: {$pending}");
        $this->line("Cancelled: {$cancelled}");
        $this->line('');

        // Test revenue
        $this->info('💰 Revenue Data:');
        $totalRevenue = Booking::where('status', 'confirmed')->sum('total_amount');
        $avgBookingValue = Booking::avg('total_amount');
        
        $this->line("Total Revenue: €" . number_format($totalRevenue, 2));
        $this->line("Average Booking Value: €" . number_format($avgBookingValue, 2));
        $this->line('');

        // Test user relationships
        $this->info('👥 User Relationships:');
        $usersWithBookings = User::whereHas('bookings')->count();
        $usersWithChatSessions = User::whereHas('chatSessions')->count();
        
        $this->line("Users with Bookings: {$usersWithBookings}");
        $this->line("Users with Chat Sessions: {$usersWithChatSessions}");
        $this->line('');

        // Test polymorphic relationships
        $this->info('🔗 Testing Polymorphic Relationships:');
        
        $sampleBooking = Booking::first();
        if ($sampleBooking) {
            $this->line("Sample Booking ID: {$sampleBooking->id}");
            $this->line("Bookable Type: {$sampleBooking->bookable_type}");
            $this->line("Bookable ID: {$sampleBooking->bookable_id}");
            
            try {
                $bookable = $sampleBooking->bookable;
                if ($bookable) {
                    $this->line("✅ Polymorphic relationship working");
                    $this->line("Bookable Model: " . get_class($bookable));
                } else {
                    $this->warn("⚠️ Bookable relationship returned null");
                }
            } catch (\Exception $e) {
                $this->error("❌ Polymorphic relationship error: " . $e->getMessage());
            }
        } else {
            $this->warn("No bookings found to test relationships");
        }
        
        $this->line('');

        // Test destination relationships
        $this->info('🗺️ Testing Destination Relationships:');
        $destinationsWithActivities = Destination::whereHas('activities')->count();
        $destinationsWithHotels = Destination::whereHas('hotels')->count();
        
        $this->line("Destinations with Activities: {$destinationsWithActivities}");
        $this->line("Destinations with Hotels: {$destinationsWithHotels}");
        $this->line('');

        $this->info('✅ Dashboard data test completed!');
        $this->info('Your dashboard should now display real data from your database.');
        $this->line('');
        $this->info('🚀 Access your dashboard at: /admin');

        return 0;
    }
}
