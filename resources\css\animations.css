/* Custom Animations for Destination Detail Page */

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes float-delayed {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

@keyframes fade-in-down {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes fade-in-delayed {
    0% { opacity: 0; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes slide-up {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in-up {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce-gentle {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

@keyframes count-up {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes count-up-delayed {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    30% {
        opacity: 0;
        transform: scale(0.5);
    }
    80% {
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes count-up-slow {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    60% {
        opacity: 0;
        transform: scale(0.5);
    }
    90% {
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes star-twinkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Animation Classes */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 4s ease-in-out infinite;
    animation-delay: 1s;
}

.animate-float-slow {
    animation: float-slow 5s ease-in-out infinite;
    animation-delay: 2s;
}

.animate-fade-in-down {
    animation: fade-in-down 0.8s ease-out;
}

.animate-fade-in {
    animation: fade-in 1s ease-out;
}

.animate-fade-in-delayed {
    animation: fade-in-delayed 2s ease-out;
}

.animate-slide-up {
    animation: slide-up 0.8s ease-out;
}

.animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
    opacity: 0;
}

.animate-bounce-gentle {
    animation: bounce-gentle 2s infinite;
}

.animate-count-up {
    animation: count-up 1s ease-out;
}

.animate-count-up-delayed {
    animation: count-up-delayed 1.5s ease-out;
}

.animate-count-up-slow {
    animation: count-up-slow 2s ease-out;
}

.animate-star-twinkle {
    animation: star-twinkle 2s ease-in-out infinite;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
