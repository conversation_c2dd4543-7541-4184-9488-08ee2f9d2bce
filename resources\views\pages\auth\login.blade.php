<!-- resources/views/auth/login.blade.php -->
<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">
        <div class="flex flex-col items-center space-y-3">
            <img src="{{ asset('storage/avatar.jpg') }}" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Welcome back!</h2>
            <p class="text-center text-gray-500">Stay signed in with your account</p>
        </div>

        <!-- Modified Breeze Form with your design -->
        <form wire:submit.prevent="authenticate">
            @csrf
            <!-- Email -->
            <div class="mb-4">
                <input wire:model="email" type="email"
                    class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring" placeholder="Email"
                    required autofocus>
                @error('email')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password -->
            <div class="mb-4 relative">
                <input wire:model="password" id="password-input" type="password"
                    class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring" placeholder="Password"
                    required>
                <span class="absolute inset-y-0 right-3 flex items-center cursor-pointer"
                    x-on:click="const input = $el.previousElementSibling; input.type = input.type === 'password' ? 'text' : 'password';">
                    <i class="fa-regular fa-eye-slash"></i>
                </span>
                @error('password')
                    <span class="text-red-500 text-sm">{{ $message }}</span>
                @enderror
            </div>

            <!-- Remember Me -->
            <label class="flex items-center mb-4">
                <input wire:model="remember" type="checkbox" class="mr-2">
                <span>Remember me</span>
            </label>

            <button type="submit" class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md">
                Log in
            </button>
        </form>

        <div class="text-center text-gray-400">or</div>

        <button class="flex items-center justify-center w-full px-4 py-2 space-x-2 border rounded-md">
            <img src="https://www.google.com/favicon.ico" class="w-5 h-5" alt="Google">
            <span>Continue with Google</span>
        </button>

        <p class="text-sm text-center">
            Don't have an account?
            <a href="{{ route('register') }}" class="font-semibold">Sign Up</a>
        </p>
    </div>

    <!-- Required scripts -->
    @push('scripts')
        <!-- AlpineJS for password toggle -->
        <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
        <!-- Font Awesome (replace with your kit) -->
        <script src="https://kit.fontawesome.com/YOUR_CODE.js" crossorigin="anonymous"></script>
    @endpush
</main>
