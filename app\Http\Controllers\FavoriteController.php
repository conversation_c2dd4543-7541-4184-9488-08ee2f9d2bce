<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Hotel;
use App\Models\Activity;
use App\Models\Destination;

class FavoriteController extends Controller
{
    /**
     * Toggle favorite status for an item.
     */
    public function toggle(Request $request): JsonResponse
    {
        // Debug logging
        Log::info('Favorite toggle request', [
            'type' => $request->type,
            'id' => $request->id,
            'user_id' => Auth::id()
        ]);

        $request->validate([
            'type' => 'required|string|in:hotel,activity,destination,transfer',
            'id' => 'required|integer|exists:' . $this->getTableName($request->type) . ',id',
        ]);

        $user = Auth::user();
        $model = $this->getModel($request->type, $request->id);

        if (!$model) {
            Log::error('Model not found', [
                'type' => $request->type,
                'id' => $request->id
            ]);
            return response()->json(['error' => 'Item not found'], 404);
        }

        $isFavorited = $user->hasFavorited($model);

        if ($isFavorited) {
            $user->removeFromFavorites($model);
            $message = 'Removed from favorites';
            $favorited = false;
        } else {
            $user->addToFavorites($model);
            $message = 'Added to favorites';
            $favorited = true;
        }

        Log::info('Favorite toggle result', [
            'favorited' => $favorited,
            'message' => $message
        ]);

        return response()->json([
            'success' => true,
            'message' => $message,
            'favorited' => $favorited,
        ]);
    }

    /**
     * Get user's favorites.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $favorites = $user->favorites()->with('favoritable')->get();

        $groupedFavorites = [
            'hotels' => [],
            'activities' => [],
            'destinations' => [],
        ];

        foreach ($favorites as $favorite) {
            $type = strtolower(class_basename($favorite->favoritable_type));
            if (isset($groupedFavorites[$type . 's'])) {
                $groupedFavorites[$type . 's'][] = $favorite->favoritable;
            }
        }

        return response()->json([
            'success' => true,
            'favorites' => $groupedFavorites,
        ]);
    }

    /**
     * Get the appropriate model instance.
     */
    private function getModel(string $type, int $id)
    {
        return match ($type) {
            'hotel' => Hotel::find($id),
            'activity' => Activity::find($id),
            'destination' => Destination::find($id),
            default => null,
        };
    }

    /**
     * Get the table name for validation.
     */
    private function getTableName(string $type): string
    {
        return match ($type) {
            'hotel' => 'hotels',
            'activity' => 'activities',
            'destination' => 'destinations',
            default => '',
        };
    }
}
