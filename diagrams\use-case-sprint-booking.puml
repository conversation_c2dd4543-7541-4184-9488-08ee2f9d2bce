@startuml Sprint Booking & Maps Use Case Diagram
!theme plain
skinparam packageStyle rectangle
skinparam usecase {
    BackgroundColor White
    BorderColor Black
    BorderThickness 2
    ArrowColor Black
}
skinparam actor {
    BackgroundColor White
    BorderColor Black
    BorderThickness 2
}

title Sprint Use Case Diagram - Interactive Maps & Booking System

' Actors
actor "👤 Visitor" as Visitor
actor "👤 User" as User

' System boundary
rectangle "Travel Platform System" {
    
    ' Interactive Maps Package
    package "Interactive Maps" {
        usecase "View Product\nLocations on Map" as UC2
        note right of UC2 : Priority: Medium\nView hotels, activities,\ntransfers on interactive map
    }
    
    ' Favorites Management Package
    package "Favorites Management" {
        usecase "Add to Favorites" as UC13a
        usecase "Remove from\nFavorites" as UC13b
        usecase "View Favorites\nList" as UC13c
        note right of UC13c : Priority: Medium\nManage personal\nfavorites collection
    }
    
    ' Booking System Package
    package "Booking System" {
        usecase "Book Product" as UC14
        usecase "Select Date\nand Time" as UC14a
        usecase "Choose Number\nof Participants" as UC14b
        usecase "Complete Payment" as UC14c
        note right of UC14 : Priority: High\nBook hotels, activities,\ntransfers with payment
    }
    
    ' Trip Management Package
    package "Trip Management" {
        usecase "View My Trips" as UC15
        usecase "View Booking\nDetails" as UC15a
        usecase "Cancel Booking" as UC16
        usecase "Check Cancellation\nPolicies" as UC16a
        note right of UC15 : Priority: High\nManage personal bookings\nand trip history
    }
    
    ' Support System Package
    package "Support System" {
        usecase "Contact Support" as UC17
        usecase "Send Support\nMessage" as UC17a
        usecase "View Support\nHistory" as UC17b
        note right of UC17 : Priority: Medium\nCustomer service\nand assistance
    }
}

' Actor relationships
Visitor --|> User : <<extends>>

' Use case relationships - Interactive Maps
Visitor --> UC2
User --> UC2

' Use case relationships - Favorites (User only)
User --> UC13a
User --> UC13b
User --> UC13c
UC13a .> UC13c : <<include>>
UC13b .> UC13c : <<include>>

' Use case relationships - Booking (User only)
User --> UC14
UC14 ..> UC14a : <<include>>
UC14 ..> UC14b : <<include>>
UC14 ..> UC14c : <<include>>

' Use case relationships - Trip Management (User only)
User --> UC15
User --> UC16
UC15 ..> UC15a : <<include>>
UC16 ..> UC16a : <<include>>

' Use case relationships - Support (User only)
User --> UC17
UC17 ..> UC17a : <<include>>
UC17 ..> UC17b : <<include>>

' Cross-package relationships
UC14 ..> UC15 : <<extend>>\nafter booking
UC16 ..> UC15 : <<extend>>\nupdate trip list

@enduml
