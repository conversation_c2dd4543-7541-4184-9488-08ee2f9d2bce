<?php

namespace App\Filament\Widgets;

use App\Models\Booking;
use App\Models\Hotel;
use App\Models\Activity;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class RevenuePerProductChart extends ChartWidget
{
    protected static ?string $heading = '💰 Revenue by Product Type';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'total';

    protected function getFilters(): ?array
    {
        return [
            'total' => 'All Time',
            'month' => 'This Month',
            'quarter' => 'This Quarter',
            'year' => 'This Year',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        switch ($activeFilter) {
            case 'month':
                return $this->getMonthlyData();
            case 'quarter':
                return $this->getQuarterlyData();
            case 'year':
                return $this->getYearlyData();
            default:
                return $this->getTotalData();
        }
    }

    private function getTotalData(): array
    {
        // Get revenue by product type
        $revenueData = Booking::select('bookable_type', DB::raw('SUM(total_amount) as total_revenue'))
            ->where('status', 'confirmed')
            ->groupBy('bookable_type')
            ->get();

        return $this->formatRevenueData($revenueData);
    }

    private function getMonthlyData(): array
    {
        $revenueData = Booking::select('bookable_type', DB::raw('SUM(total_amount) as total_revenue'))
            ->where('status', 'confirmed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->groupBy('bookable_type')
            ->get();

        return $this->formatRevenueData($revenueData);
    }

    private function getQuarterlyData(): array
    {
        $currentQuarter = ceil(now()->month / 3);
        $quarterStart = (($currentQuarter - 1) * 3) + 1;
        $quarterEnd = $currentQuarter * 3;

        $revenueData = Booking::select('bookable_type', DB::raw('SUM(total_amount) as total_revenue'))
            ->where('status', 'confirmed')
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', '>=', $quarterStart)
            ->whereMonth('created_at', '<=', $quarterEnd)
            ->groupBy('bookable_type')
            ->get();

        return $this->formatRevenueData($revenueData);
    }

    private function getYearlyData(): array
    {
        $revenueData = Booking::select('bookable_type', DB::raw('SUM(total_amount) as total_revenue'))
            ->where('status', 'confirmed')
            ->whereYear('created_at', now()->year)
            ->groupBy('bookable_type')
            ->get();

        return $this->formatRevenueData($revenueData);
    }

    private function formatRevenueData($revenueData): array
    {
        $productTypes = [
            Hotel::class => 'Hotels',
            Activity::class => 'Activities',
            'App\\Models\\Transfer' => 'Transfers',
        ];

        $labels = [];
        $data = [];
        $colors = [
            '#ec4899', // Pink for Hotels
            '#3b82f6', // Blue for Activities  
            '#10b981', // Green for Transfers
        ];

        $totalRevenue = $revenueData->sum('total_revenue');

        foreach ($productTypes as $class => $label) {
            $revenue = $revenueData->where('bookable_type', $class)->first()?->total_revenue ?? 0;
            
            if ($revenue > 0 || $totalRevenue == 0) {
                $labels[] = $label;
                $data[] = round($revenue, 2);
            }
        }

        // If no data, show placeholder
        if (empty($data)) {
            $labels = ['Hotels', 'Activities', 'Transfers'];
            $data = [0, 0, 0];
        }

        return [
            'datasets' => [
                [
                    'label' => 'Revenue (€)',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return label + ": €" + value.toFixed(2) + " (" + percentage + "%)";
                        }'
                    ]
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'cutout' => '60%',
        ];
    }

    public function getDescription(): ?string
    {
        $totalRevenue = Booking::where('status', 'confirmed')->sum('total_amount');
        $totalBookings = Booking::where('status', 'confirmed')->count();
        
        $filterText = match($this->filter) {
            'month' => 'this month',
            'quarter' => 'this quarter', 
            'year' => 'this year',
            default => 'all time'
        };

        return "Total Revenue: €" . number_format($totalRevenue, 2) . " from {$totalBookings} confirmed bookings ({$filterText})";
    }
}
