<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Filament\Widgets\PopularDestinationsChart;

class TestSpecificWidget extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:specific-widget';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test specific widget methods to find toArray() error';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing PopularDestinationsChart widget methods...');
        $this->line('');

        $widget = new PopularDestinationsChart();

        try {
            $this->info('Testing getBookingsData()...');
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getBookingsData');
            $method->setAccessible(true);
            $result = $method->invoke($widget);
            $this->info('✅ getBookingsData() - SUCCESS');
            $this->line('Data structure: ' . json_encode(array_keys($result)));
        } catch (\Exception $e) {
            $this->error('❌ getBookingsData() - ERROR: ' . $e->getMessage());
            $this->line('File: ' . $e->getFile() . ':' . $e->getLine());
        }

        $this->line('');

        try {
            $this->info('Testing getFavoritesData()...');
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getFavoritesData');
            $method->setAccessible(true);
            $result = $method->invoke($widget);
            $this->info('✅ getFavoritesData() - SUCCESS');
        } catch (\Exception $e) {
            $this->error('❌ getFavoritesData() - ERROR: ' . $e->getMessage());
            $this->line('File: ' . $e->getFile() . ':' . $e->getLine());
        }

        $this->line('');

        try {
            $this->info('Testing getRevenueData()...');
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getRevenueData');
            $method->setAccessible(true);
            $result = $method->invoke($widget);
            $this->info('✅ getRevenueData() - SUCCESS');
        } catch (\Exception $e) {
            $this->error('❌ getRevenueData() - ERROR: ' . $e->getMessage());
            $this->line('File: ' . $e->getFile() . ':' . $e->getLine());
        }

        $this->line('');

        try {
            $this->info('Testing getData() with different filters...');
            $reflection = new \ReflectionClass($widget);
            $method = $reflection->getMethod('getData');
            $method->setAccessible(true);

            // Test with bookings filter
            $widget->filter = 'bookings';
            $result = $method->invoke($widget);
            $this->info('✅ getData() with bookings filter - SUCCESS');

            // Test with favorites filter
            $widget->filter = 'favorites';
            $result = $method->invoke($widget);
            $this->info('✅ getData() with favorites filter - SUCCESS');

            // Test with revenue filter
            $widget->filter = 'revenue';
            $result = $method->invoke($widget);
            $this->info('✅ getData() with revenue filter - SUCCESS');

        } catch (\Exception $e) {
            $this->error('❌ getData() - ERROR: ' . $e->getMessage());
            $this->line('File: ' . $e->getFile() . ':' . $e->getLine());
            $this->line('Stack trace:');
            $this->line($e->getTraceAsString());
        }

        return 0;
    }
}
