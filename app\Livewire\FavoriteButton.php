<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class FavoriteButton extends Component
{
    public $model;
    public $isFavorited = false;

    public function mount($model)
    {
        $this->model = $model;
        $this->checkFavoriteStatus();
    }

    public function checkFavoriteStatus()
    {
        if (Auth::check()) {
            $this->isFavorited = Auth::user()->hasFavorited($this->model);
        }
    }

    public function toggleFavorite()
    {
        if (!Auth::check()) {
            $this->dispatch('show-login-modal');
            return;
        }

        $user = Auth::user();

        if ($this->isFavorited) {
            $user->removeFromFavorites($this->model);
            $this->isFavorited = false;
            session()->flash('success', 'Removed from favorites');
        } else {
            $user->addToFavorites($this->model);
            $this->isFavorited = true;
            session()->flash('success', 'Added to favorites');
        }
    }

    public function render()
    {
        return view('livewire.favorite-button');
    }
}
