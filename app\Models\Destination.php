<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Destination extends Model
{

    protected $fillable = [
        'name',
        'country',
        'continent',
        'zone',
        'image',
    ];

    public function transferContracts(): BelongsToMany
    {
        return $this->belongsToMany(TransferContract::class, 'destination_transfer_contracts');
    }

    /**
     * Get all of the destination's favorites.
     */
    public function favorites(): MorphMany
    {
        return $this->morphMany(Favorite::class, 'favoritable');
    }

    /**
     * Get the events for the destination.
     */
    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Get the activities for the destination.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(Activity::class);
    }

    /**
     * Get the hotels for the destination.
     */
    public function hotels(): HasMany
    {
        return $this->hasMany(Hotel::class);
    }
}