<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_session_id',
        'sender',
        'message',
        'intent',
        'entities',
        'response_data',
    ];

    protected $casts = [
        'entities' => 'array',
        'response_data' => 'array',
    ];

    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', now()->subHour());
    }

    public function scopeBySession($query, $sessionId)
    {
        return $query->whereHas('chatSession', function ($q) use ($sessionId) {
            $q->where('session_id', $sessionId);
        });
    }
}
