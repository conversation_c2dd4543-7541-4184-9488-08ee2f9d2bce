<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Mail\BookingConfirmation;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email {--booking-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test booking confirmation email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $bookingId = $this->option('booking-id');

        if (!$bookingId) {
            // Get the latest booking
            $booking = Booking::with(['user', 'activity', 'activity.destination'])->latest()->first();
        } else {
            $booking = Booking::with(['user', 'activity', 'activity.destination'])->find($bookingId);
        }

        if (!$booking) {
            $this->error('No booking found.');
            return 1;
        }

        $this->info('Testing email for booking #' . $booking->id);
        $this->info('User: ' . $booking->user->name . ' (' . $booking->user->email . ')');
        $this->info('Activity: ' . $booking->activity->name);

        try {
            Mail::to($booking->user->email)->send(new BookingConfirmation($booking));
            $this->info('✅ Email sent successfully!');
            return 0;
        } catch (\Exception $e) {
            $this->error('❌ Failed to send email: ' . $e->getMessage());
            return 1;
        }
    }
}
