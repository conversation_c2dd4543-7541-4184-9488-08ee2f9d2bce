<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\ChatbotService;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class Chatbot extends Component
{
    public $message = '';
    public $chat = [];
    public $sessionId;
    public $isTyping = false;

    protected $listeners = ['destination-selected' => 'onDestinationSelected'];

    protected $chatbotService;

    public function mount()
    {
        $this->sessionId = Session::get('chatbot_session_id', $this->createNewSession());
        $this->loadChatHistory();
    }

    private function getChatbotService()
    {
        if (!$this->chatbotService) {
            $this->chatbotService = app(ChatbotService::class);
        }
        return $this->chatbotService;
    }

    private function createNewSession()
    {
        $session = ChatSession::create([
            'session_id' => uniqid('chat_', true),
            'user_id' => auth()->id(),
            'started_at' => now(),
        ]);

        Session::put('chatbot_session_id', $session->session_id);
        return $session->session_id;
    }

    private function loadChatHistory()
    {
        $session = ChatSession::where('session_id', $this->sessionId)->first();
        if (!$session) return;

        // Charger les messages de la dernière heure
        $messages = ChatMessage::where('chat_session_id', $session->id)
            ->where('created_at', '>=', now()->subHour())
            ->orderBy('created_at')
            ->get();

        $this->chat = $messages->map(function ($message) {
            return [
                'sender' => $message->sender,
                'text' => $message->message,
                'timestamp' => $message->created_at,
            ];
        })->toArray();
    }

    public function send()
    {
        if (!$this->message) return;

        $userMessage = $this->message;

        // Clear message first
        $this->message = '';

        // Add user message to chat IMMEDIATELY
        $this->chat[] = [
            'sender' => 'user',
            'text' => $userMessage,
            'timestamp' => now()
        ];

        // Force immediate UI update
        $this->dispatch('$refresh');

        // THEN dispatch typing indicator event
        $this->dispatch('message-sent');

        Log::info('Chatbot: Starting to process message', [
            'message' => $userMessage,
            'isTyping' => $this->isTyping
        ]);

        // Use JavaScript to delay processing and show typing indicator
        $this->dispatch('process-with-delay', message: $userMessage);
    }

    public function processDelayedMessage($userMessage)
    {
        try {
            // Traiter le message avec le service chatbot
            $responseData = $this->getChatbotService()->processMessage(
                $userMessage,
                $this->sessionId,
                $this->getContextHistory()
            );

            // Ajouter la réponse du bot
            $botMessage = [
                'sender' => 'bot',
                'text' => $responseData['text'],
                'timestamp' => now()
            ];

            // Ajouter les cartes de destinations si nécessaire
            if ($responseData['show_destination_cards'] && !empty($responseData['destinations'])) {
                $botMessage['show_destination_cards'] = true;
                $botMessage['destinations'] = $responseData['destinations'];
            }

            // Ajouter les recommandations si disponibles
            if (!empty($responseData['recommendations'])) {
                $botMessage['recommendations'] = $responseData['recommendations'];
            }

            $this->chat[] = $botMessage;

        } catch (\Exception $e) {
            Log::error('Chatbot error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->chat[] = [
                'sender' => 'bot',
                'text' => 'Désolé, je rencontre un problème technique. Pouvez-vous reformuler votre question ?',
                'timestamp' => now()
            ];
        }

        // Dispatch event to hide typing indicator
        $this->dispatch('message-processed');

        Log::info('Chatbot: Finished processing message', [
            'isTyping' => $this->isTyping
        ]);
    }

    private function getContextHistory()
    {
        // Retourner les 10 derniers messages pour le contexte
        return array_slice($this->chat, -10);
    }

    // Method for testing typing indicator
    public function testTyping()
    {
        $this->isTyping = !$this->isTyping;
        Log::info('Typing indicator toggled', ['isTyping' => $this->isTyping]);
    }

    public function onDestinationSelected($destinationId)
    {
        Log::info('Destination selected', ['destinationId' => $destinationId]);

        // Convertir en string si c'est un array
        if (is_array($destinationId)) {
            $destinationId = $destinationId[0] ?? $destinationId['destinationId'] ?? '';
        }

        // Trouver le nom de la destination pour l'affichage
        $destinationName = $this->findDestinationName($destinationId);
        Log::info('Destination name found', ['name' => $destinationName]);

        // Message affiché à l'utilisateur
        $userDisplayMessage = $destinationName ?
            "Je suis intéressé(e) par " . $destinationName :
            "Je suis intéressé(e) par cette destination";

        // Message traité par le chatbot (avec ID)
        $processingMessage = "Destination sélectionnée ID: " . $destinationId;
        Log::info('Processing message', ['message' => $processingMessage]);

        // Ajouter le message de sélection à l'historique
        $this->chat[] = [
            'sender' => 'user',
            'text' => $userDisplayMessage,
            'timestamp' => now()
        ];

        try {
            // Traiter avec le service chatbot
            $responseData = $this->getChatbotService()->processMessage(
                $processingMessage,
                $this->sessionId,
                $this->getContextHistory()
            );

            // Ajouter la réponse du bot
            $botMessage = [
                'sender' => 'bot',
                'text' => $responseData['text'],
                'timestamp' => now()
            ];

            // Ajouter les cartes de destinations si nécessaire
            if ($responseData['show_destination_cards'] && !empty($responseData['destinations'])) {
                $botMessage['show_destination_cards'] = true;
                $botMessage['destinations'] = $responseData['destinations'];
            }

            // Ajouter les recommandations si disponibles
            if (!empty($responseData['recommendations'])) {
                $botMessage['recommendations'] = $responseData['recommendations'];
            }

            $this->chat[] = $botMessage;

        } catch (\Exception $e) {
            Log::error('Destination selection error', [
                'destinationId' => $destinationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->chat[] = [
                'sender' => 'bot',
                'text' => 'Désolé, je ne peux pas récupérer les informations sur cette destination pour le moment.',
                'timestamp' => now()
            ];
        }

        $this->dispatch('message-processed');
    }

    private function findDestinationName($destinationId)
    {
        // Chercher dans les messages récents pour trouver le nom de la destination
        foreach (array_reverse($this->chat) as $message) {
            if ($message['sender'] === 'bot' &&
                isset($message['destinations']) &&
                is_array($message['destinations'])) {

                foreach ($message['destinations'] as $destination) {
                    if (isset($destination['id']) && $destination['id'] == $destinationId) {
                        return $destination['name'];
                    }
                }
            }
        }

        // Si pas trouvé dans l'historique, essayer de récupérer depuis la base
        try {
            $destination = \App\Models\Destination::find($destinationId);
            return $destination ? $destination->name : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function render()
    {
        return view('livewire.chatbot');
    }
}