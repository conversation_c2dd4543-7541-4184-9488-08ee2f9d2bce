<?php

namespace App\Filament\Widgets;

use App\Models\Booking;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class BookingStatusChart extends ChartWidget
{
    protected static ?string $heading = '📊 Booking Status Distribution';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '350px';

    protected function getData(): array
    {
        // Get booking counts by status
        $bookingStats = Booking::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($bookingStats as $stat) {
            $labels[] = ucfirst($stat->status);
            $data[] = $stat->count;
            
            // Assign colors based on status
            $colors[] = match($stat->status) {
                'confirmed' => '#10b981', // Green
                'pending' => '#f59e0b',   // Amber
                'cancelled' => '#ef4444', // Red
                default => '#6b7280',     // Gray
            };
        }

        // Calculate percentages
        $total = array_sum($data);
        $percentages = array_map(fn($value) => $total > 0 ? round(($value / $total) * 100, 1) : 0, $data);

        return [
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                    'hoverBorderWidth' => 3,
                ],
            ],
            'labels' => $labels,
            'percentages' => $percentages,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                    'labels' => [
                        'usePointStyle' => true,
                        'padding' => 20,
                        'font' => [
                            'size' => 12,
                        ],
                    ],
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ": " + value + " (" + percentage + "%)";
                        }'
                    ]
                ],
            ],
            'cutout' => '60%',
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }

    protected function getDescription(): ?string
    {
        $total = Booking::count();
        $confirmed = Booking::where('status', 'confirmed')->count();
        $pending = Booking::where('status', 'pending')->count();
        $cancelled = Booking::where('status', 'cancelled')->count();

        $confirmedRate = $total > 0 ? round(($confirmed / $total) * 100, 1) : 0;

        return "Total Bookings: {$total} | Confirmation Rate: {$confirmedRate}%";
    }
}
