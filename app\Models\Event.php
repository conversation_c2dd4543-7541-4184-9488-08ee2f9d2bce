<?php

namespace App\Models;

use App\Events\NewEventCreated;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'duration',
        'destination_id',
        'start_date',
        'end_date',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the destination that owns the event.
     */
    public function destination(): BelongsTo
    {
        return $this->belongsTo(Destination::class);
    }

    /**
     * Scope a query to only include active events.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include current events.
     */
    public function scopeCurrent($query)
    {
        return $query->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (Event $event) {
            // Log that an event was created
            Log::info('Event created: ' . $event->name . ' for destination: ' . $event->destination_id);

            // Fire the NewEventCreated event when a new event is created
            NewEventCreated::dispatch($event);

            Log::info('NewEventCreated event dispatched for event: ' . $event->id);
        });
    }
}
