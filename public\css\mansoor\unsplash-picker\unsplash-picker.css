.aspect-square{aspect-ratio:1/1}.columns-3{-moz-columns:3;column-count:3}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity))}.bg-gray-900{--tw-bg-opacity:1;background-color:rgba(var(--gray-900),var(--tw-bg-opacity))}.bg-opacity-60{--tw-bg-opacity:0.6}.opacity-25{opacity:.25}.opacity-75{opacity:.75}.opacity-80{opacity:.8}.hover\:ring-2:hover{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.hover\:ring-primary-600:hover{--tw-ring-opacity:1;--tw-ring-color:rgba(var(--primary-600),var(--tw-ring-opacity))}.hover\:ring-offset-2:hover{--tw-ring-offset-width:2px}.group:hover .group-hover\:flex{display:flex}:is(.dark .dark\:ring-white\/20){--tw-ring-color:#fff3}:is(.dark .dark\:hover\:ring-primary-500:hover){--tw-ring-opacity:1;--tw-ring-color:rgba(var(--primary-500),var(--tw-ring-opacity))}:is(.dark .dark\:hover\:ring-offset-black:hover){--tw-ring-offset-color:#000}@media (min-width:1024px){.lg\:columns-4{-moz-columns:4;column-count:4}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}}