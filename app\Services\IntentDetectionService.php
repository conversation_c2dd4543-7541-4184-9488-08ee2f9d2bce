<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class IntentDetectionService
{
    protected $geminiService;

    public function __construct(GeminiService $geminiService)
    {
        $this->geminiService = $geminiService;
    }

    public function detectIntentAndEntities(string $message, array $contextHistory = []): array
    {
        try {
            // Préprocessing du message
            $cleanedMessage = $this->preprocessMessage($message);

            // Détection via Gemini
            $result = $this->geminiService->detectIntentAndEntities($cleanedMessage, $contextHistory);

            // Post-processing et validation
            return $this->validateAndEnhanceResult($result, $cleanedMessage, $contextHistory);

        } catch (\Exception $e) {
            Log::error('Intent detection failed', [
                'message' => $message,
                'error' => $e->getMessage()
            ]);

            // Fallback: détection basique par mots-clés
            return $this->fallbackDetection($message);
        }
    }

    private function preprocessMessage(string $message): string
    {
        // Correction des fautes d'orthographe courantes
        $corrections = [
            'otel' => 'hôtel',
            'otels' => 'hôtels',
            'activiter' => 'activité',
            'activites' => 'activités',
            'transfere' => 'transfert',
            'transferes' => 'transferts',
            'voitur' => 'voiture',
            'minivane' => 'minivan',
            'aeropor' => 'aéroport',
            'aeroport' => 'aéroport',
        ];

        $message = str_ireplace(array_keys($corrections), array_values($corrections), $message);

        return trim($message);
    }

    private function validateAndEnhanceResult(array $result, string $message, array $contextHistory): array
    {
        // Validation de l'intention
        $validIntents = ['voir_hotel', 'voir_activité', 'voir_transfert', 'voir_destination', 'autre'];
        if (!in_array($result['intent'], $validIntents)) {
            $result['intent'] = 'autre';
        }

        // Amélioration des entités avec le contexte
        $result['entities'] = $this->enhanceEntitiesWithContext($result['entities'], $contextHistory);

        // Validation des entités
        $result['entities'] = $this->validateEntities($result['entities']);

        return $result;
    }

    private function enhanceEntitiesWithContext(array $entities, array $contextHistory): array
    {
        // Si pas de location dans les entités, chercher dans le contexte
        if (empty($entities['location']) && !empty($contextHistory)) {
            foreach (array_reverse($contextHistory) as $msg) {
                if ($msg['sender'] === 'user') {
                    $contextLocation = $this->extractLocationFromText($msg['text']);
                    if ($contextLocation) {
                        $entities['location'] = $contextLocation;
                        break;
                    }
                }
            }
        }

        return $entities;
    }

    private function extractLocationFromText(string $text): ?string
    {
        return $this->extractDestinationFromText($text);
    }

    private function extractDestinationFromText(string $text): ?string
    {
        // Mots à exclure (ne sont pas des destinations)
        $excludedWords = ['id', 'avec', 'destination', 'informations', 'sur', 'la', 'le', 'les', 'des', 'du', 'de'];

        // Liste étendue des destinations courantes
        $destinations = [
            // France
            'paris', 'lyon', 'marseille', 'nice', 'toulouse', 'bordeaux', 'lille', 'nantes', 'strasbourg', 'montpellier',
            'cannes', 'antibes', 'saint-tropez', 'avignon', 'aix-en-provence', 'annecy', 'chamonix',

            // Maroc
            'marrakech', 'casablanca', 'rabat', 'fès', 'agadir', 'tanger', 'meknès', 'ouarzazate', 'essaouira',

            // Tunisie
            'tunis', 'sousse', 'hammamet', 'djerba', 'monastir', 'sfax', 'kairouan', 'tozeur',

            // Italie
            'rome', 'milan', 'venise', 'florence', 'naples', 'turin', 'bologne', 'palerme', 'catane',
            'amalfi', 'cinque terre', 'lac de côme', 'toscane', 'sicile',

            // Espagne
            'madrid', 'barcelone', 'séville', 'valence', 'bilbao', 'grenade', 'cordoue', 'tolède',
            'saint-sébastien', 'salamanque', 'cadix', 'majorque', 'ibiza', 'canaries',

            // Portugal
            'lisbonne', 'porto', 'faro', 'coimbra', 'braga', 'aveiro', 'óbidos', 'sintra',

            // Royaume-Uni
            'londres', 'manchester', 'liverpool', 'birmingham', 'édimbourg', 'glasgow', 'cardiff', 'belfast',

            // Allemagne
            'berlin', 'munich', 'hambourg', 'cologne', 'francfort', 'stuttgart', 'düsseldorf', 'dresde',

            // Pays-Bas
            'amsterdam', 'rotterdam', 'la haye', 'utrecht', 'eindhoven', 'groningue',

            // Belgique
            'bruxelles', 'anvers', 'gand', 'bruges', 'liège', 'namur',

            // Grèce
            'athènes', 'thessalonique', 'patras', 'héraklion', 'santorin', 'mykonos', 'rhodes', 'corfou',

            // Turquie
            'istanbul', 'ankara', 'izmir', 'antalya', 'bodrum', 'cappadoce', 'pamukkale',

            // Autres destinations populaires
            'new york', 'tokyo', 'dubaï', 'bangkok', 'singapour', 'hong kong', 'sydney', 'melbourne',
            'los angeles', 'san francisco', 'chicago', 'miami', 'las vegas',
            'rio de janeiro', 'buenos aires', 'lima', 'mexico', 'cancun',
            'le caire', 'marrakesh', 'cape town', 'nairobi',
            'mumbai', 'delhi', 'goa', 'kerala', 'rajasthan',
            'pékin', 'shanghai', 'xi\'an', 'guilin',
            'séoul', 'busan', 'jeju',
            'moscou', 'saint-pétersbourg',
            'reykjavik', 'oslo', 'stockholm', 'copenhague', 'helsinki',
            'vienne', 'prague', 'budapest', 'cracovie', 'varsovie',
            'zurich', 'genève', 'berne', 'lucerne'
        ];

        $text = strtolower($text);

        // Recherche des destinations dans le texte
        foreach ($destinations as $destination) {
            if (strpos($text, $destination) !== false) {
                return ucwords($destination);
            }
        }

        // Recherche de patterns plus complexes
        if (preg_match('/\b(?:à|en|vers|pour|dans)\s+([a-záàâäéèêëíìîïóòôöúùûüç\s-]+)\b/i', $text, $matches)) {
            $potentialDestination = trim(strtolower($matches[1]));

            // Vérifier que ce n'est pas un mot exclu
            if (!in_array($potentialDestination, $excludedWords) &&
                strlen($potentialDestination) > 2 &&
                strlen($potentialDestination) < 30) {
                return ucwords($potentialDestination);
            }
        }

        return null;
    }

    private function validateEntities(array $entities): array
    {
        $validated = [];

        // Validation de la location
        if (!empty($entities['location'])) {
            $validated['location'] = ucfirst(strtolower(trim($entities['location'])));
        }

        // Validation du prix
        if (!empty($entities['prix'])) {
            $validated['prix'] = $this->normalizePriceEntity($entities['prix']);
        }

        // Validation des autres entités
        foreach (['nom_hotel', 'nom_activité', 'type_activité', 'transfert_name', 'vehicule_type', 'nom_destination'] as $key) {
            if (!empty($entities[$key])) {
                $value = trim($entities[$key]);
                // Exclure les mots qui ne sont pas des destinations valides
                if ($key === 'nom_destination' && in_array(strtolower($value), ['id', 'destination', 'informations'])) {
                    continue;
                }
                $validated[$key] = $value;
            }
        }

        // Validation spéciale pour destination_id
        if (!empty($entities['destination_id'])) {
            $validated['destination_id'] = trim($entities['destination_id']);
        }

        return $validated;
    }

    private function normalizePriceEntity(string $price): string
    {
        // Normaliser les expressions de prix
        $price = strtolower(trim($price));

        // Remplacer les expressions courantes
        $price = str_replace(['euros', '€', 'euro'], '€', $price);
        $price = str_replace(['moins de', 'moins que', 'max'], 'moins de', $price);
        $price = str_replace(['plus de', 'plus que', 'min'], 'plus de', $price);

        return $price;
    }

    private function fallbackDetection(string $message): array
    {
        $message = strtolower($message);
        $intent = 'autre';
        $entities = [];

        // Détection basique par mots-clés
        if (preg_match('/\b(destination|voyage|partir|aller|visiter|explorer|découvrir|pays|ville|endroit|lieu|où aller|suggestions|recommandations|conseils voyage)\b/', $message)) {
            $intent = 'voir_destination';
        } elseif (preg_match('/\b(hôtel|hotel|hébergement|dormir|chambre)\b/', $message)) {
            $intent = 'voir_hotel';
        } elseif (preg_match('/\b(activité|activités|visite|excursion|attraction|musée|spa)\b/', $message)) {
            $intent = 'voir_activité';
        } elseif (preg_match('/\b(transfert|transport|voiture|minivan|bus|aéroport)\b/', $message)) {
            $intent = 'voir_transfert';
        }

        // Extraction basique de location
        $location = $this->extractLocationFromText($message);
        if ($location) {
            $entities['location'] = $location;
        }

        // Extraction du nom de destination pour l'intention voir_destination
        if ($intent === 'voir_destination') {
            // Extraire l'ID de destination si présent (pour les sélections)
            if (preg_match('/(?:destination\s+sélectionnée\s+)?ID:\s*(\d+)/i', $message, $matches)) {
                $entities['destination_id'] = $matches[1];
                Log::info('ID de destination extrait', ['id' => $entities['destination_id'], 'message' => $message]);
            } else {
                // Sinon, extraire le nom de destination
                $destinationName = $this->extractDestinationFromText($message);
                if ($destinationName && !in_array(strtolower($destinationName), ['id', 'destination', 'informations'])) {
                    $entities['nom_destination'] = $destinationName;
                }
                Log::info('Nom de destination extrait', ['nom' => $destinationName ?? 'aucun', 'message' => $message]);
            }
        }

        return [
            'intent' => $intent,
            'entities' => $entities,
            'confidence' => 0.6
        ];
    }
}
