<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - Travel Shaper</title>
    <style>
        @import url('https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            border: 1px solid #fce7f3;
        }
        
        .header {
            background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            color: #ffffff;
            font-size: 28px;
            font-weight: 800;
            letter-spacing: -0.5px;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .tagline {
            color: #fce7f3;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            box-shadow: 0 10px 15px -3px rgba(236, 72, 153, 0.3), 0 4px 6px -2px rgba(236, 72, 153, 0.1);
            transition: all 0.3s ease;
            margin: 20px 0;
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 20px -3px rgba(236, 72, 153, 0.4), 0 6px 8px -2px rgba(236, 72, 153, 0.15);
        }
        
        .security-notice {
            background: #fef3f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            margin: 30px 0;
        }
        
        .security-notice .icon {
            color: #dc2626;
            font-size: 18px;
            margin-right: 8px;
        }
        
        .security-notice .text {
            color: #7f1d1d;
            font-size: 14px;
            font-weight: 500;
        }
        
        .footer {
            background: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #f3f4f6;
        }
        
        .footer-text {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #ec4899;
            text-decoration: none;
            font-size: 20px;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #ec4899, transparent);
            margin: 30px 0;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 20px;
            }
            
            .logo {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">TRAVEL<br>SHAPER</div>
            <div class="tagline">Your Journey, Perfectly Shaped</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ $notifiable->name ?? 'Traveler' }}! 👋</div>
            
            <div class="message">
                We received a request to reset your password for your Travel Shaper account. 
                Don't worry, it happens to the best of us! Click the button below to create a new password.
            </div>
            
            <div style="text-align: center;">
                <a href="{{ $actionUrl }}" class="reset-button">
                    🔐 Reset My Password
                </a>
            </div>
            
            <div class="security-notice">
                <div style="display: flex; align-items: center;">
                    <span class="icon">⚠️</span>
                    <span class="text">
                        <strong>Security Notice:</strong> This link will expire in 60 minutes for your security.
                    </span>
                </div>
            </div>
            
            <div class="divider"></div>
            
            <div class="message">
                If you didn't request this password reset, please ignore this email. 
                Your account remains secure and no changes have been made.
            </div>
            
            <div class="message" style="font-size: 14px; color: #6b7280;">
                <strong>Having trouble with the button?</strong> Copy and paste this link into your browser:<br>
                <span style="word-break: break-all; color: #ec4899;">{{ $actionUrl }}</span>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                <strong>Travel Shaper Team</strong><br>
                Making your travel dreams come true ✈️
            </div>
            
            <div class="social-links">
                <a href="#" title="Facebook">📘</a>
                <a href="#" title="Twitter">🐦</a>
                <a href="#" title="Instagram">📷</a>
                <a href="#" title="LinkedIn">💼</a>
            </div>
            
            <div class="footer-text" style="font-size: 12px;">
                © {{ date('Y') }} Travel Shaper. All rights reserved.<br>
                This email was sent to {{ $notifiable->email }}
            </div>
        </div>
    </div>
</body>
</html>
