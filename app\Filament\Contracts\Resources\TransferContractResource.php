<?php

namespace App\Filament\Contracts\Resources;

use App\Filament\Contracts\Resources\TransferContractResource\Pages;
use App\Filament\Contracts\Resources\TransferContractResource\RelationManagers;
use App\Models\TransferContract;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;

use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\DatePicker;

use App\Enums\ContractStatus;
use App\Enums\Currency;
use App\Models\MeetingPoint;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Doctrine\DBAL\Schema\Schema;
use Filament\Actions\Action as ActionsAction;

use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Get;

class TransferContractResource extends Resource
{
    protected static ?string $model = TransferContract::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Transfer Details')
                    ->schema([
                        Tabs::make('Tabs')
                            ->columnSpan('full')
                            ->tabs([
                                Tabs\Tab::make('Information')
                                    ->columns(3)
                                    ->schema([
                                        TextInput::make('contract_number')->label('Contract Number')->required()->columnSpan(2),
                                        Forms\Components\Select::make('type')
                                            ->required()
                                            ->options([
                                                'FIT' => 'FIT',
                                                'Dynamic Rates' => 'Dynamic Rates',
                                                'Group Rate Plan' => 'Group Rate Plan',
                                                'Package Rate Plan' => 'Package Rate Plan',
                                                'Fly & Drive Rate Plan' => 'Fly & Drive Rate Plan',
                                            ])
                                            ->placeholder('Sélectionnez un type'),

                                            Forms\Components\Select::make('main_supplier')
                                            ->required()
                                            ->options([
                                                'test1' => 'test1',
                                                'test2' => 'test2',
                                            ])
                                            ->placeholder('Sélectionnez une option'),

                                        Forms\Components\Toggle::make('direct_supplier')
                                            ->live()
                                            ->default(true),
                                        Grid::make(2)->schema([

                                            Forms\Components\Select::make('intermediate_supplier')

                                                ->options([
                                                    'test1' => 'test1',
                                                    'test2' => 'test2',
                                                ])

                                                ->placeholder('Sélectionnez un point'),
                                        ])->hidden(function (Get $get) {
                                            return $get('direct_supplier') ? true : false;
                                        }),

                                        Forms\Components\Textarea::make('description')
                                            ->required()
                                            ->columnSpanFull(),
                                            Select::make('transfers')
                                            ->searchable()
                                            ->preload()
                                            ->live()
                                            ->native(false)
                                            ->multiple()
                                            ->relationship('transfers', "name"),
                                        Forms\Components\Select::make('markets')
                                            ->label('Market(s)')
                                            ->multiple()
                                            ->relationship('markets', 'name')
                                            ->preload()
                                            ->columnSpanFull()
                                            ->searchable()
                                            ->required(),

                                        Section::make("what's included / what'is not included")
                                            ->schema([
                                                Repeater::make('whats_included')

                                                    ->simple(

                                                        Forms\Components\Select::make('whats_included')
                                                            ->options([
                                                                'Ambiance musicale' => 'Ambiance musicale',
                                                                'Tables avec vue panoramique' => 'Tables avec vue panoramique',
                                                                'Prise de photos autorisée' => 'Prise de photos autorisée',
                                                                'nouveau incluede services' => 'nouveau incluede services',
                                                                'Nourriture et boissons' => 'Nourriture et boissons',
                                                                'Commentaire audio depuis votre appareil mobile' => 'Commentaire audio depuis votre appareil mobile',
                                                                'Spectacles' => 'Spectacles',
                                                                'Zone de selfies avec les personnages' => 'Zone de selfies avec les personnages',
                                                                'Sièges extérieurs et intérieurs' => 'Sièges extérieurs et intérieurs',
                                                            ]),

                                                    )->columnSpan(2),


                                                Repeater::make('whats_not_included')
                                                    ->simple(
                                                        Forms\Components\Select::make('whats_not_included')
                                                            ->options([
                                                                'Les animaux ne sont pas acceptés' => 'Les animaux ne sont pas acceptés',
                                                                'Deposit amount must be paid on site and is not linked to the use of the Application' => 'Deposit amount must be paid on site and is not linked to the use of the Application',
                                                                'Nourriture et boissons' => 'Nourriture et boissons',
                                                                'Prise en charge et retour à l’hôtel' => 'Prise en charge et retour à l’hôtel',
                                                            ])
                                                    )->columnSpan(2)

                                            ])
                                    ]),
                                    Tabs\Tab::make('Price')
                                    ->schema(function (Get $get) {
                                        $transfers = $get('transfers') ?? [];
                                
                                        if (!is_array($transfers)) {
                                            return [];
                                        }
                                
                                        $repeaterTransfers = [];
                                
                                        foreach ($transfers as $transferId) {
                                            $transfer = \App\Models\Transfer::find($transferId);
                                            if (!$transfer) continue;
                                
                                            $repeaterTransfers[] = Section::make($transfer->name)
                                                ->schema([
                                                    Placeholder::make("transfer_info_{$transferId}")
                                                        ->label('Selected Transfer')
                                                        ->content($transfer->name)
                                                        ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                                
                                                    TableRepeater::make("price_{$transferId}")


                                            ->columns(2)
                                            ->headers([
                                                Header::make('Start Date'),

                                                Header::make('End Date'),
                                                
                                                Header::make('Transfer Details'),
                                                Header::make('Actions'),
                                            ])
                                            ->schema([
                                                DatePicker::make('start_date')->label('Start Date'),
                                                DatePicker::make('end_date')->label('End Date'),
                                                Actions::make([
                                                Action::make('transfer_details')
                                                ->label('Transfer Details')
                                                ->icon('heroicon-o-information-circle')
                                                ->modalHeading('Transfer Details')
                                                //->modalSubmitAction(false) // No submit button
                                                ->modalCancelActionLabel('Close')
                                                /*->mountUsing(function ($record, $component) {
                                                    $component->fillForm([
                                                        
                                                        'description' => $record->activity->description,
                                                        'type' => $record->activity->activity_type,
                                                        'nature' => $record->activity->activity_nature,
                                                        'difficulty' => $record->activity->difficulty_level,
                                                    ]);
                                                })*/
                                                ->form([
                                                    TableRepeater::make('trips')
                                                    ->headers([
                                                        Header::make('Trip'),
                                                
                                                        Header::make('Price Trip'),
                                                        Header::make('Actions'),
                                                        
                                                    ])
                                                    ->schema([
                                                        Placeholder::make("transfer_info_{$transferId}")
                                                        
                                                        ->content($transfer->name)
                                                        ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                                                        Actions::make([
                                                Action::make('set_prices')
                                                ->label('Set Prices')
                                                ->icon('heroicon-o-pencil')
                                                ->modalHeading('Transfer Details')
                                                ->modalWidth('full')
                                                
                                                ->modalCancelActionLabel('Close')
                                                
                                                ->form([
                                                    
                                                    Forms\Components\Select::make('type_price')->label('Type Price')
                                                    ->required()
                                                    ->options([
                                                        'km' => 'KM',
                                                        'vehicle' => 'Vehicle',
                                                        'number_of_passengers' => 'Number Of Passengers (Pax)',
                                                        
                                                    ])->columns(2),
                                                    Forms\Components\Select::make('route')->label('Route')
                                                    ->required()
                                                    ->options([
                                                        'one_way' => 'One Way',
                                                        'round_trips' => 'Round Trips',
                                                        
                                                        
                                                    ])->columns(2),

                                                    Forms\Components\Select::make('currency')->label('Currency')
                                                        ->options(Currency::class)
                                                        ->columns(2),

                                                    Section::make('pick_up_Location')
                                                    ->label('Pick Up Location')
                                                    ->Schema([
                                                    Forms\Components\Select::make('destination_id')
                                                        ->label('Destination')
                                                        ->relationship('destinations', 'name')
                                                        ->searchable()
                                                        ->multiple()
                                                        ->placeholder('Sélectionnez un point')
                                                        ->preload()
                                                        ->createOptionForm([
                                                            TextInput::make('name'),
                                                            TextInput::make('country'),
                                                            TextInput::make('continent'),
                                                            TextInput::make('zone'),
                                                        ]),
                                                        
                                                        Forms\Components\Select::make('meeting_point_id')
                                                            ->label('Meeting Point')
                                                            ->relationship('meetingPoints', 'name')
                                                            ->searchable()
                                                            ->preload(),
                                                    
                                                    ]),
                                                    Section::make('drop_of_location')
                                                    ->label('Drop Of Location')
                                                    ->Schema([
                                                        Forms\Components\Select::make('drop_destination_id')
                                                        ->label('Destination')
                                                        ->relationship('destinations', 'name')
                                                        ->searchable()
                                                        ->multiple()
                                                        ->placeholder('Sélectionnez un point')
                                                        ->preload()
                                                        ->createOptionForm([
                                                            TextInput::make('name'),
                                                            TextInput::make('country'),
                                                            TextInput::make('continent'),
                                                            TextInput::make('zone'),
                                                        ]),
                                                    Forms\Components\Select::make('drop_of_points')
                                                        ->label('Drop Of Points')
                                                        ->options(MeetingPoint::pluck('name','id'))
                                                        ->multiple()            
                                                        ->searchable()
                                                        ->preload(),

                                                    ]),
                                                    Section::make('estimated')
                                                    ->label('Estimated')
                                                    ->Schema([
                                                        Forms\Components\TextInput::make('estimated_days')
                                                        ->label('Days')
                                                        ->numeric(),
                                                        
                                                        Forms\Components\TextInput::make('estimated_hours')
                                                        ->label('Hours')
                                                        ->numeric(),

                                                        Forms\Components\TextInput::make('estimated_minutes')
                                                        ->label('Minutes')
                                                        ->numeric(),

                                                    ]),
                                                    
                                                    Section::make('rates')
                                                    ->label('Rates')
                                                    ->Schema([
                                                        Forms\Components\TextInput::make('price')
                                                        ->label('Price')
                                                        ->numeric(),
                                                    ])
                                                                                
                                                    
                                                    
                                                
                                                
                                                
                                                
                                              
                                                ])
                                            ])
                                                
                                                        ])
                                                    
                                                    
                                                
                                                
                                                // Add more fields if needed
                                                ])

                                                ])->columns(6),
                                            ])->columns(6)


                                            ]);
                                }
                                    
                                return $repeaterTransfers;
                            }),
                                
                            Tabs\Tab::make('Policies')
                            ->schema(function (Get $get) {
                                $transfers = $get('transfers') ?? [];
                        
                                if (!is_array($transfers)) {
                                    return [];
                                }
                        
                                $repeaterTransfers = [];
                        
                                foreach ($transfers as $transferId) {
                                    $transfer = \App\Models\Transfer::find($transferId);
                                    if (!$transfer) continue;
                        
                                    $repeaterTransfers[] = Section::make($transfer->name)
                                        ->schema([
                                            Placeholder::make("transfer_info_{$transferId}")
                                                ->label('Selected Transfer')
                                                ->content($transfer->name)
                                                ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                        
                                            TableRepeater::make("policy_{$transferId}")


                                            ->columns(2)
                                            ->headers([
                                                Header::make('Period Start'),

                                                Header::make('Period End'),
                                                Header::make('Policies'),
                                                Header::make('Show Policies Details'),
                                                
                                                Header::make('Actions'),
                                            ])
                                            ->schema([
                                                DatePicker::make('period_start')->label('Period Start'),
                                                DatePicker::make('period_end')->label('Period End'),
                                                TextInput::make('pol'),    
                                                Actions::make([
                                                Action::make('show_policies_details')
                                                ->label('Show Policies Details')
                                                ->icon('heroicon-o-information-circle')
                                                ->modalHeading('Policies Details')
                                                
                                                ->modalCancelActionLabel('Close')
                                                
                                                ->form([
                                                    section::make(null)
                                                    ->Schema([
                                                    section::make('Policy')
                                                    ->Schema([
                                                    
                                                    
                                                    Forms\Components\Select::make('types_policies')->label('Types Policies')
                                                    ->multiple()
                                                    ->required()
                                                    ->options([
                                                        'cancelation' => 'Cancelation',
                                                        'modification' => 'Modification',
                                                        'no_show' => 'No Show',
                                                    ]),
                                                    Forms\Components\TextInput::make('days_before_activity')->numeric()->required()->label('Days before Activity'),
                                                    Forms\Components\TextInput::make('hours_before_activity')->numeric()->required()->label('Hours before Activity'),
                                                    Forms\Components\Select::make('charge_type')->label('Charge Type')
                                                    ->live()
                                                    ->required()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Flat',
                                                        'none_refundable' => 'None Refundable',
                                                        'free' => 'Free',
                                                    ]),
                                                    Forms\Components\TextInput::make('percentage_rate')
                                                                ->numeric()
                                                                ->label('Percentage')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('charge_type') === 'percentage'),
                                                                
                                                            Forms\Components\TextInput::make('reduction')
                                                                ->numeric()
                                                                ->label('Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('charge_type') === 'flat'),
                                                                
                                                            Forms\Components\Select::make('currency')
                                                                ->label('Currency')
                                                                ->options(Currency::class)
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('charge_type') === 'flat'),

                                                ])->columnSpan(1),
                                                    
                                                    section::make('Policy Conditions')
                                                    ->Schema([
                                                    Forms\Components\Select::make('types_organization')->label('Types organization of conditions (AND/OR)')                       
                                                        ->required()
                                                        ->options([
                                                            'and' => 'And',
                                                            'or' => 'OR',
                                                        
                                                        ]),


                                                        Forms\Components\Select::make('condition_type')->label('Condition Type')                       
                                                        ->required()
                                                        ->options([
                                                            'payment_type' => 'Payment Type',                                                            
                                                        ]),

                                                        Forms\Components\Select::make('comparison_type')->label('Comparison Type)')                       
                                                        ->required()
                                                        ->options([
                                                            'equal' => 'Equal',
                                                            'not_equal' => 'Not Equal',
                                                        
                                                        ]),

                                                        Forms\Components\Select::make('value')->label('Value')                       
                                                        ->required()
                                                        ->options([
                                                            'total_payment' => 'Total Payment',
                                                            'deposit' => 'Deposit',
                                                        
                                                        ]),
                                                    ])->columnSpan(1)
                                                    ])->columns(2),
                                                
                                                
                                                // Add more fields if needed
                                                ])
                                                
                                                ->action(function($record,$data,Get $get){
                                                    
                                                    $record->policies=[
                                                        
                                                        'period_start'=>$get('period_start'),
                                                        'period_end'=>$get('period_end'),
                                                        'show_policies_details'=>[
                                                            
                                                            
                                                            'types_policies'=>$data['types_policies'],
                                                            'days_before_activity'=>$data['days_before_activity'],
                                                            'hours_before_activity'=>$data['hours_before_activity'],
                                                            'charge_type' => $data['charge_type'],
                                                            'charge_type' => $data['charge_type'] === 'Percentage' ? $data['value_percentage'] : null,
                                                            'charge_type' => $data['charge_type'] === 'Flat'? [
                                                                'flat_value' => $data['flat_value'],
                                                                'currency' => $data['currency'],
                                                            ]: null,        
                                                            'types_organization'=>$data['types_organization'],
                                                            'condition_type'=>$data['condition_type'],
                                                            'comparison_type'=>$data['comparison_type'],
                                                            'value'=>$data['value'],
                                                            
                                                            
                                                            ]
                                                    ];
                                                    $record->save();
                                                }),

                                                ]),
                                            ])


                                            ]);
                                }
                                
                                 return $repeaterTransfers;
                                }),
                                Tabs\Tab::make('Supplement & special offers')
                                ->schema(function (Get $get) {
                                    $transfers = $get('transfers') ?? [];
                            
                                    if (!is_array($transfers)) {
                                        return [];
                                    }
                            
                                    $repeaterTransfers = [];
                            
                                    foreach ($transfers as $transferId) {
                                        $transfer = \App\Models\Transfer::find($transferId);
                                        if (!$transfer) continue;
                            
                                        $repeaterTransfers[] = Section::make($transfer->name)
                                            ->schema([
                                                Placeholder::make("transfer_info_{$transferId}")
                                                    ->label('Selected Transfer')
                                                    ->content($transfer->name)
                                                    ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                            
                                                TableRepeater::make("supplemets_and_special_offers_{$transferId}")


                                        ->columns(2)
                                        ->headers([
                                            Header::make('Special offer'),
                                            Header::make('Period Start'),
                                            Header::make('Period End'),
                                            Header::make('Show Periods'),
                                            
                                            Header::make('Actions'),
                                        ])
                                        ->schema([
                                            TextInput::make('special_offer'),
                                            DatePicker::make('period_start')->label('Period Start'),
                                            DatePicker::make('period_end')->label('Period End'),
                                                
                                            Actions::make([
                                            Action::make('show_offer_details')
                                            ->label('Show Offer Details')
                                            ->icon('heroicon-o-information-circle')
                                            ->modalHeading('Offer Details')
                                            
                                            ->modalCancelActionLabel('Close')
                                            
                                            ->form([
                                                section::make(null)
                                                ->Schema([
                                                
                                                
                                                    Repeater::make('offers')
                                                    ->schema([
                                                Forms\Components\Select::make('type_pax')->label('Type Pax')
                                                
                                                ->required()
                                                ->columnSpan(1)
                                                ->options([
                                                    'adult' => 'Adult',
                                                    'child' => 'Child',
                                                    'infant' => 'Infant',
                                                ]),
                                                Forms\Components\Select::make('level')->label('Level')
                                                ->required()
                                                ->columnSpan(1)
                                                ->options([
                                                    'all' => 'All',
                                                    'first' => 'First',
                                                    'second' => 'Second',
                                                    'third' => 'Third',
                                                    'fourth' => 'Fourth',
                                                ]),
                                                Forms\Components\Select::make('type_reduction')
                                                                ->label('Type Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->options([
                                                                    'flat' => 'Flat',
                                                                    'percentage' => '%',
                                                                ])
                                                                ->live(), // Add this to trigger reactivity
                                                                
                                                            Forms\Components\TextInput::make('percentage_rate')
                                                                ->numeric()
                                                                ->label('Percentage')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'percentage'),
                                                                
                                                            Forms\Components\TextInput::make('reduction')
                                                                ->numeric()
                                                                ->label('Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'flat'),
                                                                
                                                            Forms\Components\Select::make('currency')
                                                                ->label('Currency')
                                                                ->options(Currency::class)
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'flat'),
                                                                
                                                                ])->columns(4),
                                                        Repeater::make('conditions')
                                                        ->schema([
                                                            Forms\Components\Select::make('condition_type')->label('Condition Type')                       
                                                            ->required()
                                                            ->columnSpan(3)
                                                            ->options([
                                                                'travel_date' => 'Travel Date',                                                            
                                                                'booking_date' => 'Booking Date',                                                            
                                                                'pick_up_time' => 'Pick up time',                                                            
                                                                'drop_off_time' => 'Drop off time ',                                                            
                                                            ]),

                                                            Forms\Components\Select::make('comparison_type')
                                                                ->label('Comparison Type')
                                                                ->required()
                                                                ->columnSpan(2)
                                                                ->options([
                                                                    'before' => 'Before',
                                                                    'after' => 'After',
                                                                    'equal' => 'Equal',
                                                                    'not_equal' => 'Not Equal',
                                                                    'between' => 'Between',
                                                                    'not_between' => 'Not Between',
                                                                ])
                                                                ->live(),
                                                                
                                                            DatePicker::make('value')
                                                                ->label('Value')
                                                                ->required()
                                                                ->columnSpan(1),
                                                                
                                                            DatePicker::make('second_value')
                                                                ->label('Second Value')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) =>
                                                                    in_array($get('comparison_type'), ['between', 'not_between'])
                                                                ),

                                                            
                                                            ])->columns(4)
                                                

                                            ]),
                                                
                                               
                                               
                                                
                                            
                                            
                                            // Add more fields if needed
                                            ])
                                            
                                            ->action(function($record, $data, Get $get) {
                                                $chargeType = $data['charge_type'] ?? null;
                                            
                                                $chargeDetails = match ($chargeType) {
                                                    'Percentage' => ['value_percentage' => $data['value_percentage']],
                                                    'Flat' => [
                                                        'flat_value' => $data['flat_value'],
                                                        'currency' => $data['currency'],
                                                    ],
                                                    default => $chargeType, // just the string if "None Refundable" or "Free"
                                                };
                                            
                                                $record->supplements_and_special_offers = [
                                                    'special_offer' => $get('special_offer'),
                                                    'period_start' => $get('period_start'),
                                                    'period_end' => $get('period_end'),
                                                    'offers' => [
                                                        'type_pax' => $data['type_pax'] ,
                                                        'level' => $data['level'] ,
                                                        'charge_type' => $chargeDetails,
                                                        'types_organization' => $data['types_organization'] ?? null,
                                                        'condition_type' => $data['condition_type'] ?? null,
                                                        'comparison_type' => $data['comparison_type'] ?? null,
                                                        'value' => $data['value'] ?? null,
                                                    ],
                                                ];
                                                
                                                $record->save();
                                            }),

                                            ]),
                                        ])


                                        ]);
                                    }
                                    
                                    return $repeaterTransfers;
                                }),
                                Tabs\Tab::make('Channels')
                                    ->schema([
                                        Section::make("Channels contract")
                                            ->schema([
                                                Repeater::make('channels contract')
                                                    ->schema([
                                                        Forms\Components\Select::make('channels')
                                                            ->required()
                                                            ->options([
                                                                'b2b_europe' => 'B2B Europe',
                                                                'b2b2c' => 'B2B2C',
                                                            ])->columnSpan(1)
                                                            ->placeholder('Sélectionnez une option'),

                                                        Forms\Components\Select::make('commission')
                                                            ->required()
                                                            ->options([
                                                                'net' => 'NET',
                                                                'commisionnable' => 'Commisionnable',
                                                            ])->columnSpan(1)
                                                            ->placeholder('Sélectionnez une option'),
                                                    ])->columns(2),
                                            ])->columnSpanFull()

                                    ]),
                            ])
                    ])->columns(3)
                    ->columnSpan(3),

                // Second section: Radio Buttons and Date Picker (Top-Right Position)
                Section::make('Settings')
                    ->schema([
                        // Radio Button for contract status
                        ToggleButtons::make('contract_status')
                            ->label('Contract Status')
                            ->options(ContractStatus::class)
                            ->default('signed')

                            ->required(),

                        // Date Picker for contract date
                        DatePicker::make('signed_date')
                            ->label('Signed Date')

                            ->default(now())
                            ->displayFormat('Y-m-d'),

                        DatePicker::make('start_date')
                            ->label('Start Date')
                            ->required()
                            ->default(now())
                            ->displayFormat('Y-m-d')
                            ->minDate(fn (callable $get) => $get('signed_date')),

                        DatePicker::make('end_date')
                            ->label('End Date')
                            ->required()
                            ->default(now())
                            ->displayFormat('Y-m-d')
                            ->minDate(fn (callable $get) => $get('start_date')),

                            
                    ])
                    ->grow(false) // Prevents it from growing, keeps it compact
                    ->columnSpan(1), // This section takes up 1 column
            ])->columns(4);
            
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('contract_number'),

                Tables\Columns\TextColumn::make('description')->searchable(),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('signed_date'),
                Tables\Columns\TextColumn::make('start_date'),
                Tables\Columns\TextColumn::make('end_date'),
                Tables\Columns\TextColumn::make('contract_status')->badge(),


                Tables\Columns\TextColumn::make('markets.name')->badge(),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransferContracts::route('/'),
            'create' => Pages\CreateTransferContract::route('/create'),
            'edit' => Pages\EditTransferContract::route('/{record}/edit'),
        ];
    }
}