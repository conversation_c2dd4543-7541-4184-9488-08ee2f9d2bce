<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransferResource\Pages;
use App\Models\Transfer;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;


use Mansoor\UnsplashPicker\Actions\UnsplashPickerAction;


class TransferResource extends Resource
{
    protected static ?string $model = Transfer::class;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->required(),
                Forms\Components\FileUpload::make('main_image')
                    ->image()
                    ->imageEditorAspectRatios([null, '16:9', '4:3', '1:1'])
 
                    ->hintAction(
                        UnsplashPickerAction::make()
                        ->thumbnail()
                        ->perPage(10)
                        ->useSquareDisplay(true),

                    ),
                    
                Select::make('transfer_type')
                    ->options([
                        'Private transfer' => 'Private transfer',
                        'Shared transfer' => 'Shared transfer',
                        
                    ])
                    ->required(),
                Select::make('vehicle_type')
                    ->options([
                        'Sedan' => 'Sedan',
                        'Micro bus' => 'Micro bus',
                        'Bike' => 'Bike',
                        'Bus' => 'Bus',
                        'Mini bus' => 'Mini bus',
                        'Shuttle' => 'Shuttle',
                        'Taxi' => 'Taxi',
                        'Other' => 'Other',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('min_capacity')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('max_capacity')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('suit_cases')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('small_bag')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('tax')
                    ->numeric()
                    ->nullable(),
                Forms\Components\Textarea::make('details_tax')
                    ->nullable(),
                Select::make('suppliers')
                    ->options([
                        'Supplier A' => 'Supplier A',
                        'Supplier B' => 'Supplier B',
                        'Supplier C' => 'Supplier C',
                    ])
                    ->required(),
            ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('main_image'),

                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TextColumn::make('transfer_type'),
                Tables\Columns\TextColumn::make('vehicle_type'),
                
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransfers::route('/'),
            'create' => Pages\CreateTransfer::route('/create'),
            'edit' => Pages\EditTransfer::route('/{record}/edit'),
        ];
    }
}
