<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\HasLocation;

class Hotel extends Model
{
    use HasFactory;
    //use HasLocation;

    protected $fillable = [
        'name',
        'slug',
        'Type',
        'chaine',
        'gimmonix',
        'giata',
        'short_description',
        'image',
        'rating',
        'sustainability_score',
        'sustainability_certification',
        'description',
        'status',
        'email',
        'phone',
        'country',
        'address',
        'street',
        'city',
        'state',
        'zip',
        'roomcount',
        'from_check_in_time',
        'to_check_in_time',
        'from_check_out_time',
        'to_check_out_time',
        'website',
        
    ];
    public function hotelcontracts(){
        return $this->hasOne(Hotel_Contract::class);
    }

    /**
     * Get the destination that owns the hotel.
     */
    public function destination(): BelongsTo
    {
        return $this->belongsTo(Destination::class);
    }

    /**
     * Get all bookings for this hotel.
     */
    public function bookings(): MorphMany
    {
        return $this->morphMany(Booking::class, 'bookable');
    }

}