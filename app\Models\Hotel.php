<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\HasLocation;

class Hotel extends Model
{
    use HasFactory;
    //use HasLocation;

    protected $fillable = [
        'name',
        'slug',
        'Type',
        'chaine',
        'gimmonix',
        'giata',
        'short_description',
        'image',
        'rating',
        'sustainability_score',
        'sustainability_certification',
        'description',
        'status',
        'email',
        'phone',
        'country',
        'address',
        'street',
        'city',
        'state',
        'zip',
        'roomcount',
        'from_check_in_time',
        'to_check_in_time',
        'from_check_out_time',
        'to_check_out_time',
        'website',
        
    ];
    public function hotelcontracts(){
        return $this->hasOne(Hotel_Contract::class);
    }
        
    
}