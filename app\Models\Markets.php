<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class Markets extends Model
{


    use HasFactory;

    protected $fillable = [
        'name',
        'country',
        'description',

    ];

    public function hotelContracts()
    {
        return $this->belongsToMany(Hotel_Contract::class, 'hotel_contract_market');
    }

    public function activityContracts()
    {
        return $this->belongsToMany(ActivityContract::class, 'activity_contract_market');
    }

    public function transferContracts()
    {
        return $this->belongsToMany(TransferContract::class, 'transfer_contract_market');
    }
}