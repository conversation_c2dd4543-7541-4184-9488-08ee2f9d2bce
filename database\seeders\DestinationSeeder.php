<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Destination;

class DestinationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $destinations = [
            [
                'name' => 'Paris',
                'country' => 'France',
                'continent' => 'Europe',
                'zone' => 'Western Europe',
            ],
            [
                'name' => 'Tokyo',
                'country' => 'Japan',
                'continent' => 'Asia',
                'zone' => 'East Asia',
            ],
            [
                'name' => 'New York',
                'country' => 'United States',
                'continent' => 'North America',
                'zone' => 'Northeast',
            ],
            [
                'name' => 'London',
                'country' => 'United Kingdom',
                'continent' => 'Europe',
                'zone' => 'Western Europe',
            ],
            [
                'name' => 'Sydney',
                'country' => 'Australia',
                'continent' => 'Oceania',
                'zone' => 'Southeast Australia',
            ],
            [
                'name' => 'Dubai',
                'country' => 'United Arab Emirates',
                'continent' => 'Asia',
                'zone' => 'Middle East',
            ],
            [
                'name' => 'Barcelona',
                'country' => 'Spain',
                'continent' => 'Europe',
                'zone' => 'Southern Europe',
            ],
            [
                'name' => 'Rio de Janeiro',
                'country' => 'Brazil',
                'continent' => 'South America',
                'zone' => 'Southeast Brazil',
            ],
        ];

        foreach ($destinations as $destination) {
            Destination::create($destination);
        }
    }
}
