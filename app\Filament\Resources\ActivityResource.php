<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ActivityResource\Pages;
use App\Models\Activity;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\get;
use Filament\Forms\Components\Grid;

use App\Filament\Forms\Components\LeafletMap;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Set;
use Mansoor\UnsplashPicker\Actions\UnsplashPickerAction;

use Cheesegrits\FilamentGoogleMaps\Fields\Map;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;



class ActivityResource extends Resource 
{
   


    
    protected static ?string $model = Activity::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Tabs::make('Tabs')
                ->columnSpan('full')
                ->tabs([
                    
                    Tabs\Tab::make('General Information')->columns(2)->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('highlights')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('hotspot_id')
                            ->numeric(),
                        Forms\Components\FileUpload::make('image')
                            ->image()
                            ->imageEditorAspectRatios([null, '16:9', '4:3', '1:1'])
                                    
                            ->hintAction(
                                UnsplashPickerAction::make()
                                ->thumbnail()
                                ->perPage(10)
                                ->useSquareDisplay(true),
                            
                            ),

                        Forms\Components\TextInput::make('tax')->numeric(),
                        Forms\Components\Textarea::make('details_tax')
                            ->columnSpan(1),
                    ]),
                    Tabs\Tab::make('Address')
                        ->columns(3)
                            ->schema([
                                TextInput::make('address_description')
                                ->columnSpanFull()
                                ->maxLength(255)
                                ->string(),
                            TextInput::make('map_address')
                                ->label('Map address')
                                ->columnSpanFull(),
                            Map::make('location')
                                ->mapControls([
                                    'mapTypeControl' => true,
                                    'scaleControl' => true,
                                    'streetViewControl' => true,
                                    'rotateControl' => true,
                                    'fullscreenControl' => true,
                                    'zoomControl' => true,
                                ])
                                ->columnSpanFull()
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                    $set('latitude', $state['lat']);
                                    $set('longitude', $state['lng']);
                                })->lazy()
                                ->debug()
                                ->geolocate()
                                ->autocomplete('map_address', ['establishment']) // field on form to use as Places geocompletion field
                                ->autocompleteReverse(true) // reverse geocode marker location to autocomplete field
                                ->reverseGeocode([
                                    'street' => '%n %S',
                                    'city' => '%L',
                                    'state' => '%A1',
                                    'zip' => '%z',
                                ])->debug(),
                            TextInput::make('street')->columnSpan(1),
                            TextInput::make('city')->columnSpan(1),
                            TextInput::make('state')->columnSpan(1),
                            TextInput::make('zip')->columnSpan(1),
                            TextInput::make('latitude')
                                ->readOnly()->lazy(),
                            TextInput::make('longitude')
                                ->readOnly()->lazy(),
                        ])->columns(2),
                    Tabs\Tab::make('Activity Details')->columns(3)->schema([
                        Forms\Components\Select::make('activity_type')
                            ->columnSpan(2)
                            ->required()
                            ->options([
                                'Art culture' => 'Art culture',
                                'Sports' => 'Sports',
                                'Historical' => 'Historical',
                                'Audio guides' => 'Audio guides',
                                'Classes and workshops' => 'Classes and workshops',
                                'Food and drink' => 'Food and drink',
                                'Likely to sell out' => 'Likely to sell out',
                                'Limousine transfers' => 'Limousine transfers',
                                'Outdoor activities' => 'Outdoor activities',
                                'Seasonal and special occasions' => 'Seasonal and special occasions',
                                'Unique experiences' => 'Unique experiences',
                            ])
                            ->placeholder('Sélectionnez une option'),

                        Forms\Components\Select::make('activity_nature')
                            ->required()
                            ->options([
                                'air' => 'Air',
                                'land' => 'Land',
                                'sea' => 'Sea',
                                'hybrid' => 'Hybrid',
                            ])
                            ->placeholder('Sélectionnez un type'),

                        Forms\Components\Select::make('difficulty_level')
                            ->required()
                            ->options([
                                'easy' => 'Easy',
                                'medium' => 'Medium',
                                'hard' => 'Hard',
                                'extreme' => 'Extreme',
                            ])
                            ->placeholder('Sélectionnez un niveau'),
                        Forms\Components\Toggle::make('recommend_equipments')->required(),
                        Forms\Components\Toggle::make('requires_adult_for_booking')->required(),
                        Forms\Components\TextInput::make('min_age')->numeric(),
                        Forms\Components\TextInput::make('max_age')->numeric(),
                        Forms\Components\TextInput::make('min_travelers_per_booking')->numeric(),
                        Forms\Components\TextInput::make('max_travelers_per_booking')->numeric(),
                        Radio::make('type_of_service')->required()
                        ->options([
                            'Private' => 'Private',
                            'Shared' => 'Shared',
                        ]),
                        Forms\Components\TextInput::make('min_participant')->numeric(),
                        Forms\Components\TextInput::make('max_participant')->numeric(),
                       Forms\Components\Select::make('starting_points')
                            ->label('Starting Points')
                            ->relationship('startingPoints', 'name')
                            ->multiple()
                            ->createOptionForm([
                                TextInput::make('name'),
                                TextInput::make('pays')
                            ])
                            ->searchable()
                            ->preload(),
                        Forms\Components\Toggle::make('pickup_and_meet_at_start_point')->live()->columnSpan(3)->required(),
                        Grid::make(2)->schema([

                            Forms\Components\Select::make('meeting_point_id')
                            ->label('Meeting Point')
                            ->relationship('meetingPoints', 'name')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                TextInput::make('name'),
                                TextInput::make('pays')
                            ])
                            ->multiple()
                            ->placeholder('Sélectionnez un point'),
                        ])->hidden(function (Get $get) {
                            return $get('pickup_and_meet_at_start_point') ? false : true;
                        }),


                        Forms\Components\TextInput::make('destination_id')
                            ->required()
                            ->numeric(),
                        Forms\Components\Radio::make('end_point_return_to')->live()
                            ->options([
                                'Pick up point' => 'Pick up point',
                                'Meeting point' => 'Meeting point',
                                'Other' => 'Other',
                            ]),
                            
                      Forms\Components\Select::make('ending_points')
                            ->label('Ending Points')
                            ->relationship('endingPoints', 'name')
                            
                            ->searchable()
                            ->createOptionForm([
                                TextInput::make('name'),
                                TextInput::make('pays')
                            ])
                            ->placeholder('Sélectionnez un point')
                            
                            ->preload()->hidden(function (Get $get) {
                                return  $get('end_point_return_to') != 'Other';
                            })
                            
                    ])
                            
                        
                        ])
                    
                ]);
                
            
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image'),
                Tables\Columns\TextColumn::make('title')->searchable(),
                Tables\Columns\TextColumn::make('description')->searchable(),
                Tables\Columns\TextColumn::make('hotspot_id')->numeric()->sortable(),

                Tables\Columns\TextColumn::make('activity_type')->searchable(),
                Tables\Columns\TextColumn::make('activity_nature')->searchable(),
                Tables\Columns\TextColumn::make('difficulty_level')->searchable(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivities::route('/'),
            'create' => Pages\CreateActivity::route('/create'),
            'edit' => Pages\EditActivity::route('/{record}/edit'),
        ];
    }
}