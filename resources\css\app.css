/* Import custom animations */
@import './animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom chatbot typing animation */
@keyframes typing-bounce {

    0%,
    60%,
    100% {
        transform: translateY(0);
    }

    30% {
        transform: translateY(-8px);
    }
}

.typing-dot {
    animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0ms;
}

.typing-dot:nth-child(2) {
    animation-delay: 200ms;
}

.typing-dot:nth-child(3) {
    animation-delay: 400ms;
}

/* Smooth scroll for chat container */
#chat-container {
    scroll-behavior: smooth;
}

/* Custom scrollbar for chat container */
#chat-container::-webkit-scrollbar {
    width: 4px;
}

#chat-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

#chat-container::-webkit-scrollbar-thumb {
    background: #ec4899;
    border-radius: 2px;
}

#chat-container::-webkit-scrollbar-thumb:hover {
    background: #db2777;
}