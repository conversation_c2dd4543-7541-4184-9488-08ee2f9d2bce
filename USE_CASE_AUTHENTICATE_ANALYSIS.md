# TEXTUAL DESCRIPTION OF USE CASE "AUTHENTICATE"

## Use Case Analysis

| Use Case | Authenticate |
|----------|-------------|
| Actors | User |
| Goal | The user authenticates to access personalized features of the Travel Platform |
| Pre-condition | The user has an account or wants to use Google OAuth |
| Post-condition | The user is logged in and accesses authenticated features |
| Trigger | The user clicks "Login" or accesses a protected page |

## Textual Description

| Step | User Action | System Reaction |
|------|-------------|-----------------|
| 1 | The user accesses the login page | The system displays the login form with email, password fields and Google OAuth option |
| 2 | The user enters email and password OR clicks "Login with Google" | The system validates the input format OR redirects to Google OAuth |
| 3 | The user clicks "Login" OR authorizes the application on Google | The system verifies credentials in database OR retrieves Google information |
| 4 | - | The system compares password with stored hash OR verifies/creates Google account |
| 5 | - | The system creates a user session with authentication information |
| 6 | - | The system redirects the user to requested page or dashboard |
| 7 | The user accesses personalized features | The system displays interface with user options (favorites, bookings, profile) |

## Alternative Scenarios

| Scenario | Condition | User Action | System Reaction |
|----------|-----------|-------------|-----------------|
| A1 - Google OAuth | User chooses Google | Clicks "Login with Google" | Redirects to Google OAuth |
| A2 - Invalid credentials | Wrong email/password | Enters incorrect credentials | Displays error message |
| A3 - Forgot password | User forgets password | Clicks "Forgot password" | Displays reset form |
| E1 - Account not found | Email not registered | Attempts to login | Displays "Account not found" |
| E2 - Account disabled | Account suspended | Attempts to login | Displays "Account suspended" |
