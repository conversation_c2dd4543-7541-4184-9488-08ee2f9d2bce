<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\Destination;
use App\Models\ActivityContract;
use App\Models\MeetingPoint;

class ActivityDetailSeeder extends Seeder
{
    public function run()
    {
        // Créer quelques destinations si elles n'existent pas
        $paris = Destination::firstOrCreate([
            'name' => 'Paris',
            'country' => 'France',
            'description' => 'The City of Light',
        ]);

        $lisbon = Destination::firstOrCreate([
            'name' => 'Lisbon',
            'country' => 'Portugal', 
            'description' => 'Beautiful coastal city',
        ]);

        // Créer des activités détaillées
        $surfActivity = Activity::create([
            'title' => 'Surf Masterclass',
            'description' => 'Learn to surf with professional instructors in the beautiful waters of Portugal. This comprehensive course covers everything from basic techniques to advanced maneuvers.',
            'highlights' => 'Professional instruction, equipment included, small groups',
            'image' => 'activities/surf-masterclass.jpg',
            'activity_type' => 'Water Sports',
            'activity_nature' => 'Outdoor',
            'difficulty_level' => 'Intermediate',
            'duration' => '4h',
            'type_of_service' => 'Shared',
            'min_participant' => 2,
            'max_participant' => 8,
            'destination_id' => $lisbon->id,
            'end_point_return_to' => 'Pick up point',
            'public_address' => 'Praia do Guincho, Cascais',
            'city' => 'Cascais',
        ]);

        $louvreActivity = Activity::create([
            'title' => 'Louvre Museum Private Tour',
            'description' => 'Discover the world\'s most famous artworks with a private guide. Skip the lines and explore the Louvre\'s masterpieces including the Mona Lisa and Venus de Milo.',
            'highlights' => 'Skip-the-line access, private guide, 3-hour tour',
            'image' => 'activities/louvre-tour.jpg',
            'activity_type' => 'Cultural',
            'activity_nature' => 'Indoor',
            'difficulty_level' => 'Easy',
            'duration' => '3h',
            'type_of_service' => 'Private',
            'min_participant' => 1,
            'max_participant' => 6,
            'destination_id' => $paris->id,
            'end_point_return_to' => 'Meeting point',
            'public_address' => 'Rue de Rivoli, 75001 Paris',
            'city' => 'Paris',
        ]);

        // Créer des points de rencontre
        $surfMeeting = MeetingPoint::create([
            'name' => 'Guincho Beach Surf School',
            'address' => 'Praia do Guincho, 2750-642 Cascais, Portugal',
            'pays' => 'Portugal',
        ]);

        $louvreMeeting = MeetingPoint::create([
            'name' => 'Louvre Pyramid Entrance',
            'address' => 'Rue de Rivoli, 75001 Paris, France',
            'pays' => 'France',
        ]);

        // Associer les points de rencontre aux activités
        $surfActivity->meetingPoints()->attach($surfMeeting->id);
        $louvreActivity->meetingPoints()->attach($louvreMeeting->id);

        // Créer des contrats d'activité
        $surfContract = ActivityContract::create([
            'contract_number' => 'SURF-001',
            'type' => 'Standard',
            'direct_supplier' => true,
            'main_supplier' => 'Guincho Surf School',
            'type_of_service' => 'Shared',
            'description' => 'Surf lessons with professional instructors',
            'whats_included' => [
                'Professional surf instructor',
                'Surfboard and wetsuit rental',
                'Safety briefing',
                'Small group instruction'
            ],
            'whats_not_included' => [
                'Transportation to beach',
                'Food and drinks',
                'Photos (available for purchase)'
            ],
            'contract_status' => 'signed',
            'start_date' => now(),
            'end_date' => now()->addYear(),
            'price' => [
                'adult' => 89,
                'child' => 69,
                'currency' => 'EUR'
            ],
        ]);

        $louvreContract = ActivityContract::create([
            'contract_number' => 'LOUVRE-001',
            'type' => 'Premium',
            'direct_supplier' => true,
            'main_supplier' => 'Paris Museum Tours',
            'type_of_service' => 'Private',
            'description' => 'Private guided tour of the Louvre Museum',
            'whats_included' => [
                'Skip-the-line entrance tickets',
                'Private professional guide',
                '3-hour guided tour',
                'Headsets for groups over 4'
            ],
            'whats_not_included' => [
                'Hotel pickup and drop-off',
                'Food and drinks',
                'Gratuities'
            ],
            'contract_status' => 'signed',
            'start_date' => now(),
            'end_date' => now()->addYear(),
            'price' => [
                'adult' => 299,
                'child' => 199,
                'currency' => 'EUR'
            ],
        ]);

        // Associer les contrats aux activités
        $surfActivity->activityContracts()->attach($surfContract->id);
        $louvreActivity->activityContracts()->attach($louvreContract->id);

        $this->command->info('Activity details seeded successfully!');
    }
}
