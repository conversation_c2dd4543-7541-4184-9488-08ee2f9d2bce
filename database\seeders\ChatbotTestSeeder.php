<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ChatbotTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer quelques hôtels de test
        \App\Models\Hotel::create([
            'name' => 'Hôtel Royal Palace',
            'slug' => 'hotel-royal-palace',
            'city' => 'Paris',
            'country' => 'France',
            'rating' => 5,
            'sustainability_score' => 4,
            'status' => 'published',
            'short_description' => 'Hôtel de luxe au cœur de Paris',
            'address' => '123 Rue de Rivoli, Paris',
        ]);

        \App\Models\Hotel::create([
            'name' => 'Riad Atlas',
            'slug' => 'riad-atlas',
            'city' => 'Marrakech',
            'country' => 'Maroc',
            'rating' => 4,
            'sustainability_score' => 3,
            'status' => 'published',
            'short_description' => 'Riad traditionnel dans la médina',
            'address' => 'Médina, Marrakech',
        ]);

        \App\Models\Hotel::create([
            'name' => 'Hotel Les Dunes',
            'slug' => 'hotel-les-dunes',
            'city' => 'Djerba',
            'country' => 'Tunisie',
            'rating' => 4,
            'sustainability_score' => 3,
            'status' => 'published',
            'short_description' => 'Hôtel face à la mer',
            'address' => 'Zone touristique, Djerba',
        ]);

        // Créer quelques activités de test
        \App\Models\Activity::create([
            'title' => 'Visite du Louvre',
            'city' => 'Paris',
            'activity_type' => 'Musée',
            'activity_nature' => 'Culturelle',
            'difficulty_level' => 'Facile',
            'type_of_service' => 'Shared',
            'destination_id' => 1,
            'end_point_return_to' => 'Pick up point',
            'description' => 'Visite guidée du célèbre musée du Louvre',
            'public_address' => 'Rue de Rivoli, Paris',
        ]);

        \App\Models\Activity::create([
            'title' => 'Safari dans le désert',
            'city' => 'Marrakech',
            'activity_type' => 'Aventure',
            'activity_nature' => 'Outdoor',
            'difficulty_level' => 'Modéré',
            'type_of_service' => 'Private',
            'destination_id' => 1,
            'end_point_return_to' => 'Pick up point',
            'description' => 'Excursion en 4x4 dans le désert du Sahara',
            'public_address' => 'Départ de Marrakech',
        ]);

        \App\Models\Activity::create([
            'title' => 'Plongée sous-marine',
            'city' => 'Djerba',
            'activity_type' => 'Sport aquatique',
            'activity_nature' => 'Aquatique',
            'difficulty_level' => 'Modéré',
            'type_of_service' => 'Shared',
            'destination_id' => 1,
            'end_point_return_to' => 'Pick up point',
            'description' => 'Découverte des fonds marins de Djerba',
            'public_address' => 'Port de Djerba',
        ]);

        // Créer quelques transferts de test
        \App\Models\Transfer::create([
            'name' => 'Transfert VIP Paris',
            'vehicle_type' => 'Voiture',
            'max_capacity' => 4,
            'description' => 'Transfert privé en voiture de luxe',
        ]);

        \App\Models\Transfer::create([
            'name' => 'Navette Marrakech',
            'vehicle_type' => 'Minivan',
            'max_capacity' => 8,
            'description' => 'Transfert partagé en minivan',
        ]);

        \App\Models\Transfer::create([
            'name' => 'Bus Djerba',
            'vehicle_type' => 'Bus',
            'max_capacity' => 25,
            'description' => 'Transfert en bus climatisé',
        ]);
    }
}
