<?php

use Illuminate\Support\Facades\Password;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.guest')] class extends Component
{
    public string $email = '';

    /**
     * Send a password reset link to the provided email address.
     */
    public function sendPasswordResetLink(): void
    {
        $this->validate([
            'email' => ['required', 'string', 'email'],
        ]);

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
        $status = Password::sendResetLink(
            $this->only('email')
        );

        if ($status != Password::RESET_LINK_SENT) {
            $this->addError('email', __($status));

            return;
        }

        $this->reset('email');

        session()->flash('status', __($status));
    }
}; ?>

<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">

        <!-- Header -->
        <div class="flex flex-col items-center space-y-3">
            <img src="{{ asset('storage/avatar.jpg') }}" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Reset your password</h2>
            <p class="text-center text-gray-500">
                Enter the email associated with your account and we'll send a code to reset your password.
            </p>
        </div>

        <!-- Session Status Message -->
        @if (session('status'))
            <div class="p-3 text-sm text-green-600 bg-green-100 border border-green-300 rounded">
                {{ session('status') }}
            </div>
        @endif

        <!-- Livewire Form -->
        <form wire:submit="sendPasswordResetLink" class="space-y-4">
            <!-- Email Input -->
            <input
                type="email"
                wire:model.defer="email"
                placeholder="Email"
                class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                required
                autofocus
            />
            @error('email')
                <p class="text-sm text-red-600">{{ $message }}</p>
            @enderror

            <!-- Submit Button -->
            <button type="submit" class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md">
                send Password Reset Link
            </button>
        </form>

        <!-- Back to Login -->
        <p class="text-sm text-center text-gray-400">
            Back to <a href="{{ route('login') }}" class="font-semibold hover:underline">Login</a>
        </p>
    </div>
</main>