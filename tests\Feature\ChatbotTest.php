<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ChatbotTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    public function test_chatbot_page_loads(): void
    {
        $response = $this->get('/chatbot');
        $response->assertStatus(200);
    }

    public function test_intent_detection_service(): void
    {
        $service = app(\App\Services\IntentDetectionService::class);

        $result = $service->detectIntentAndEntities("Je cherche un hôtel à Paris");

        $this->assertArrayHasKey('intent', $result);
        $this->assertArrayHasKey('entities', $result);
        $this->assertArrayHasKey('confidence', $result);
    }

    public function test_database_query_service(): void
    {
        $service = app(\App\Services\DatabaseQueryService::class);

        $results = $service->queryByIntent('voir_hotel', ['location' => 'Paris']);

        $this->assertIsArray($results);
    }
}
