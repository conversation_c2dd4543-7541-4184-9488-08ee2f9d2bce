<?php

namespace App\Livewire;

    use App\Livewire\Forms\LoginForm;
    use Livewire\Attributes\Layout;
    use Livewire\Component;

    class LoginPage extends Component
    {
        public LoginForm $form;

        public function login()
        {
            $this->form->authenticate();
            session()->regenerate();

            return redirect()->intended('/'); // Fixed redirect
        }

        #[Layout('components.layouts.home')]
        public function render()
        {
            
            return view('livewire.pages.auth.login');
        }
    }