@startuml Travel Platform - Class Diagram
!theme plain
skinparam backgroundColor #FFFFFF
skinparam class {
    BackgroundColor #F8BBD9
    BorderColor #E91E63
    FontColor #1A1A1A
    HeaderBackgroundColor #E91E63
    HeaderFontColor #FFFFFF
}
skinparam package {
    BackgroundColor #FCE4EC
    BorderColor #E91E63
    FontColor #1A1A1A
}
    
title Travel Platform - Diagramme de Classes

package "User Management" {
    class User {
        -id: int
        -name: string
        -email: string
        -email_verified_at: timestamp
        -password: string
        -remember_token: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +favorites(): HasMany
        +chatSessions(): HasMany
        +supportMessages(): HasMany
        +hasRole(role: string): bool
        +assignRole(role: string): void
    }
}

package "Hotel Management" {
    class Hotel {
        -id: int
        -name: string
        -description: text
        -location: string
        -latitude: decimal
        -longitude: decimal
        -stars: int
        -image: string
        -destination_id: int
        -created_at: timestamp
        -updated_at: timestamp
        --
        +destination(): BelongsTo
        +contracts(): HasMany
        +rooms(): HasMany
        +favorites(): MorphMany
        +getFullAddressAttribute(): string
        +scopeByLocation(query, location): Builder
    }

    class Hotel_Contract {
        -id: int
        -hotel_id: int
        -contract_name: string
        -start_date: date
        -end_date: date
        -currency: string
        -status: enum
        -terms_conditions: text
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotel(): BelongsTo
        +markets(): BelongsToMany
        +isActive(): bool
        +scopeActive(query): Builder
    }

    class Room {
        -id: int
        -hotel_id: int
        -room_type: string
        -capacity: int
        -description: text
        -amenities: json
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotel(): BelongsTo
        +getAmenitiesListAttribute(): array
    }
}

package "Activity Management" {
    class Activity {
        -id: int
        -name: string
        -description: text
        -type: string
        -duration: string
        -difficulty_level: enum
        -min_participants: int
        -max_participants: int
        -starting_point: string
        -ending_point: string
        -image: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +contracts(): HasMany
        +meetingPoints(): HasMany
        +favorites(): MorphMany
        +scopeByType(query, type): Builder
        +scopeByDifficulty(query, difficulty): Builder
    }

    class ActivityContract {
        -id: int
        -activity_id: int
        -contract_name: string
        -start_date: date
        -end_date: date
        -price_per_person: decimal
        -currency: string
        -status: enum
        -created_at: timestamp
        -updated_at: timestamp
        --
        +activity(): BelongsTo
        +markets(): BelongsToMany
        +isActive(): bool
    }

    class MeetingPoint {
        -id: int
        -activity_id: int
        -name: string
        -address: string
        -latitude: decimal
        -longitude: decimal
        -instructions: text
        -created_at: timestamp
        -updated_at: timestamp
        --
        +activity(): BelongsTo
        +getCoordinatesAttribute(): string
    }
}

package "Transfer Management" {
    class Transfer {
        -id: int
        -name: string
        -description: text
        -vehicle_type: string
        -capacity: int
        -suit_cases: int
        -departure_location: string
        -arrival_location: string
        -duration: string
        -image: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +contracts(): HasMany
        +favorites(): MorphMany
        +scopeByVehicleType(query, type): Builder
        +scopeByCapacity(query, capacity): Builder
    }

    class TransferContract {
        -id: int
        -transfer_id: int
        -contract_name: string
        -start_date: date
        -end_date: date
        -price: decimal
        -currency: string
        -status: enum
        -created_at: timestamp
        -updated_at: timestamp
        --
        +transfer(): BelongsTo
        +markets(): BelongsToMany
        +isActive(): bool
    }
}

package "Destination Management" {
    class Destination {
        -id: int
        -name: string
        -description: text
        -country: string
        -region: string
        -image: string
        -featured: boolean
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotels(): HasMany
        +favorites(): MorphMany
        +scopeFeatured(query): Builder
        +scopeByCountry(query, country): Builder
        +getFullLocationAttribute(): string
    }
}

package "Favorites System" {
    class Favorite {
        -id: int
        -user_id: int
        -favoritable_type: string
        -favoritable_id: int
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +favoritable(): MorphTo
        +scopeByType(query, type): Builder
        +scopeByUser(query, userId): Builder
    }
}

package "Support System" {
    class SupportMessage {
        -id: int
        -user_id: int
        -name: string
        -email: string
        -phone: string
        -subject: string
        -message: text
        -status: enum
        -resolved_at: timestamp
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +scopePending(query): Builder
        +scopeResolved(query): Builder
        +markAsResolved(): void
        +isPending(): bool
        +isResolved(): bool
    }
}

package "Chatbot System" {
    class ChatSession {
        -id: int
        -user_id: int
        -session_id: string
        -context: json
        -last_activity: timestamp
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +messages(): HasMany
        +isActive(): bool
        +updateActivity(): void
        +getContextAttribute(): array
        +setContextAttribute(array): void
    }

    class ChatMessage {
        -id: int
        -session_id: int
        -message: text
        -response: text
        -intent: string
        -entities: json
        -confidence: decimal
        -created_at: timestamp
        -updated_at: timestamp
        --
        +session(): BelongsTo
        +getEntitiesAttribute(): array
        +setEntitiesAttribute(array): void
        +hasHighConfidence(): bool
    }
}

package "Market Management" {
    class Markets {
        -id: int
        -name: string
        -code: string
        -description: text
        -currency: string
        -active: boolean
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotelContracts(): BelongsToMany
        +activityContracts(): BelongsToMany
        +transferContracts(): BelongsToMany
        +scopeActive(query): Builder
        +isActive(): bool
    }
}

' Relations principales
User ||--o{ Favorite : "has many"
User ||--o{ ChatSession : "has many"
User ||--o{ SupportMessage : "has many"

Hotel ||--o{ Hotel_Contract : "has many"
Hotel ||--o{ Room : "has many"
Activity ||--o{ ActivityContract : "has many"
Activity ||--o{ MeetingPoint : "has many"
Transfer ||--o{ TransferContract : "has many"
Destination ||--o{ Hotel : "has many"

ChatSession ||--o{ ChatMessage : "has many"

' Relations polymorphes pour les favoris
Favorite }o--|| Hotel : "favoritable"
Favorite }o--|| Activity : "favoritable"
Favorite }o--|| Transfer : "favoritable"
Favorite }o--|| Destination : "favoritable"

' Relations Many-to-Many avec Markets
Markets }o--o{ Hotel_Contract : "markets"
Markets }o--o{ ActivityContract : "markets"
Markets }o--o{ TransferContract : "markets"

@enduml
