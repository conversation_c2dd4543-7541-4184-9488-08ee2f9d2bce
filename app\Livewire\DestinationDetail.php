<?php

namespace App\Livewire;

use App\Models\Destination;
use App\Models\Hotel;
use App\Models\Activity;
use Livewire\Component;
use Illuminate\Support\Str;

class DestinationDetail extends Component
{
    public $destination;
    public $destinationId;

    public function mount($id)
    {
        $this->destinationId = $id;
        $this->destination = Destination::findOrFail($id);
    }

    public function render()
    {
        // Get hotels in this destination (using destination_id column)
        $hotels = Hotel::where('destination_id', $this->destinationId)
            ->orWhere('city', 'like', '%' . $this->destination->name . '%')
            ->orWhere('country', 'like', '%' . $this->destination->country . '%')
            ->limit(6)
            ->get();

        // Get activities in this destination (using destination_id column)
        $activities = Activity::where('destination_id', $this->destinationId)
            ->orWhere('city', 'like', '%' . $this->destination->name . '%')
            ->limit(6)
            ->get();

        return view('livewire.destination-detail', [
            'hotels' => $hotels,
            'activities' => $activities,
        ]);
    }
}
