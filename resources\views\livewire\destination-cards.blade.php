<div class="bg-pink-50 rounded-2xl p-4 mb-4 animate-fade-in-up">
    <!-- Header avec avatar et titre -->
    <div class="flex items-center gap-3 mb-4">
        <img src="{{ asset('storage/avatar.jpg') }}" alt="AI Assistant" class="w-8 h-8 rounded-full animate-bounce-gentle">
        <span class="text-gray-700 font-medium">{{ $title }}</span>
    </div>

    <!-- Grille de cartes de destinations -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
        @foreach ($destinations as $index => $destination)
            <div class="relative group cursor-pointer animate-fade-in-up hover:scale-105 transform transition-all duration-300"
                style="animation-delay: {{ $index * 0.1 }}s" wire:click="selectDestination('{{ $destination['id'] }}')">

                <!-- Image de la destination -->
                <div class="relative h-24 md:h-32 rounded-xl overflow-hidden">
                    @if (isset($destination['image']) && $destination['image'])
                        <img src="{{ asset('storage/' . $destination['image']) }}" alt="{{ $destination['name'] }}"
                            class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                    @else
                        <!-- Image par défaut avec gradient -->
                        <div
                            class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                            <span class="text-white text-2xl">🏙️</span>
                        </div>
                    @endif

                    <!-- Overlay sombre -->
                    <div class="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors duration-300">
                    </div>

                    <!-- Nom de la destination -->
                    <div class="absolute bottom-2 left-2 right-2">
                        <h3 class="text-white font-semibold text-sm md:text-base drop-shadow-lg">
                            {{ $destination['name'] }}
                        </h3>
                        @if (isset($destination['country']))
                            <p class="text-white/80 text-xs drop-shadow">{{ $destination['country'] }}</p>
                        @endif
                    </div>

                    <!-- Indicateur de hover -->
                    <div
                        class="absolute top-2 right-2 w-2 h-2 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse">
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Message d'aide -->
    <div class="mt-3 text-center">
        <p class="text-gray-500 text-xs">Click on a destination to get more information</p>
    </div>
</div>
