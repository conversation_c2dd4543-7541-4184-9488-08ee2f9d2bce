<?php

namespace App\Console\Commands;

use App\Jobs\QueueHeartbeat;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class StartQueueHeartbeat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:heartbeat {action=start : start|stop|status}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage queue heartbeat system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'start':
                return $this->startHeartbeat();
            case 'stop':
                return $this->stopHeartbeat();
            case 'status':
                return $this->heartbeatStatus();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: start, stop, status');
                return 1;
        }
    }

    /**
     * Start the queue heartbeat
     */
    private function startHeartbeat()
    {
        $this->info('Starting queue heartbeat system...');

        // Clear any existing heartbeat
        Cache::forget('queue_worker_heartbeat');

        // Dispatch the first heartbeat job
        QueueHeartbeat::dispatch();

        $this->info('Queue heartbeat started successfully!');
        $this->info('The heartbeat will run every 2 minutes to monitor queue health.');

        return 0;
    }

    /**
     * Stop the queue heartbeat
     */
    private function stopHeartbeat()
    {
        $this->info('Stopping queue heartbeat system...');

        // Clear the heartbeat cache
        Cache::forget('queue_worker_heartbeat');

        $this->info('Queue heartbeat stopped.');
        $this->warn('Note: Existing heartbeat jobs in the queue will still execute.');

        return 0;
    }

    /**
     * Show heartbeat status
     */
    private function heartbeatStatus()
    {
        $this->info('Queue Heartbeat Status:');
        $this->line('========================');

        $lastHeartbeat = Cache::get('queue_worker_heartbeat');

        if ($lastHeartbeat) {
            $this->info("Last heartbeat: {$lastHeartbeat}");
            
            $lastTime = \Carbon\Carbon::parse($lastHeartbeat);
            $minutesAgo = $lastTime->diffInMinutes();
            
            if ($minutesAgo <= 5) {
                $this->info("Status: HEALTHY (last seen {$minutesAgo} minutes ago)");
            } else {
                $this->warn("Status: WARNING (last seen {$minutesAgo} minutes ago)");
            }
        } else {
            $this->error('Status: NO HEARTBEAT DETECTED');
            $this->info('Run "php artisan queue:heartbeat start" to initialize.');
        }

        return 0;
    }
}
