<?php

namespace App\Filament\Contracts\Resources\ActivityContractResource\Pages;

use App\Filament\Contracts\Resources\ActivityContractResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditActivityContract extends EditRecord
{
    protected static string $resource = ActivityContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
