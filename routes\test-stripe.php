<?php

use Illuminate\Support\Facades\Route;
use App\Services\StripeService;

// Test route to verify Stripe integration
Route::get('/test-stripe', function () {
    try {
        $stripeService = new StripeService();

        // Test creating a payment intent
        $result = $stripeService->createPaymentIntent(
            50.00, // $50.00
            'usd',
            [
                'test' => 'true',
                'description' => 'Test payment intent'
            ]
        );

        if ($result['success']) {
            return response()->json([
                'status' => 'success',
                'message' => 'Stripe integration is working!',
                'payment_intent_id' => $result['payment_intent_id'],
                'client_secret' => substr($result['client_secret'], 0, 20) . '...' // Partial for security
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => 'Stripe integration failed',
                'error' => $result['error']
            ], 500);
        }

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Exception occurred',
            'error' => $e->getMessage()
        ], 500);
    }
});

// Test payment page
Route::get('/test-payment', function () {
    return view('test-payment');
});

// Check transactions page
Route::get('/check-transactions', function () {
    $bookings = \App\Models\Booking::with(['user', 'activity'])
        ->orderBy('created_at', 'desc')
        ->limit(20)
        ->get();

    return response()->json([
        'total_bookings' => $bookings->count(),
        'confirmed' => $bookings->where('status', 'confirmed')->count(),
        'pending' => $bookings->where('status', 'pending')->count(),
        'paid' => $bookings->where('payment_status', 'paid')->count(),
        'failed' => $bookings->where('payment_status', 'failed')->count(),
        'recent_bookings' => $bookings->map(function($booking) {
            return [
                'id' => $booking->id,
                'user' => $booking->user->name ?? 'N/A',
                'activity' => $booking->activity->name ?? 'N/A',
                'booking_date' => $booking->booking_date,
                'status' => $booking->status,
                'payment_status' => $booking->payment_status,
                'total_price' => '$' . number_format($booking->total_price, 2),
                'payment_intent_id' => $booking->payment_intent_id,
                'created_at' => $booking->created_at->format('Y-m-d H:i:s')
            ];
        })
    ], 200, [], JSON_PRETTY_PRINT);
});

// Transaction dashboard page
Route::get('/transaction-dashboard', function () {
    return view('transaction-dashboard');
});
