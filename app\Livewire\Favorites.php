<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Favorites extends Component
{
    /**
     * Remove an item from favorites.
     */
    public function removeFromFavorites($type, $id)
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();
        $model = $this->getModel($type, $id);

        if ($model) {
            $user->removeFromFavorites($model);
            session()->flash('success', 'Removed from favorites');
        }
    }

    /**
     * Get the appropriate model instance.
     */
    private function getModel($type, $id)
    {
        return match ($type) {
            'hotel' => \App\Models\Hotel::find($id),
            'activity' => \App\Models\Activity::find($id),
            'transfer' => \App\Models\Transfer::find($id),
            'destination' => \App\Models\Destination::find($id),
            default => null,
        };
    }

    public function render()
    {
        $favorites = [];

        if (Auth::check()) {
            $userFavorites = Auth::user()->favorites()->with('favoritable')->get();

            Log::info('Favorites page render', [
                'user_id' => Auth::id(),
                'total_favorites' => $userFavorites->count(),
                'favorites_data' => $userFavorites->map(function($fav) {
                    return [
                        'type' => $fav->favoritable_type,
                        'id' => $fav->favoritable_id,
                        'favoritable_exists' => $fav->favoritable ? true : false
                    ];
                })
            ]);

            foreach ($userFavorites as $favorite) {
                $type = strtolower(class_basename($favorite->favoritable_type));

                // Map singular to plural correctly
                $pluralType = match($type) {
                    'activity' => 'activities',
                    'hotel' => 'hotels',
                    'transfer' => 'transfers',
                    'destination' => 'destinations',
                    default => $type . 's'
                };

                Log::info('Processing favorite', [
                    'type' => $type,
                    'plural_type' => $pluralType,
                    'favoritable_type' => $favorite->favoritable_type,
                    'favoritable_id' => $favorite->favoritable_id,
                    'favoritable_exists' => $favorite->favoritable ? true : false
                ]);

                if (!isset($favorites[$pluralType])) {
                    $favorites[$pluralType] = [];
                }

                if ($favorite->favoritable) {
                    $favorites[$pluralType][] = $favorite->favoritable;
                }
            }
        }

        Log::info('Final favorites array', [
            'favorites_structure' => array_map('count', $favorites)
        ]);

        return view('livewire.favorites', [
            'favorites' => $favorites,
            'hasFavorites' => !empty($favorites)
        ]);
    }
}