<?php

use App\Models\Hotel_Contract;
use App\Models\Markets;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('markets', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('country')->nullable();
            $table->timestamps();
        });
        Schema::create('hotel_contract_market', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Hotel_Contract::class);
            $table->foreignIdFor(Markets::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_contract_market');
        Schema::dropIfExists('markets');
    }
};