@startuml Travel Platform - Use Case Diagram (Styled)
!theme plain

' Configuration du style exact comme l'image
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
    FontSize 12
}
skinparam usecase {
    BackgroundColor #87CEEB
    BorderColor #4682B4
    FontColor #000000
    FontSize 10
}
skinparam arrow {
    Color #000000
    FontColor #FF0000
}
skinparam note {
    BackgroundColor #FFFFFF
    BorderColor #FF0000
    FontColor #FF0000
}

title Travel Platform - Use Case Diagram

' Acteurs avec positionnement
actor "Utilisateur\nInvité" as Guest
actor "Utilisateur\nEnregistré" as User
actor "Administrateur" as Admin

' Positionnement des acteurs
Guest -[hidden]down- User
User -[hidden]down- Admin

' Cas d'usage principaux - Navigation
usecase "Consulter page\nd'accueil" as UC1
usecase "Explorer\ndestinations" as UC2
usecase "Rechercher\nhôtels" as UC3
usecase "Rechercher\nactivités" as UC4
usecase "Rechercher\ntransferts" as UC5

' Cas d'usage - Communication
usecase "Utiliser\nchatbot" as UC7
usecase "Contacter\nsupport" as UC8

' Cas d'usage - Authentification
usecase "S'inscrire" as UC9
usecase "Se connecter" as UC10

' Extensions d'authentification
usecase "Connexion\nGoogle OAuth" as UC11
usecase "Récupérer\nmot de passe" as UC12

' Cas d'usage - Gestion personnelle
usecase "Gérer\nprofil" as UC13
usecase "Ajouter aux\nfavoris" as UC16
usecase "Consulter\nfavoris" as UC18
usecase "Consulter mes\nvoyages" as UC20

' Extensions personnelles
usecase "Modifier\ninformations" as UC14
usecase "Changer\nmot de passe" as UC15
usecase "Supprimer des\nfavoris" as UC17
usecase "Organiser\nfavoris" as UC19

' Cas d'usage - Administration
usecase "Gérer\nhôtels" as UC22
usecase "Gérer\nactivités" as UC23
usecase "Gérer\ntransferts" as UC24
usecase "Gérer\ndestinations" as UC25
usecase "Gérer\nutilisateurs" as UC27
usecase "Gérer\nsupport" as UC29

' Extensions admin
usecase "Gérer\ncontrats" as UC26
usecase "Gérer\npermissions" as UC28
usecase "Configurer\nchatbot" as UC31

' Relations principales - Utilisateur Invité
Guest --> UC1
Guest --> UC2
Guest --> UC3
Guest --> UC4
Guest --> UC5
Guest --> UC7
Guest --> UC8
Guest --> UC9
Guest --> UC10

' Héritage
Guest <|-- User

' Relations - Utilisateur Enregistré
User --> UC13
User --> UC16
User --> UC18
User --> UC20

' Relations - Administrateur
Admin --> UC22
Admin --> UC23
Admin --> UC24
Admin --> UC25
Admin --> UC27
Admin --> UC29

' Relations Include avec style pointillé
UC10 ..> UC11 : <<include>>
UC10 ..> UC12 : <<include>>
UC13 ..> UC14 : <<include>>
UC13 ..> UC15 : <<include>>
UC18 ..> UC17 : <<include>>
UC16 ..> UC10 : <<include>>

' Relations Extend avec style pointillé
UC11 ..> UC10 : <<extend>>
UC12 ..> UC10 : <<extend>>
UC14 ..> UC13 : <<extend>>
UC15 ..> UC13 : <<extend>>
UC17 ..> UC18 : <<extend>>
UC19 ..> UC18 : <<extend>>
UC26 ..> UC22 : <<extend>>
UC26 ..> UC23 : <<extend>>
UC26 ..> UC24 : <<extend>>
UC28 ..> UC27 : <<extend>>
UC31 ..> UC29 : <<extend>>

' Notes explicatives en rouge
note top of Guest : Actor
note right of UC11 : Extend
note right of UC14 : Extension Point
note left of Guest : Association

@enduml
