{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "awcodes/filament-table-repeater": "^3.1", "bezhansalleh/filament-panel-switch": "^1.0", "bezhansalleh/filament-shield": "^3.3", "cheesegrits/filament-google-maps": "^3.0", "dotswan/filament-map-picker": "^1.8", "filament/filament": "^3.3", "laravel/framework": "^11.31", "laravel/socialite": "^5.20", "laravel/tinker": "^2.9", "leandrocfe/filament-apex-charts": "^3.2", "livewire/livewire": "^3.4", "livewire/volt": "^1.7.0", "mansoor/filament-unsplash-picker": "0.0.9", "mokhosh/filament-rating": "^1.4", "rupadana/filament-slider": "^1.0", "spatie/laravel-permission": "^6.17", "stripe/stripe-php": "^17.3"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/breeze": "^2.3", "laravel/pail": "^1.1", "laravel/pint": "^1.21", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/CurrencyHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:work --sleep=3 --tries=3 --timeout=60 --verbose\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "queue:start": ["php artisan queue:manage start"], "queue:stop": ["php artisan queue:manage stop"], "queue:status": ["php artisan queue:manage status"], "queue:health": ["php artisan queue:health-check"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}