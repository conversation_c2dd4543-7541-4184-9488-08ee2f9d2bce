<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum SpecialOffers: string implements HasLabel
{
    case end_of_year_offer = 'end_of_year_offer';
    case week_end_offer = 'week_end_offer';
    case free_offer = 'free_offer';
    case discount = 'discount';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::end_of_year_offer => 'End of Year offer',
            self::week_end_offer => 'Week-End offer',
            self::free_offer => 'Free offer',
            self::discount => 'Discount',
        };
    }

    
}