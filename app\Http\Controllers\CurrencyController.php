<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\CurrencyService;

class CurrencyController extends Controller
{
    public function switch(Request $request)
    {
        $currency = $request->input('currency');
        
        if (in_array($currency, ['EUR', 'USD', 'TND', 'GBP', 'JPY'])) {
            CurrencyService::setCurrency($currency);
        }
        
        return response()->json([
            'success' => true,
            'currency' => CurrencyService::getCurrentCurrency()
        ]);
    }
}
