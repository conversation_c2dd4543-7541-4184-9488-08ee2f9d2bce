<?php

namespace App\Mail;

use App\Models\Event;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class EventCreatedNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Event $event,
        public User $user
    ) {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $eventName = $this->event->name;
        $destinationName = $this->event->destination->name;

        // Create dynamic subject based on event details
        $subject = "🌟 {$eventName} in {$destinationName} - Your Favorite Destination!";

        return new Envelope(
            subject: $subject,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: config('mail.reply_to.address', '<EMAIL>'),
            tags: ['event-notification', 'favorites', 'destination-' . $this->event->destination->id],
            metadata: [
                'event_id' => $this->event->id,
                'destination_id' => $this->event->destination->id,
                'user_id' => $this->user->id,
                'notification_type' => 'event_created'
            ]
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.event-created',
            with: [
                'event' => $this->event,
                'user' => $this->user,
                'destination' => $this->event->destination,
                'appName' => config('app.name'),
                'appUrl' => config('app.url'),
                'unsubscribeUrl' => route('favorites'),
                'currentYear' => date('Y'),
                'eventDuration' => $this->formatDuration($this->event->duration),
                'eventDateRange' => $this->formatDateRange(),
            ],
        );
    }

    /**
     * Format event duration for display
     */
    private function formatDuration($hours): string
    {
        if (!$hours) return '';

        if ($hours < 24) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }

        $days = floor($hours / 24);
        $remainingHours = $hours % 24;

        $result = $days . ' day' . ($days > 1 ? 's' : '');
        if ($remainingHours > 0) {
            $result .= ' and ' . $remainingHours . ' hour' . ($remainingHours > 1 ? 's' : '');
        }

        return $result;
    }

    /**
     * Format event date range for display
     */
    private function formatDateRange(): string
    {
        if (!$this->event->start_date) return '';

        $startDate = $this->event->start_date;
        $endDate = $this->event->end_date;

        if (!$endDate || $startDate->isSameDay($endDate)) {
            return $startDate->format('F j, Y');
        }

        if ($startDate->isSameMonth($endDate)) {
            return $startDate->format('F j') . ' - ' . $endDate->format('j, Y');
        }

        return $startDate->format('F j, Y') . ' - ' . $endDate->format('F j, Y');
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
