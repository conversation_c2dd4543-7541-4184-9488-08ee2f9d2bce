# Travel Platform - Queue Management Guide

## 🚀 Automatic Queue Processing

This guide explains how to set up and manage automatic queue processing for the Travel Platform, ensuring that email notifications and other background jobs are processed automatically.

## 📋 Quick Start

### Development Environment

**Option 1: Use the development startup script**
```bash
# Windows
scripts\start-dev.bat

# This starts:
# - Laravel server (http://localhost:8000)
# - Queue worker (automatic processing)
# - Vite dev server (asset compilation)
# - Log monitoring (real-time logs)
```

**Option 2: Use Composer scripts**
```bash
# Start all development services
composer dev

# Or manage queue individually
composer queue:start
composer queue:status
composer queue:stop
```

**Option 3: Manual queue management**
```bash
# Start queue worker
php artisan queue:manage start

# Check status
php artisan queue:manage status

# Stop queue worker
php artisan queue:manage stop

# Monitor queue in real-time
php artisan queue:manage monitor
```

### Production Environment

**Windows Service Setup**
```powershell
# Install as Windows service
.\scripts\queue-service.ps1 -Action install

# Start the service
.\scripts\queue-service.ps1 -Action start -Daemon

# Check service status
.\scripts\queue-service.ps1 -Action status
```

**Production Deployment**
```powershell
# Deploy with automatic queue restart
.\scripts\deploy-production.ps1

# Deploy without queue restart
.\scripts\deploy-production.ps1 -RestartQueue:$false
```

## 🔧 Queue Configuration

### Queue Priorities

The system uses three queue priorities:

1. **High Priority** (`high`) - Email notifications, critical jobs
2. **Default Priority** (`default`) - Regular background jobs
3. **Low Priority** (`low`) - Cleanup, maintenance jobs

### Queue Workers

**Single Worker (Development)**
```bash
php artisan queue:work --sleep=3 --tries=3 --timeout=60
```

**Multiple Workers (Production)**
```bash
# High priority queue
php artisan queue:work --queue=high --sleep=1 --tries=3 --timeout=60

# Default queue
php artisan queue:work --queue=default --sleep=3 --tries=3 --timeout=60

# Low priority queue
php artisan queue:work --queue=low --sleep=5 --tries=2 --timeout=120
```

## 📊 Monitoring & Health Checks

### Health Check Command
```bash
# Basic health check
php artisan queue:health-check

# Health check with alerts
php artisan queue:health-check --alert

# Health check with auto-fix
php artisan queue:health-check --fix

# Custom threshold
php artisan queue:health-check --threshold=50
```

### Queue Statistics
```bash
# View queue status
php artisan queue:manage status

# Monitor in real-time
php artisan queue:manage monitor

# View failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
```

## 🔄 Automatic Features

### Queue Heartbeat

The system includes an automatic heartbeat mechanism:

- **QueueHeartbeat job** runs every 2 minutes
- Updates `queue_worker_heartbeat` cache key
- Helps detect if queue workers are stuck or stopped
- Automatically reschedules itself

### Auto-Restart on Failure

Queue workers automatically restart when:

- Code changes are detected
- Memory limits are reached
- Unexpected errors occur
- Manual restart is triggered

### Health Monitoring

Automatic health checks detect:

- High number of pending jobs
- Failed jobs accumulation
- Stuck/old jobs (older than 1 hour)
- Missing queue worker heartbeat
- Database connectivity issues

## 🛠️ Troubleshooting

### Common Issues

**1. Queue worker not processing jobs**
```bash
# Check if worker is running
php artisan queue:manage status

# Restart worker
php artisan queue:manage restart

# Check for failed jobs
php artisan queue:failed
```

**2. High number of pending jobs**
```bash
# Check queue health
php artisan queue:health-check

# Start additional workers
php artisan queue:work --queue=high,default,low
```

**3. Email notifications not sent**
```bash
# Check email configuration
php artisan config:show mail

# Test email sending
php artisan test:email

# Check queue for email jobs
php artisan queue:manage status
```

**4. Memory issues**
```bash
# Restart queue workers (clears memory)
php artisan queue:restart

# Use memory-efficient worker
php artisan queue:work --memory=512
```

### Log Files

Monitor these log files:

- **Application logs**: `storage/logs/laravel.log`
- **Queue worker logs**: `storage/logs/queue-worker.log`
- **System logs**: Check Windows Event Viewer for service issues

## 🔐 Security Considerations

### Production Setup

1. **Run as dedicated user**: Create a dedicated Windows user for the queue service
2. **Limit permissions**: Grant only necessary file system permissions
3. **Monitor logs**: Set up log rotation and monitoring
4. **Backup strategy**: Include queue jobs in backup procedures

### Environment Variables

Ensure these are properly configured:

```env
QUEUE_CONNECTION=database
DB_QUEUE_CONNECTION=mysql
DB_QUEUE_TABLE=jobs
DB_QUEUE=default
DB_QUEUE_RETRY_AFTER=90
```

## 📈 Performance Optimization

### Scaling Queue Workers

**Horizontal Scaling**
```bash
# Run multiple workers on different queues
php artisan queue:work --queue=high &
php artisan queue:work --queue=default &
php artisan queue:work --queue=low &
```

**Vertical Scaling**
```bash
# Increase worker resources
php artisan queue:work --memory=1024 --timeout=300
```

### Database Optimization

1. **Index the jobs table** for better performance
2. **Regular cleanup** of completed jobs
3. **Monitor database size** and implement archiving

## 🚨 Alerts & Notifications

### Setting Up Alerts

The health check system can be extended to send alerts via:

- **Email notifications**
- **Slack webhooks**
- **SMS alerts**
- **Monitoring services** (New Relic, DataDog, etc.)

### Scheduled Health Checks

Add to your task scheduler:

```bash
# Run health check every 5 minutes
*/5 * * * * php artisan queue:health-check --alert
```

## 📞 Support

For queue-related issues:

1. Check this documentation
2. Review application logs
3. Run health checks
4. Contact system administrator

---

**Last Updated**: June 2025  
**Version**: 1.0  
**Maintainer**: Travel Platform Team
