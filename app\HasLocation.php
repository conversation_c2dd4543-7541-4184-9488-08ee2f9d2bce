<?php

namespace App;

trait HasLocation
{
    protected function initializeHasLocation()
    {
        $this->mergeCasts([
            'latitude' => 'float',
            'longitude' => 'float',
        ]);
    }

    protected $appends = ['location'];

    public function getLocationAttribute(): array
    {
        return [
            'latitude' => (float) $this->latitude,
            'longitude' => (float) $this->longitude,
        ];
    }

    public function setLocationAttribute(array $location): void
    {
        $this->attributes['latitude'] = $location['latitude'];
        $this->attributes['longitude'] = $location['longitude'];
    }

  /*  protected function mergeCasts(array $newCasts): void
    {
        if (property_exists($this, 'casts')) {
            $this->casts = array_merge($this->casts, $newCasts);
        } else {
            $this->casts = $newCasts;
        }
    }*/
}