<?php

namespace App\Filament\Contracts\Resources\TransferContractResource\Pages;

use App\Filament\Contracts\Resources\TransferContractResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTransferContracts extends ListRecords
{
    protected static string $resource = TransferContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
