<?php

namespace App\Livewire;

use App\Models\Destination;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class AllDestinations extends Component
{
    use WithPagination;

    public $search = '';
    public $selectedContinent = '';
    public $selectedCountry = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedContinent' => ['except' => ''],
        'selectedCountry' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedContinent()
    {
        $this->resetPage();
        $this->selectedCountry = ''; // Reset country when continent changes
    }

    public function updatingSelectedCountry()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->selectedContinent = '';
        $this->selectedCountry = '';
        $this->resetPage();
    }

    public function render()
    {
        $query = Destination::query();

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('country', 'like', '%' . $this->search . '%')
                  ->orWhere('continent', 'like', '%' . $this->search . '%')
                  ->orWhere('zone', 'like', '%' . $this->search . '%');
            });
        }

        // Apply continent filter
        if ($this->selectedContinent) {
            $query->where('continent', $this->selectedContinent);
        }

        // Apply country filter
        if ($this->selectedCountry) {
            $query->where('country', $this->selectedCountry);
        }

        $destinations = $query->whereNotNull('name')->orderBy('name')->paginate(12);

        // Get filter options
        $continents = Destination::whereNotNull('continent')
            ->distinct()
            ->pluck('continent')
            ->filter()
            ->sort()
            ->values();

        $countries = Destination::when($this->selectedContinent, function ($q) {
                return $q->where('continent', $this->selectedContinent);
            })
            ->whereNotNull('country')
            ->distinct()
            ->pluck('country')
            ->filter()
            ->sort()
            ->values();

        return view('livewire.all-destinations', [
            'destinations' => $destinations,
            'continents' => $continents,
            'countries' => $countries,
        ]);
    }

    /**
     * Toggle favorite status for a destination.
     */
    public function toggleFavorite($type, $id)
    {
        if (!Auth::check()) {
            session()->flash('error', 'Please login to add favorites');
            return;
        }

        $user = Auth::user();
        $destination = Destination::find($id);

        if ($destination) {
            if ($user->hasFavorited($destination)) {
                $user->removeFromFavorites($destination);
                session()->flash('success', 'Removed from favorites');
            } else {
                $user->addToFavorites($destination);
                session()->flash('success', 'Added to favorites');
            }
        }
    }

    /**
     * Check if user has favorited a destination.
     */
    public function isFavorited($type, $id)
    {
        if (!Auth::check()) {
            return false;
        }

        $destination = Destination::find($id);
        return $destination ? Auth::user()->hasFavorited($destination) : false;
    }
}
