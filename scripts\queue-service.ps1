# Travel Platform Queue Worker Service
# PowerShell script for advanced queue management

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "status", "install", "uninstall")]
    [string]$Action,
    
    [switch]$Daemon,
    [int]$Sleep = 3,
    [int]$Tries = 3,
    [int]$Timeout = 60
)

# Configuration
$ProjectPath = Split-Path -Parent $PSScriptRoot
$PhpPath = "php"
$ArtisanPath = Join-Path $ProjectPath "artisan"
$LogPath = Join-Path $ProjectPath "storage\logs\queue-worker.log"
$PidFile = Join-Path $ProjectPath "storage\queue-worker.pid"
$ServiceName = "TravelPlatformQueue"

# Ensure we're in the project directory
Set-Location $ProjectPath

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message"
    Add-Content -Path $LogPath -Value "[$timestamp] $Message"
}

function Start-QueueWorker {
    Write-Log "Starting Travel Platform Queue Worker..."
    
    # Check if already running
    if (Test-Path $PidFile) {
        $pidInfo = Get-Content $PidFile -ErrorAction SilentlyContinue
        Write-Warning "Queue worker may already be running. PID info: $pidInfo"
        return
    }
    
    try {
        # Build command
        $command = "$PhpPath `"$ArtisanPath`" queue:work --sleep=$Sleep --tries=$Tries --timeout=$Timeout --verbose"
        
        if ($Daemon) {
            # Start as background process
            $process = Start-Process -FilePath "powershell" -ArgumentList "-Command", $command -WindowStyle Hidden -PassThru
            $process.Id | Out-File -FilePath $PidFile
            Write-Log "Queue worker started as daemon with PID: $($process.Id)"
        } else {
            # Start in current session
            Write-Log "Starting queue worker in current session..."
            Invoke-Expression $command
        }
        
        Write-Log "Queue worker started successfully!"
        
    } catch {
        Write-Error "Failed to start queue worker: $($_.Exception.Message)"
    }
}

function Stop-QueueWorker {
    Write-Log "Stopping Travel Platform Queue Worker..."
    
    try {
        # Send Laravel queue restart signal
        & $PhpPath $ArtisanPath queue:restart
        
        # Kill process if PID file exists
        if (Test-Path $PidFile) {
            $pid = Get-Content $PidFile -ErrorAction SilentlyContinue
            if ($pid -and (Get-Process -Id $pid -ErrorAction SilentlyContinue)) {
                Stop-Process -Id $pid -Force
                Write-Log "Stopped process with PID: $pid"
            }
            Remove-Item $PidFile -ErrorAction SilentlyContinue
        }
        
        Write-Log "Queue worker stopped successfully!"
        
    } catch {
        Write-Error "Error stopping queue worker: $($_.Exception.Message)"
    }
}

function Get-QueueStatus {
    Write-Host "Travel Platform Queue Worker Status" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    
    # Check if running
    $isRunning = $false
    if (Test-Path $PidFile) {
        $pid = Get-Content $PidFile -ErrorAction SilentlyContinue
        if ($pid -and (Get-Process -Id $pid -ErrorAction SilentlyContinue)) {
            Write-Host "Status: " -NoNewline
            Write-Host "RUNNING" -ForegroundColor Green
            Write-Host "PID: $pid"
            $isRunning = $true
        }
    }
    
    if (-not $isRunning) {
        Write-Host "Status: " -NoNewline
        Write-Host "STOPPED" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Show queue statistics
    try {
        & $PhpPath $ArtisanPath queue:manage status
    } catch {
        Write-Warning "Could not retrieve queue statistics"
    }
}

function Install-Service {
    Write-Log "Installing Travel Platform Queue Service..."
    
    # Create a scheduled task for auto-start
    $taskName = "TravelPlatformQueueWorker"
    $scriptPath = $PSCommandPath
    
    try {
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File `"$scriptPath`" -Action start -Daemon"
        $trigger = New-ScheduledTaskTrigger -AtStartup
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Force
        
        Write-Log "Service installed successfully! Queue worker will start automatically on system boot."
        
    } catch {
        Write-Error "Failed to install service: $($_.Exception.Message)"
    }
}

function Uninstall-Service {
    Write-Log "Uninstalling Travel Platform Queue Service..."
    
    try {
        $taskName = "TravelPlatformQueueWorker"
        Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
        
        Write-Log "Service uninstalled successfully!"
        
    } catch {
        Write-Error "Failed to uninstall service: $($_.Exception.Message)"
    }
}

# Main execution
switch ($Action) {
    "start" { Start-QueueWorker }
    "stop" { Stop-QueueWorker }
    "restart" { 
        Stop-QueueWorker
        Start-Sleep -Seconds 3
        Start-QueueWorker
    }
    "status" { Get-QueueStatus }
    "install" { Install-Service }
    "uninstall" { Uninstall-Service }
}
