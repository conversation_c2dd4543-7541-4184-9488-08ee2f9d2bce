<?php

use App\Models\Hotel;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotel__contracts', function (Blueprint $table) {
            $table->id();
            
            $table->foreignIdFor(Hotel::class);
            //$table->integer('hotel_id')->constraint('hotels')->ondelete('cascade');
            $table->string('contract_number');
            $table->string('type');

            $table->boolean('direct_supplier')->default(true);
            $table->string('main_supplier');
            $table->string('intermediate_supplier')->nullable();
            $table->string('hotels_chains')->nullable();
            
            $table->string('markets');
            $table->text('description');
            $table->json('whats_included')->nullable();
            $table->json('whats_not_included')->nullable();

            $table->timestamps();
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel__contracts');
    }
};