<?php

namespace App\Models;

use App\Enums\ContractStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Hotel_Contract extends Model
{
    use HasFactory;

    protected $table = 'hotel__contracts';

    protected $fillable = [
        'hotel_id',
        'contract_number',
        'type',
        'direct_supplier',
        'main_supplier',
        'intermediate_supplier',
        'hotels_chains',

        'markets',
        'description',
        'whats_included',
        'whats_not_included',
        'contract_status',
        'signed_date',
        'start_date',
        'end_date',
        'channels_contract',
        'special_offers',
        'policies',
    ];

    protected $casts = [
        'direct_supplier' => 'boolean',
        'whats_included' => 'array',
        'whats_not_included' => 'array',
        'show_allocations' => 'array',
        'rooms' => 'array',
        'markets' => 'array',
        'policies' => 'array',
        'special_offers'=> 'array',
        'channels_contract' => 'array',
        'contract_status' => ContractStatus::class
    ];

    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }


    public function markets()
    {
        return $this->belongsToMany(Markets::class, 'hotel_contract_market');
    }
    public function rooms(){
        return $this->hasMany(Room::class, 'hotel_contract_id');
    }

}