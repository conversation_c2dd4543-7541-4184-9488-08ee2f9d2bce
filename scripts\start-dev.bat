@echo off
REM Travel Platform Development Startup Script
REM This script starts all development services including queue worker

echo ========================================
echo Travel Platform Development Startup
echo ========================================
echo.

REM Change to project directory
cd /d "%~dp0.."

REM Check if composer is available
where composer >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Composer not found in PATH
    echo Please install Composer or add it to your PATH
    pause
    exit /b 1
)

REM Check if npm is available
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: npm not found in PATH
    echo Please install Node.js or add npm to your PATH
    pause
    exit /b 1
)

echo Starting Travel Platform development environment...
echo.

REM Install dependencies if needed
if not exist "vendor" (
    echo Installing PHP dependencies...
    composer install
    echo.
)

if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
    echo.
)

REM Run database migrations
echo Running database migrations...
php artisan migrate --force
echo.

REM Clear caches
echo Clearing application caches...
php artisan config:clear
php artisan cache:clear
php artisan view:clear
echo.

REM Start queue heartbeat
echo Starting queue heartbeat...
php artisan queue:clear
php artisan queue:restart
echo.

REM Start development servers with queue worker
echo Starting development servers...
echo.
echo Services starting:
echo - Laravel Server: http://localhost:8000
echo - Queue Worker: Background processing
echo - Vite Dev Server: Asset compilation
echo - Log Monitoring: Real-time logs
echo.
echo Press Ctrl+C to stop all services
echo.

REM Start all services using composer script
composer dev
