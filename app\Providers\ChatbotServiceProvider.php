<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class ChatbotServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Enregistrer les services du chatbot
        $this->app->singleton(\App\Services\GeminiService::class);
        $this->app->singleton(\App\Services\IntentDetectionService::class);
        $this->app->singleton(\App\Services\ContextManager::class);
        $this->app->singleton(\App\Services\DatabaseQueryService::class);
        $this->app->singleton(\App\Services\ChatbotService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
