@php
    $rating = (int) $getState();
@endphp

<div class="flex items-center space-x-1">
    @for ($i = 1; $i <= 5; $i++)
        @if ($i <= $rating)
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927a1 1 0 011.902 0l1.857 3.76a1 1 0 00.753.547l4.146.604a1 1 0 01.554 1.705l-3.002 2.92a1 1 0 00-.287.885l.709 4.135a1 1 0 01-1.451 1.054L10 15.347l-3.719 1.955a1 1 0 01-1.451-1.054l.709-4.135a1 1 0 00-.287-.885l-3.002-2.92a1 1 0 01.554-1.705l4.146-.604a1 1 0 00.753-.547l1.857-3.76z"/>
            </svg>
        @else
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927a1 1 0 011.902 0l1.857 3.76a1 1 0 00.753.547l4.146.604a1 1 0 01.554 1.705l-3.002 2.92a1 1 0 00-.287.885l.709 4.135a1 1 0 01-1.451 1.054L10 15.347l-3.719 1.955a1 1 0 01-1.451-1.054l.709-4.135a1 1 0 00-.287-.885l-3.002-2.92a1 1 0 01.554-1.705l4.146-.604a1 1 0 00.753-.547l1.857-3.76z"/>
            </svg>
        @endif
    @endfor
</div>
