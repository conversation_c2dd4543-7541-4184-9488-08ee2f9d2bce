<?php

namespace App\Http\Controllers\Auth;

use Lara<PERSON>\Socialite\Facades\Socialite;
use App\Http\Controllers\Controller;

class GoogleLoginController extends Controller
{
    public function redirect()
    {
        return Socialite::driver('google')->redirect();
    }

    public function callback()
    {
        $user = Socialite::driver('google')->user();
        
        // Implement your user creation/login logic here
        // Example: auth()->login($user);
        
        return redirect('/dashboard');
    }
}