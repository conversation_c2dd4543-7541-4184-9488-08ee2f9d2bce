var B;(function(r){r.Range="range",r.Steps="steps",r.Positions="positions",r.Count="count",r.Values="values"})(B||(B={}));var _;(function(r){r[r.None=-1]="None",r[r.NoValue=0]="NoValue",r[r.LargeValue=1]="LargeValue",r[r.SmallValue=2]="SmallValue"})(_||(_={}));function bt(r){return te(r)&&typeof r.from=="function"}function te(r){return typeof r=="object"&&typeof r.to=="function"}function Re(r){r.parentElement.removeChild(r)}function Se(r){return r!=null}function je(r){r.preventDefault()}function Ct(r){return r.filter(function(e){return this[e]?!1:this[e]=!0},{})}function Et(r,e){return Math.round(r/e)*e}function At(r,e){var s=r.getBoundingClientRect(),f=r.ownerDocument,u=f.documentElement,d=Ie(f);return/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)&&(d.x=0),e?s.top+d.y-u.clientTop:s.left+d.x-u.clientLeft}function z(r){return typeof r=="number"&&!isNaN(r)&&isFinite(r)}function Fe(r,e,s){s>0&&(L(r,e),setTimeout(function(){ee(r,e)},s))}function Ne(r){return Math.max(Math.min(r,100),0)}function re(r){return Array.isArray(r)?r:[r]}function Pt(r){r=String(r);var e=r.split(".");return e.length>1?e[1].length:0}function L(r,e){r.classList&&!/\s/.test(e)?r.classList.add(e):r.className+=" "+e}function ee(r,e){r.classList&&!/\s/.test(e)?r.classList.remove(e):r.className=r.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")}function Vt(r,e){return r.classList?r.classList.contains(e):new RegExp("\\b"+e+"\\b").test(r.className)}function Ie(r){var e=window.pageXOffset!==void 0,s=(r.compatMode||"")==="CSS1Compat",f=e?window.pageXOffset:s?r.documentElement.scrollLeft:r.body.scrollLeft,u=e?window.pageYOffset:s?r.documentElement.scrollTop:r.body.scrollTop;return{x:f,y:u}}function Dt(){return window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"}}function kt(){var r=!1;try{var e=Object.defineProperty({},"passive",{get:function(){r=!0}});window.addEventListener("test",null,e)}catch{}return r}function yt(){return window.CSS&&CSS.supports&&CSS.supports("touch-action","none")}function xe(r,e){return 100/(e-r)}function we(r,e,s){return e*100/(r[s+1]-r[s])}function Ut(r,e){return we(r,r[0]<0?e+Math.abs(r[0]):e-r[0],0)}function Mt(r,e){return e*(r[1]-r[0])/100+r[0]}function W(r,e){for(var s=1;r>=e[s];)s+=1;return s}function Lt(r,e,s){if(s>=r.slice(-1)[0])return 100;var f=W(s,r),u=r[f-1],d=r[f],v=e[f-1],b=e[f];return v+Ut([u,d],s)/xe(v,b)}function _t(r,e,s){if(s>=100)return r.slice(-1)[0];var f=W(s,e),u=r[f-1],d=r[f],v=e[f-1],b=e[f];return Mt([u,d],(s-v)*xe(v,b))}function Ot(r,e,s,f){if(f===100)return f;var u=W(f,r),d=r[u-1],v=r[u];return s?f-d>(v-d)/2?v:d:e[u-1]?r[u-1]+Et(f-r[u-1],e[u-1]):f}var Ke=function(){function r(e,s,f){this.xPct=[],this.xVal=[],this.xSteps=[],this.xNumSteps=[],this.xHighestCompleteStep=[],this.xSteps=[f||!1],this.xNumSteps=[!1],this.snap=s;var u,d=[];for(Object.keys(e).forEach(function(v){d.push([re(e[v]),v])}),d.sort(function(v,b){return v[0][0]-b[0][0]}),u=0;u<d.length;u++)this.handleEntryPoint(d[u][1],d[u][0]);for(this.xNumSteps=this.xSteps.slice(0),u=0;u<this.xNumSteps.length;u++)this.handleStepPoint(u,this.xNumSteps[u])}return r.prototype.getDistance=function(e){for(var s=[],f=0;f<this.xNumSteps.length-1;f++)s[f]=we(this.xVal,e,f);return s},r.prototype.getAbsoluteDistance=function(e,s,f){var u=0;if(e<this.xPct[this.xPct.length-1])for(;e>this.xPct[u+1];)u++;else e===this.xPct[this.xPct.length-1]&&(u=this.xPct.length-2);!f&&e===this.xPct[u+1]&&u++,s===null&&(s=[]);var d,v=1,b=s[u],x=0,p=0,k=0,V=0;for(f?d=(e-this.xPct[u])/(this.xPct[u+1]-this.xPct[u]):d=(this.xPct[u+1]-e)/(this.xPct[u+1]-this.xPct[u]);b>0;)x=this.xPct[u+1+V]-this.xPct[u+V],s[u+V]*v+100-d*100>100?(p=x*d,v=(b-100*d)/s[u+V],d=1):(p=s[u+V]*x/100*v,v=0),f?(k=k-p,this.xPct.length+V>=1&&V--):(k=k+p,this.xPct.length-V>=1&&V++),b=s[u+V]*v;return e+k},r.prototype.toStepping=function(e){return e=Lt(this.xVal,this.xPct,e),e},r.prototype.fromStepping=function(e){return _t(this.xVal,this.xPct,e)},r.prototype.getStep=function(e){return e=Ot(this.xPct,this.xSteps,this.snap,e),e},r.prototype.getDefaultStep=function(e,s,f){var u=W(e,this.xPct);return(e===100||s&&e===this.xPct[u-1])&&(u=Math.max(u-1,1)),(this.xVal[u]-this.xVal[u-1])/f},r.prototype.getNearbySteps=function(e){var s=W(e,this.xPct);return{stepBefore:{startValue:this.xVal[s-2],step:this.xNumSteps[s-2],highestStep:this.xHighestCompleteStep[s-2]},thisStep:{startValue:this.xVal[s-1],step:this.xNumSteps[s-1],highestStep:this.xHighestCompleteStep[s-1]},stepAfter:{startValue:this.xVal[s],step:this.xNumSteps[s],highestStep:this.xHighestCompleteStep[s]}}},r.prototype.countStepDecimals=function(){var e=this.xNumSteps.map(Pt);return Math.max.apply(null,e)},r.prototype.hasNoSize=function(){return this.xVal[0]===this.xVal[this.xVal.length-1]},r.prototype.convert=function(e){return this.getStep(this.toStepping(e))},r.prototype.handleEntryPoint=function(e,s){var f;if(e==="min"?f=0:e==="max"?f=100:f=parseFloat(e),!z(f)||!z(s[0]))throw new Error("noUiSlider: 'range' value isn't numeric.");this.xPct.push(f),this.xVal.push(s[0]);var u=Number(s[1]);f?this.xSteps.push(isNaN(u)?!1:u):isNaN(u)||(this.xSteps[0]=u),this.xHighestCompleteStep.push(0)},r.prototype.handleStepPoint=function(e,s){if(s){if(this.xVal[e]===this.xVal[e+1]){this.xSteps[e]=this.xHighestCompleteStep[e]=this.xVal[e];return}this.xSteps[e]=we([this.xVal[e],this.xVal[e+1]],s,0)/xe(this.xPct[e],this.xPct[e+1]);var f=(this.xVal[e+1]-this.xVal[e])/this.xNumSteps[e],u=Math.ceil(Number(f.toFixed(3))-1),d=this.xVal[e]+this.xNumSteps[e]*u;this.xHighestCompleteStep[e]=d}},r}(),Be={to:function(r){return r===void 0?"":r.toFixed(2)},from:Number},qe={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"},N={tooltips:".__tooltips",aria:".__aria"};function Ht(r,e){if(!z(e))throw new Error("noUiSlider: 'step' is not numeric.");r.singleStep=e}function zt(r,e){if(!z(e))throw new Error("noUiSlider: 'keyboardPageMultiplier' is not numeric.");r.keyboardPageMultiplier=e}function Rt(r,e){if(!z(e))throw new Error("noUiSlider: 'keyboardMultiplier' is not numeric.");r.keyboardMultiplier=e}function jt(r,e){if(!z(e))throw new Error("noUiSlider: 'keyboardDefaultStep' is not numeric.");r.keyboardDefaultStep=e}function Ft(r,e){if(typeof e!="object"||Array.isArray(e))throw new Error("noUiSlider: 'range' is not an object.");if(e.min===void 0||e.max===void 0)throw new Error("noUiSlider: Missing 'min' or 'max' in 'range'.");r.spectrum=new Ke(e,r.snap||!1,r.singleStep)}function Nt(r,e){if(e=re(e),!Array.isArray(e)||!e.length)throw new Error("noUiSlider: 'start' option is incorrect.");r.handles=e.length,r.start=e}function Bt(r,e){if(typeof e!="boolean")throw new Error("noUiSlider: 'snap' option must be a boolean.");r.snap=e}function It(r,e){if(typeof e!="boolean")throw new Error("noUiSlider: 'animate' option must be a boolean.");r.animate=e}function Kt(r,e){if(typeof e!="number")throw new Error("noUiSlider: 'animationDuration' option must be a number.");r.animationDuration=e}function Te(r,e){var s=[!1],f;if(e==="lower"?e=[!0,!1]:e==="upper"&&(e=[!1,!0]),e===!0||e===!1){for(f=1;f<r.handles;f++)s.push(e);s.push(!1)}else{if(!Array.isArray(e)||!e.length||e.length!==r.handles+1)throw new Error("noUiSlider: 'connect' option doesn't match handle count.");s=e}r.connect=s}function qt(r,e){switch(e){case"horizontal":r.ort=0;break;case"vertical":r.ort=1;break;default:throw new Error("noUiSlider: 'orientation' option is invalid.")}}function Xe(r,e){if(!z(e))throw new Error("noUiSlider: 'margin' option must be numeric.");e!==0&&(r.margin=r.spectrum.getDistance(e))}function Tt(r,e){if(!z(e))throw new Error("noUiSlider: 'limit' option must be numeric.");if(r.limit=r.spectrum.getDistance(e),!r.limit||r.handles<2)throw new Error("noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.")}function Xt(r,e){var s;if(!z(e)&&!Array.isArray(e))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(e)&&!(e.length===2||z(e[0])||z(e[1])))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(e!==0){for(Array.isArray(e)||(e=[e,e]),r.padding=[r.spectrum.getDistance(e[0]),r.spectrum.getDistance(e[1])],s=0;s<r.spectrum.xNumSteps.length-1;s++)if(r.padding[0][s]<0||r.padding[1][s]<0)throw new Error("noUiSlider: 'padding' option must be a positive number(s).");var f=e[0]+e[1],u=r.spectrum.xVal[0],d=r.spectrum.xVal[r.spectrum.xVal.length-1];if(f/(d-u)>1)throw new Error("noUiSlider: 'padding' option must not exceed 100% of the range.")}}function Yt(r,e){switch(e){case"ltr":r.dir=0;break;case"rtl":r.dir=1;break;default:throw new Error("noUiSlider: 'direction' option was not recognized.")}}function Wt(r,e){if(typeof e!="string")throw new Error("noUiSlider: 'behaviour' must be a string containing options.");var s=e.indexOf("tap")>=0,f=e.indexOf("drag")>=0,u=e.indexOf("fixed")>=0,d=e.indexOf("snap")>=0,v=e.indexOf("hover")>=0,b=e.indexOf("unconstrained")>=0,x=e.indexOf("invert-connects")>=0,p=e.indexOf("drag-all")>=0,k=e.indexOf("smooth-steps")>=0;if(u){if(r.handles!==2)throw new Error("noUiSlider: 'fixed' behaviour must be used with 2 handles");Xe(r,r.start[1]-r.start[0])}if(x&&r.handles!==2)throw new Error("noUiSlider: 'invert-connects' behaviour must be used with 2 handles");if(b&&(r.margin||r.limit))throw new Error("noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit");r.events={tap:s||d,drag:f,dragAll:p,smoothSteps:k,fixed:u,snap:d,hover:v,unconstrained:b,invertConnects:x}}function Gt(r,e){if(e!==!1)if(e===!0||te(e)){r.tooltips=[];for(var s=0;s<r.handles;s++)r.tooltips.push(e)}else{if(e=re(e),e.length!==r.handles)throw new Error("noUiSlider: must pass a formatter for all handles.");e.forEach(function(f){if(typeof f!="boolean"&&!te(f))throw new Error("noUiSlider: 'tooltips' must be passed a formatter or 'false'.")}),r.tooltips=e}}function Jt(r,e){if(e.length!==r.handles)throw new Error("noUiSlider: must pass a attributes for all handles.");r.handleAttributes=e}function Zt(r,e){if(!te(e))throw new Error("noUiSlider: 'ariaFormat' requires 'to' method.");r.ariaFormat=e}function $t(r,e){if(!bt(e))throw new Error("noUiSlider: 'format' requires 'to' and 'from' methods.");r.format=e}function Qt(r,e){if(typeof e!="boolean")throw new Error("noUiSlider: 'keyboardSupport' option must be a boolean.");r.keyboardSupport=e}function er(r,e){r.documentElement=e}function tr(r,e){if(typeof e!="string"&&e!==!1)throw new Error("noUiSlider: 'cssPrefix' must be a string or `false`.");r.cssPrefix=e}function rr(r,e){if(typeof e!="object")throw new Error("noUiSlider: 'cssClasses' must be an object.");typeof r.cssPrefix=="string"?(r.cssClasses={},Object.keys(e).forEach(function(s){r.cssClasses[s]=r.cssPrefix+e[s]})):r.cssClasses=e}function Ye(r){var e={margin:null,limit:null,padding:null,animate:!0,animationDuration:300,ariaFormat:Be,format:Be},s={step:{r:!1,t:Ht},keyboardPageMultiplier:{r:!1,t:zt},keyboardMultiplier:{r:!1,t:Rt},keyboardDefaultStep:{r:!1,t:jt},start:{r:!0,t:Nt},connect:{r:!0,t:Te},direction:{r:!0,t:Yt},snap:{r:!1,t:Bt},animate:{r:!1,t:It},animationDuration:{r:!1,t:Kt},range:{r:!0,t:Ft},orientation:{r:!1,t:qt},margin:{r:!1,t:Xe},limit:{r:!1,t:Tt},padding:{r:!1,t:Xt},behaviour:{r:!0,t:Wt},ariaFormat:{r:!1,t:Zt},format:{r:!1,t:$t},tooltips:{r:!1,t:Gt},keyboardSupport:{r:!0,t:Qt},documentElement:{r:!1,t:er},cssPrefix:{r:!0,t:tr},cssClasses:{r:!0,t:rr},handleAttributes:{r:!1,t:Jt}},f={connect:!1,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:!0,cssPrefix:"noUi-",cssClasses:qe,keyboardPageMultiplier:5,keyboardMultiplier:1,keyboardDefaultStep:10};r.format&&!r.ariaFormat&&(r.ariaFormat=r.format),Object.keys(s).forEach(function(x){if(!Se(r[x])&&f[x]===void 0){if(s[x].r)throw new Error("noUiSlider: '"+x+"' is required.");return}s[x].t(e,Se(r[x])?r[x]:f[x])}),e.pips=r.pips;var u=document.createElement("div"),d=u.style.msTransform!==void 0,v=u.style.transform!==void 0;e.transformRule=v?"transform":d?"msTransform":"webkitTransform";var b=[["left","top"],["right","bottom"]];return e.style=b[e.dir][e.ort],e}function ir(r,e,s){var f=Dt(),u=yt(),d=u&&kt(),v=r,b,x,p,k,V,H,m=e.spectrum,R=[],C=[],O=[],ie=0,j={},T=!1,q=r.ownerDocument,G=e.documentElement||q.documentElement,J=q.body,We=q.dir==="rtl"||e.ort===1?0:100;function F(t,i){var n=q.createElement("div");return i&&L(n,i),t.appendChild(n),n}function Ge(t,i){var n=F(t,e.cssClasses.origin),a=F(n,e.cssClasses.handle);if(F(a,e.cssClasses.touchArea),a.setAttribute("data-handle",String(i)),e.keyboardSupport&&(a.setAttribute("tabindex","0"),a.addEventListener("keydown",function(l){return ct(l,i)})),e.handleAttributes!==void 0){var o=e.handleAttributes[i];Object.keys(o).forEach(function(l){a.setAttribute(l,o[l])})}return a.setAttribute("role","slider"),a.setAttribute("aria-orientation",e.ort?"vertical":"horizontal"),i===0?L(a,e.cssClasses.handleLower):i===e.handles-1&&L(a,e.cssClasses.handleUpper),n.handle=a,n}function ne(t,i){return i?F(t,e.cssClasses.connect):!1}function Je(t,i){x=F(i,e.cssClasses.connects),p=[],k=[],k.push(ne(x,t[0]));for(var n=0;n<e.handles;n++)p.push(Ge(i,n)),O[n]=n,k.push(ne(x,t[n+1]))}function Ze(t){L(t,e.cssClasses.target),e.dir===0?L(t,e.cssClasses.ltr):L(t,e.cssClasses.rtl),e.ort===0?L(t,e.cssClasses.horizontal):L(t,e.cssClasses.vertical);var i=getComputedStyle(t).direction;return i==="rtl"?L(t,e.cssClasses.textDirectionRtl):L(t,e.cssClasses.textDirectionLtr),F(t,e.cssClasses.base)}function $e(t,i){return!e.tooltips||!e.tooltips[i]?!1:F(t.firstChild,e.cssClasses.tooltip)}function Ce(){return v.hasAttribute("disabled")}function ae(t){var i=p[t];return i.hasAttribute("disabled")}function Qe(t){t!=null?(p[t].setAttribute("disabled",""),p[t].handle.removeAttribute("tabindex")):(v.setAttribute("disabled",""),p.forEach(function(i){i.handle.removeAttribute("tabindex")}))}function et(t){t!=null?(p[t].removeAttribute("disabled"),p[t].handle.setAttribute("tabindex","0")):(v.removeAttribute("disabled"),p.forEach(function(i){i.removeAttribute("disabled"),i.handle.setAttribute("tabindex","0")}))}function se(){H&&(X("update"+N.tooltips),H.forEach(function(t){t&&Re(t)}),H=null)}function Ee(){se(),H=p.map($e),ce("update"+N.tooltips,function(t,i,n){if(!(!H||!e.tooltips)&&H[i]!==!1){var a=t[i];e.tooltips[i]!==!0&&(a=e.tooltips[i].to(n[i])),H[i].innerHTML=a}})}function tt(){X("update"+N.aria),ce("update"+N.aria,function(t,i,n,a,o){O.forEach(function(l){var h=p[l],c=Z(C,l,0,!0,!0,!0),S=Z(C,l,100,!0,!0,!0),w=o[l],E=String(e.ariaFormat.to(n[l]));c=m.fromStepping(c).toFixed(1),S=m.fromStepping(S).toFixed(1),w=m.fromStepping(w).toFixed(1),h.children[0].setAttribute("aria-valuemin",c),h.children[0].setAttribute("aria-valuemax",S),h.children[0].setAttribute("aria-valuenow",w),h.children[0].setAttribute("aria-valuetext",E)})})}function rt(t){if(t.mode===B.Range||t.mode===B.Steps)return m.xVal;if(t.mode===B.Count){if(t.values<2)throw new Error("noUiSlider: 'values' (>= 2) required for mode 'count'.");for(var i=t.values-1,n=100/i,a=[];i--;)a[i]=i*n;return a.push(100),Ae(a,t.stepped)}return t.mode===B.Positions?Ae(t.values,t.stepped):t.mode===B.Values?t.stepped?t.values.map(function(o){return m.fromStepping(m.getStep(m.toStepping(o)))}):t.values:[]}function Ae(t,i){return t.map(function(n){return m.fromStepping(i?m.getStep(n):n)})}function it(t){function i(w,E){return Number((w+E).toFixed(7))}var n=rt(t),a={},o=m.xVal[0],l=m.xVal[m.xVal.length-1],h=!1,c=!1,S=0;return n=Ct(n.slice().sort(function(w,E){return w-E})),n[0]!==o&&(n.unshift(o),h=!0),n[n.length-1]!==l&&(n.push(l),c=!0),n.forEach(function(w,E){var A,g,D,M=w,y=n[E+1],U,de,pe,me,Oe,ge,He,ze=t.mode===B.Steps;for(ze&&(A=m.xNumSteps[E]),A||(A=y-M),y===void 0&&(y=M),A=Math.max(A,1e-7),g=M;g<=y;g=i(g,A)){for(U=m.toStepping(g),de=U-S,Oe=de/(t.density||1),ge=Math.round(Oe),He=de/ge,D=1;D<=ge;D+=1)pe=S+D*He,a[pe.toFixed(5)]=[m.fromStepping(pe),0];me=n.indexOf(g)>-1?_.LargeValue:ze?_.SmallValue:_.NoValue,!E&&h&&g!==y&&(me=0),g===y&&c||(a[U.toFixed(5)]=[g,me]),S=U}}),a}function nt(t,i,n){var a,o,l=q.createElement("div"),h=(a={},a[_.None]="",a[_.NoValue]=e.cssClasses.valueNormal,a[_.LargeValue]=e.cssClasses.valueLarge,a[_.SmallValue]=e.cssClasses.valueSub,a),c=(o={},o[_.None]="",o[_.NoValue]=e.cssClasses.markerNormal,o[_.LargeValue]=e.cssClasses.markerLarge,o[_.SmallValue]=e.cssClasses.markerSub,o),S=[e.cssClasses.valueHorizontal,e.cssClasses.valueVertical],w=[e.cssClasses.markerHorizontal,e.cssClasses.markerVertical];L(l,e.cssClasses.pips),L(l,e.ort===0?e.cssClasses.pipsHorizontal:e.cssClasses.pipsVertical);function E(g,D){var M=D===e.cssClasses.value,y=M?S:w,U=M?h:c;return D+" "+y[e.ort]+" "+U[g]}function A(g,D,M){if(M=i?i(D,M):M,M!==_.None){var y=F(l,!1);y.className=E(M,e.cssClasses.marker),y.style[e.style]=g+"%",M>_.NoValue&&(y=F(l,!1),y.className=E(M,e.cssClasses.value),y.setAttribute("data-value",String(D)),y.style[e.style]=g+"%",y.innerHTML=String(n.to(D)))}}return Object.keys(t).forEach(function(g){A(g,t[g][0],t[g][1])}),l}function oe(){V&&(Re(V),V=null)}function le(t){oe();var i=it(t),n=t.filter,a=t.format||{to:function(o){return String(Math.round(o))}};return V=v.appendChild(nt(i,n,a)),V}function Pe(){var t=b.getBoundingClientRect(),i="offset"+["Width","Height"][e.ort];return e.ort===0?t.width||b[i]:t.height||b[i]}function I(t,i,n,a){var o=function(h){var c=at(h,a.pageOffset,a.target||i);if(!c||Ce()&&!a.doNotReject||Vt(v,e.cssClasses.tap)&&!a.doNotReject||t===f.start&&c.buttons!==void 0&&c.buttons>1||a.hover&&c.buttons)return!1;d||c.preventDefault(),c.calcPoint=c.points[e.ort],n(c,a)},l=[];return t.split(" ").forEach(function(h){i.addEventListener(h,o,d?{passive:!0}:!1),l.push([h,o])}),l}function at(t,i,n){var a=t.type.indexOf("touch")===0,o=t.type.indexOf("mouse")===0,l=t.type.indexOf("pointer")===0,h=0,c=0;if(t.type.indexOf("MSPointer")===0&&(l=!0),t.type==="mousedown"&&!t.buttons&&!t.touches)return!1;if(a){var S=function(A){var g=A.target;return g===n||n.contains(g)||t.composed&&t.composedPath().shift()===n};if(t.type==="touchstart"){var w=Array.prototype.filter.call(t.touches,S);if(w.length>1)return!1;h=w[0].pageX,c=w[0].pageY}else{var E=Array.prototype.find.call(t.changedTouches,S);if(!E)return!1;h=E.pageX,c=E.pageY}}return i=i||Ie(q),(o||l)&&(h=t.clientX+i.x,c=t.clientY+i.y),t.pageOffset=i,t.points=[h,c],t.cursor=o||l,t}function Ve(t){var i=t-At(b,e.ort),n=i*100/Pe();return n=Ne(n),e.dir?100-n:n}function st(t){var i=100,n=!1;return p.forEach(function(a,o){if(!ae(o)){var l=C[o],h=Math.abs(l-t),c=h===100&&i===100,S=h<i,w=h<=i&&t>l;(S||w||c)&&(n=o,i=h)}}),n}function ot(t,i){t.type==="mouseout"&&t.target.nodeName==="HTML"&&t.relatedTarget===null&&fe(t,i)}function lt(t,i){if(navigator.appVersion.indexOf("MSIE 9")===-1&&t.buttons===0&&i.buttonsProperty!==0)return fe(t,i);var n=(e.dir?-1:1)*(t.calcPoint-i.startCalcPoint),a=n*100/i.baseSize;ke(n>0,a,i.locations,i.handleNumbers,i.connect)}function fe(t,i){i.handle&&(ee(i.handle,e.cssClasses.active),ie-=1),i.listeners.forEach(function(n){G.removeEventListener(n[0],n[1])}),ie===0&&(ee(v,e.cssClasses.drag),ve(),t.cursor&&(J.style.cursor="",J.removeEventListener("selectstart",je))),e.events.smoothSteps&&(i.handleNumbers.forEach(function(n){K(n,C[n],!0,!0,!1,!1)}),i.handleNumbers.forEach(function(n){P("update",n)})),i.handleNumbers.forEach(function(n){P("change",n),P("set",n),P("end",n)})}function ue(t,i){if(!i.handleNumbers.some(ae)){var n;if(i.handleNumbers.length===1){var a=p[i.handleNumbers[0]];n=a.children[0],ie+=1,L(n,e.cssClasses.active)}t.stopPropagation();var o=[],l=I(f.move,G,lt,{target:t.target,handle:n,connect:i.connect,listeners:o,startCalcPoint:t.calcPoint,baseSize:Pe(),pageOffset:t.pageOffset,handleNumbers:i.handleNumbers,buttonsProperty:t.buttons,locations:C.slice()}),h=I(f.end,G,fe,{target:t.target,handle:n,listeners:o,doNotReject:!0,handleNumbers:i.handleNumbers}),c=I("mouseout",G,ot,{target:t.target,handle:n,listeners:o,doNotReject:!0,handleNumbers:i.handleNumbers});o.push.apply(o,l.concat(h,c)),t.cursor&&(J.style.cursor=getComputedStyle(t.target).cursor,p.length>1&&L(v,e.cssClasses.drag),J.addEventListener("selectstart",je,!1)),i.handleNumbers.forEach(function(S){P("start",S)})}}function ft(t){t.stopPropagation();var i=Ve(t.calcPoint),n=st(i);n!==!1&&(e.events.snap||Fe(v,e.cssClasses.tap,e.animationDuration),K(n,i,!0,!0),ve(),P("slide",n,!0),P("update",n,!0),e.events.snap?ue(t,{handleNumbers:[n]}):(P("change",n,!0),P("set",n,!0)))}function ut(t){var i=Ve(t.calcPoint),n=m.getStep(i),a=m.fromStepping(n);Object.keys(j).forEach(function(o){o.split(".")[0]==="hover"&&j[o].forEach(function(l){l.call(Q,a)})})}function ct(t,i){if(Ce()||ae(i))return!1;var n=["Left","Right"],a=["Down","Up"],o=["PageDown","PageUp"],l=["Home","End"];e.dir&&!e.ort?n.reverse():e.ort&&!e.dir&&(a.reverse(),o.reverse());var h=t.key.replace("Arrow",""),c=h===o[0],S=h===o[1],w=h===a[0]||h===n[0]||c,E=h===a[1]||h===n[1]||S,A=h===l[0],g=h===l[1];if(!w&&!E&&!A&&!g)return!0;t.preventDefault();var D;if(E||w){var M=w?0:1,y=Le(i),U=y[M];if(U===null)return!1;U===!1&&(U=m.getDefaultStep(C[i],w,e.keyboardDefaultStep)),S||c?U*=e.keyboardPageMultiplier:U*=e.keyboardMultiplier,U=Math.max(U,1e-7),U=(w?-1:1)*U,D=R[i]+U}else g?D=e.spectrum.xVal[e.spectrum.xVal.length-1]:D=e.spectrum.xVal[0];return K(i,m.toStepping(D),!0,!0),P("slide",i),P("update",i),P("change",i),P("set",i),!1}function De(t){t.fixed||p.forEach(function(i,n){I(f.start,i.children[0],ue,{handleNumbers:[n]})}),t.tap&&I(f.start,b,ft,{}),t.hover&&I(f.move,b,ut,{hover:!0}),t.drag&&k.forEach(function(i,n){if(!(i===!1||n===0||n===k.length-1)){var a=p[n-1],o=p[n],l=[i],h=[a,o],c=[n-1,n];L(i,e.cssClasses.draggable),t.fixed&&(l.push(a.children[0]),l.push(o.children[0])),t.dragAll&&(h=p,c=O),l.forEach(function(S){I(f.start,S,ue,{handles:h,handleNumbers:c,connect:i})})}})}function ce(t,i){j[t]=j[t]||[],j[t].push(i),t.split(".")[0]==="update"&&p.forEach(function(n,a){P("update",a)})}function ht(t){return t===N.aria||t===N.tooltips}function X(t){var i=t&&t.split(".")[0],n=i?t.substring(i.length):t;Object.keys(j).forEach(function(a){var o=a.split(".")[0],l=a.substring(o.length);(!i||i===o)&&(!n||n===l)&&(!ht(l)||n===l)&&delete j[a]})}function P(t,i,n){Object.keys(j).forEach(function(a){var o=a.split(".")[0];t===o&&j[a].forEach(function(l){l.call(Q,R.map(e.format.to),i,R.slice(),n||!1,C.slice(),Q)})})}function Z(t,i,n,a,o,l,h){var c;return p.length>1&&!e.events.unconstrained&&(a&&i>0&&(c=m.getAbsoluteDistance(t[i-1],e.margin,!1),n=Math.max(n,c)),o&&i<p.length-1&&(c=m.getAbsoluteDistance(t[i+1],e.margin,!0),n=Math.min(n,c))),p.length>1&&e.limit&&(a&&i>0&&(c=m.getAbsoluteDistance(t[i-1],e.limit,!1),n=Math.min(n,c)),o&&i<p.length-1&&(c=m.getAbsoluteDistance(t[i+1],e.limit,!0),n=Math.max(n,c))),e.padding&&(i===0&&(c=m.getAbsoluteDistance(0,e.padding[0],!1),n=Math.max(n,c)),i===p.length-1&&(c=m.getAbsoluteDistance(100,e.padding[1],!0),n=Math.min(n,c))),h||(n=m.getStep(n)),n=Ne(n),n===t[i]&&!l?!1:n}function he(t,i){var n=e.ort;return(n?i:t)+", "+(n?t:i)}function ke(t,i,n,a,o){var l=n.slice(),h=a[0],c=e.events.smoothSteps,S=[!t,t],w=[t,!t];a=a.slice(),t&&a.reverse(),a.length>1?a.forEach(function(A,g){var D=Z(l,A,l[A]+i,S[g],w[g],!1,c);D===!1?i=0:(i=D-l[A],l[A]=D)}):S=w=[!0];var E=!1;a.forEach(function(A,g){E=K(A,n[A]+i,S[g],w[g],!1,c)||E}),E&&(a.forEach(function(A){P("update",A),P("slide",A)}),o!=null&&P("drag",h))}function ye(t,i){return e.dir?100-t-i:t}function vt(t,i){C[t]=i,R[t]=m.fromStepping(i);var n=ye(i,0)-We,a="translate("+he(n+"%","0")+")";if(p[t].style[e.transformRule]=a,e.events.invertConnects&&C.length>1){var o=C.every(function(l,h,c){return h===0||l>=c[h-1]});if(T!==!o){wt();return}}Y(t),Y(t+1),T&&(Y(t-1),Y(t+2))}function ve(){O.forEach(function(t){var i=C[t]>50?-1:1,n=3+(p.length+i*t);p[t].style.zIndex=String(n)})}function K(t,i,n,a,o,l){return o||(i=Z(C,t,i,n,a,!1,l)),i===!1?!1:(vt(t,i),!0)}function Y(t){if(k[t]){var i=C.slice();T&&i.sort(function(c,S){return c-S});var n=0,a=100;t!==0&&(n=i[t-1]),t!==k.length-1&&(a=i[t]);var o=a-n,l="translate("+he(ye(n,o)+"%","0")+")",h="scale("+he(o/100,"1")+")";k[t].style[e.transformRule]=l+" "+h}}function Ue(t,i){return t===null||t===!1||t===void 0||(typeof t=="number"&&(t=String(t)),t=e.format.from(t),t!==!1&&(t=m.toStepping(t)),t===!1||isNaN(t))?C[i]:t}function $(t,i,n){var a=re(t),o=C[0]===void 0;i=i===void 0?!0:i,e.animate&&!o&&Fe(v,e.cssClasses.tap,e.animationDuration),O.forEach(function(c){K(c,Ue(a[c],c),!0,!1,n)});var l=O.length===1?0:1;if(o&&m.hasNoSize()&&(n=!0,C[0]=0,O.length>1)){var h=100/(O.length-1);O.forEach(function(c){C[c]=c*h})}for(;l<O.length;++l)O.forEach(function(c){K(c,C[c],!0,!0,n)});ve(),O.forEach(function(c){P("update",c),a[c]!==null&&i&&P("set",c)})}function dt(t){$(e.start,t)}function pt(t,i,n,a){if(t=Number(t),!(t>=0&&t<O.length))throw new Error("noUiSlider: invalid handle number, got: "+t);K(t,Ue(i,t),!0,!0,a),P("update",t),n&&P("set",t)}function Me(t){if(t===void 0&&(t=!1),t)return R.length===1?R[0]:R.slice(0);var i=R.map(e.format.to);return i.length===1?i[0]:i}function mt(){for(X(N.aria),X(N.tooltips),Object.keys(e.cssClasses).forEach(function(t){ee(v,e.cssClasses[t])});v.firstChild;)v.removeChild(v.firstChild);delete v.noUiSlider}function Le(t){var i=C[t],n=m.getNearbySteps(i),a=R[t],o=n.thisStep.step,l=null;if(e.snap)return[a-n.stepBefore.startValue||null,n.stepAfter.startValue-a||null];o!==!1&&a+o>n.stepAfter.startValue&&(o=n.stepAfter.startValue-a),a>n.thisStep.startValue?l=n.thisStep.step:n.stepBefore.step===!1?l=!1:l=a-n.stepBefore.highestStep,i===100?o=null:i===0&&(l=null);var h=m.countStepDecimals();return o!==null&&o!==!1&&(o=Number(o.toFixed(h))),l!==null&&l!==!1&&(l=Number(l.toFixed(h))),[l,o]}function gt(){return O.map(Le)}function St(t,i){var n=Me(),a=["margin","limit","padding","range","animate","snap","step","format","pips","tooltips","connect"];a.forEach(function(l){t[l]!==void 0&&(s[l]=t[l])});var o=Ye(s);a.forEach(function(l){t[l]!==void 0&&(e[l]=o[l])}),m=o.spectrum,e.margin=o.margin,e.limit=o.limit,e.padding=o.padding,e.pips?le(e.pips):oe(),e.tooltips?Ee():se(),C=[],$(Se(t.start)?t.start:n,i),t.connect&&_e()}function _e(){for(;x.firstChild;)x.removeChild(x.firstChild);for(var t=0;t<=e.handles;t++)k[t]=ne(x,e.connect[t]),Y(t);De({drag:e.events.drag,fixed:!0})}function wt(){T=!T,Te(e,e.connect.map(function(t){return!t})),_e()}function xt(){b=Ze(v),Je(e.connect,b),De(e.events),$(e.start),e.pips&&le(e.pips),e.tooltips&&Ee(),tt()}xt();var Q={destroy:mt,steps:gt,on:ce,off:X,get:Me,set:$,setHandle:pt,reset:dt,disable:Qe,enable:et,__moveHandles:function(t,i,n){ke(t,i,C,n)},options:s,updateOptions:St,target:v,removePips:oe,removeTooltips:se,getPositions:function(){return C.slice()},getTooltips:function(){return H},getOrigins:function(){return p},pips:le};return Q}function nr(r,e){if(!r||!r.nodeName)throw new Error("noUiSlider: create requires a single element, got: "+r);if(r.noUiSlider)throw new Error("noUiSlider: Slider was already initialized.");var s=Ye(e),f=ir(r,s,e);return r.noUiSlider=f,f}var be={__spectrum:Ke,cssClasses:qe,create:nr};function ar({element:r,start:e,connect:s,range:f={min:0,max:10},state:u,step:d,behaviour:v,snap:b,tooltips:x,onChange:p=console.log}){return{start:e,element:r,connect:s,range:f,component:null,state:u,step:d,behaviour:v,tooltips:x,onChange:p,init(){this.component=document.getElementById(this.element),be.cssClasses.target+=" range-slider";let k=be.create(this.component,{start:window.Alpine.raw(e),connect:window.Alpine.raw(s),range:window.Alpine.raw(f),tooltips:x,step:window.Alpine.raw(d),behaviour:window.Alpine.raw(v),snap:window.Alpine.raw(b)});this.component.noUiSlider.on("update",V=>{console.log("Values :",V),document.addEventListener("livewire:load",function(){setInterval(()=>Livewire.dispatch("nextSlot"),4e3)});for(let H=0;H<V.length;H++)window.Livewire.dispatch(this.state[H],V[H])})}}}export{ar as default};
