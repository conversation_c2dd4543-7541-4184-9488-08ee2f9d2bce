<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MeetingPoint extends Model
{
    protected $fillable = [
        'name',
        'pays',
    ];
    public function activities()
    {
         return $this->belongsToMany(Activity::class);
    }
    public function activitycontract()
    {
         return $this->belongsToMany(ActivityContract::class);
    }

    public function transfercontract()
    {
         return $this->belongsToMany(transfercontract::class,  'meeting_point_transfer_contracts');
    }

}