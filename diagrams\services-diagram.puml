@startuml Travel Platform - Services Architecture
!theme plain
skinparam backgroundColor #FFFFFF
skinparam class {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    FontColor #1A1A1A
    HeaderBackgroundColor #0277BD
    HeaderFontColor #FFFFFF
}
skinparam interface {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
    FontColor #1A1A1A
}
skinparam package {
    BackgroundColor #F1F8E9
    BorderColor #388E3C
    FontColor #1A1A1A
}

title Travel Platform - Architecture des Services

package "Chatbot Services" {
    class ChatbotService {
        -intentDetectionService: IntentDetectionService
        -contextManager: ContextManager
        -databaseQueryService: DatabaseQueryService
        -geminiService: GeminiService
        --
        +processMessage(message: string, sessionId: string): array
        +detectIntent(message: string): array
        +generateResponse(intent: string, entities: array): string
        +searchDatabase(intent: string, entities: array): Collection
        +handleConversation(sessionId: string): void
    }
    
    class IntentDetectionService {
        -geminiService: GeminiService
        -config: array
        -spellingCorrections: array
        --
        +detectIntent(message: string): array
        +extractEntities(message: string, intent: string): array
        +correctSpelling(message: string): string
        +calculateConfidence(result: array): float
        +validateEntities(entities: array, intent: string): bool
    }
    
    class ContextManager {
        -maxContextMessages: int
        -sessionTimeout: int
        -cache: CacheInterface
        --
        +getContext(sessionId: string): array
        +updateContext(sessionId: string, data: array): void
        +clearContext(sessionId: string): void
        +isSessionActive(sessionId: string): bool
        +addMessageToContext(sessionId: string, message: array): void
        +getConversationHistory(sessionId: string): array
    }
    
    class DatabaseQueryService {
        -hotelRepository: HotelRepository
        -activityRepository: ActivityRepository
        -transferRepository: TransferRepository
        -destinationRepository: DestinationRepository
        --
        +searchHotels(criteria: array): Collection
        +searchActivities(criteria: array): Collection
        +searchTransfers(criteria: array): Collection
        +searchDestinations(criteria: array): Collection
        +buildQuery(model: string, criteria: array): Builder
        +applyFilters(query: Builder, filters: array): Builder
    }
}

package "External Services" {
    class GeminiService {
        -apiKey: string
        -model: string
        -client: HttpClient
        -rateLimiter: RateLimiter
        --
        +generateContent(prompt: string, temperature: float): string
        +detectIntent(message: string): array
        +extractEntities(message: string, intent: string): array
        +generateResponse(context: array): string
        +validateApiResponse(response: array): bool
    }
    
    interface GoogleMapsService {
        +geocodeAddress(address: string): array
        +reverseGeocode(lat: float, lng: float): string
        +calculateDistance(origin: array, destination: array): float
        +getPlaceDetails(placeId: string): array
    }
    
    interface EmailService {
        +sendWelcomeEmail(user: User): bool
        +sendSupportNotification(message: SupportMessage): bool
        +sendPasswordReset(user: User, token: string): bool
    }
}

package "Repository Pattern" {
    interface RepositoryInterface {
        +find(id: int): Model
        +findAll(): Collection
        +create(data: array): Model
        +update(id: int, data: array): bool
        +delete(id: int): bool
        +findBy(criteria: array): Collection
    }
    
    class HotelRepository {
        +findByLocation(location: string): Collection
        +findByStars(stars: int): Collection
        +findByDestination(destinationId: int): Collection
        +searchWithFilters(filters: array): Collection
    }
    
    class ActivityRepository {
        +findByType(type: string): Collection
        +findByDifficulty(difficulty: string): Collection
        +findByDuration(duration: string): Collection
        +findNearLocation(lat: float, lng: float, radius: int): Collection
    }
    
    class TransferRepository {
        +findByVehicleType(type: string): Collection
        +findByCapacity(capacity: int): Collection
        +findByRoute(origin: string, destination: string): Collection
    }
    
    class DestinationRepository {
        +findFeatured(): Collection
        +findByCountry(country: string): Collection
        +findPopular(): Collection
    }
}

package "Business Logic" {
    class FavoriteService {
        -favoriteRepository: FavoriteRepository
        --
        +addToFavorites(userId: int, type: string, id: int): bool
        +removeFromFavorites(userId: int, type: string, id: int): bool
        +getUserFavorites(userId: int): array
        +isFavorite(userId: int, type: string, id: int): bool
        +getFavoritesByType(userId: int, type: string): Collection
    }
    
    class SupportService {
        -supportRepository: SupportMessageRepository
        -emailService: EmailService
        --
        +createSupportMessage(data: array): SupportMessage
        +updateMessageStatus(id: int, status: string): bool
        +assignToAgent(messageId: int, agentId: int): bool
        +markAsResolved(messageId: int): bool
        +sendNotifications(message: SupportMessage): void
    }
    
    class UserService {
        -userRepository: UserRepository
        -authService: AuthService
        --
        +createUser(data: array): User
        +updateProfile(userId: int, data: array): bool
        +changePassword(userId: int, newPassword: string): bool
        +deleteAccount(userId: int): bool
        +getUserStatistics(userId: int): array
    }
}

' Relations entre services
ChatbotService --> IntentDetectionService : uses
ChatbotService --> ContextManager : uses
ChatbotService --> DatabaseQueryService : uses
ChatbotService --> GeminiService : uses

IntentDetectionService --> GeminiService : uses
DatabaseQueryService --> HotelRepository : uses
DatabaseQueryService --> ActivityRepository : uses
DatabaseQueryService --> TransferRepository : uses
DatabaseQueryService --> DestinationRepository : uses

' Implémentation des interfaces
HotelRepository ..|> RepositoryInterface : implements
ActivityRepository ..|> RepositoryInterface : implements
TransferRepository ..|> RepositoryInterface : implements
DestinationRepository ..|> RepositoryInterface : implements

' Services métier
FavoriteService --> RepositoryInterface : uses
SupportService --> EmailService : uses
UserService --> RepositoryInterface : uses

@enduml
