<div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-shadow duration-300 {{ $type === 'cancelled' ? 'opacity-75' : '' }}">
    <!-- Activity Image -->
    <div class="relative h-48 overflow-hidden rounded-t-xl">
        @if ($booking->activity->image)
            <img src="{{ asset('storage/' . $booking->activity->image) }}"
                class="w-full h-full object-cover {{ $type === 'cancelled' ? 'grayscale' : '' }}" 
                alt="{{ $booking->activity->name }}">
        @else
            <div class="w-full h-full bg-gradient-to-r {{ $type === 'cancelled' ? 'from-gray-400 to-gray-500' : 'from-pink-400 to-purple-500' }} flex items-center justify-center">
                <span class="text-white text-3xl">
                    @if($type === 'cancelled')
                        ❌
                    @else
                        🎯
                    @endif
                </span>
            </div>
        @endif

        <!-- Status Badge -->
        <div class="absolute top-3 right-3">
            @if ($type === 'confirmed')
                <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                    Confirmed
                </span>
            @elseif ($type === 'pending')
                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                    Pending
                </span>
            @else
                <span class="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                    Cancelled
                </span>
            @endif
        </div>
    </div>

    <!-- Booking Details -->
    <div class="p-6">
        <!-- Activity Info -->
        <div class="mb-4">
            <h3 class="font-bold text-lg text-gray-900 mb-1 line-clamp-2">
                {{ $booking->activity->name }}
            </h3>
            <p class="text-sm text-gray-500">
                {{ $booking->activity->destination?->name ?? 'Activity' }}
            </p>
        </div>

        <!-- Booking Details -->
        <div class="space-y-2 mb-4">
            <!-- Date & Time -->
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                    </path>
                </svg>
                {{ $booking->booking_date->format('M j, Y') }} at {{ $booking->booking_time }}
            </div>

            <!-- People -->
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                </svg>
                {{ $booking->adults }} adult{{ $booking->adults > 1 ? 's' : '' }}
                @if ($booking->children > 0)
                    + {{ $booking->children }} child{{ $booking->children > 1 ? 'ren' : '' }}
                @endif
            </div>

            <!-- Price -->
            <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                    </path>
                </svg>
                ${{ number_format($booking->total_price, 0) }} total
            </div>
        </div>

        <!-- Actions -->
        <div class="flex gap-2">
            <a href="{{ route('activities.show', $booking->activity->id) }}"
                class="flex-1 bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                View Details
            </a>

            @if ($type === 'pending')
                <button wire:click="completePayment({{ $booking->id }})"
                    class="flex-1 bg-green-100 text-green-700 text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors">
                    💳 Complete Payment
                </button>
            @elseif ($type === 'confirmed')
                <button wire:click="cancelBooking({{ $booking->id }})"
                    onclick="return confirm('Are you sure you want to cancel this booking?')"
                    class="flex-1 bg-red-100 text-red-700 text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors">
                    Cancel
                </button>
            @endif
        </div>
    </div>
</div>
