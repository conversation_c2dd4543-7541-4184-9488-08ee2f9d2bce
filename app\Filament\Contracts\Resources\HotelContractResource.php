<?php

namespace App\Filament\Contracts\Resources;

use App\Enums\ContractStatus;
use App\Enums\Currency;
use App\Enums\RoomStatus;
use App\Filament\Contracts\Resources\HotelContractResource\Pages;
use App\Filament\Contracts\Resources\HotelContractResource\RelationManagers;
use App\Models\Hotel;
use App\Models\Hotel_Contract;
use App\Models\HotelContract;
use Filament\Forms;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Split;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ToggleButtons;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Closure;
use Doctrine\DBAL\Schema\Schema;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class HotelContractResource extends Resource
{
    protected static ?string $model = Hotel_Contract::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // First section: Main Fields (Contract Number, Type, Description, etc.)
                Section::make('Hotel Details')
                    ->schema([
                        Tabs::make('Tabs')
                            ->columnSpan('full')
                            ->tabs([
                                Tabs\Tab::make('Hotel')
                                    ->columns(3)
                                    ->schema([
                                        TextInput::make('contract_number')
                                            ->label('Contract Number')
                                            ->required()

                                            ->columnSpan(2),
                                        Forms\Components\Select::make('type')
                                            ->required()
                                            ->options([
                                                'FIT' => 'FIT',
                                                'Dynamic Rates' => 'Dynamic Rates',
                                                'Group Rate Plan' => 'Group Rate Plan',
                                                'Package Rate Plan' => 'Package Rate Plan',
                                                'Fly & Drive Rate Plan' => 'Fly & Drive Rate Plan',
                                            ])
                                            ->placeholder('Sélectionnez un type'),

                                        Forms\Components\Toggle::make('direct_supplier')
                                            ->live()
                                            ->default(true),
                                        Grid::make(2)->schema([

                                            Forms\Components\Select::make('intermediate_supplier')

                                                ->options([
                                                    'test1' => 'test1',
                                                    'test2' => 'test2',
                                                ])

                                                ->placeholder('Sélectionnez un point'),
                                        ])->hidden(function (Get $get) {
                                            return $get('direct_supplier') ? true : false;
                                        }),
                                        Forms\Components\Select::make('main_supplier')
                                            ->required()
                                            ->options([
                                                'test1' => 'test1',
                                                'test2' => 'test2',
                                            ])
                                            ->placeholder('Sélectionnez une option'),



                                        Forms\Components\Select::make('hotels_chains')
                                            ->required()
                                            ->options([
                                                'test3' => 'test3',
                                                'test4' => 'test4',
                                            ])
                                            ->placeholder('Sélectionnez une option'),

                                        Forms\Components\Select::make('hotel_id')
                                            ->label('hotels')
                                            ->required()
                                            ->live()
                                            ->relationship('hotel', 'name')

                                            ->placeholder('Sélectionnez une option'),

                                        Forms\Components\Textarea::make('description')->required()->columnSpanFull(),

                                        Forms\Components\Select::make('markets')
                                            ->label('Market(s)')
                                            ->multiple()
                                            ->relationship('markets', 'name')
                                            ->preload()
                                            ->searchable()
                                            ->columnSpanFull()
                                            ->required(),
                                        Section::make("what's included / what'is not included")
                                            ->schema([
                                                Repeater::make('whats_included')

                                                    ->simple(

                                                        Forms\Components\Select::make('whats_included')
                                                            ->options([
                                                                'Ambiance musicale' => 'Ambiance musicale',
                                                                'Tables avec vue panoramique' => 'Tables avec vue panoramique',
                                                                'Prise de photos autorisée' => 'Prise de photos autorisée',
                                                                'nouveau incluede services' => 'nouveau incluede services',
                                                                'Nourriture et boissons' => 'Nourriture et boissons',
                                                                'Commentaire audio depuis votre appareil mobile' => 'Commentaire audio depuis votre appareil mobile',
                                                                'Spectacles' => 'Spectacles',
                                                                'Zone de selfies avec les personnages' => 'Zone de selfies avec les personnages',
                                                                'Sièges extérieurs et intérieurs' => 'Sièges extérieurs et intérieurs',
                                                            ]),

                                                    )->columnSpan(2),


                                                Repeater::make('whats_not_included')
                                                    ->simple(
                                                        Forms\Components\Select::make('whats_not_included')
                                                            ->options([
                                                                'Les animaux ne sont pas acceptés' => 'Les animaux ne sont pas acceptés',
                                                                'Deposit amount must be paid on site and is not linked to the use of the Application' => 'Deposit amount must be paid on site and is not linked to the use of the Application',
                                                                'Nourriture et boissons' => 'Nourriture et boissons',
                                                                'Prise en charge et retour à l’hôtel' => 'Prise en charge et retour à l’hôtel',
                                                            ])
                                                    )->columnSpan(2)

                                            ])

                                    ]),

                                Tabs\Tab::make('Rooms')
                                    ->schema([

                                        Placeholder::make('Rooms per Hotel'),

                                        Placeholder::make('hotel_name_preview')
                                            ->label('Selected Hotel')
                                            ->content(function (Get $get) {
                                                $hotelId = $get('hotel_id');
                                            return $hotelId ? \App\Models\Hotel::find($hotelId)?->name : '-';
                                            })
                                            ->visible(fn (Get $get) => filled($get('hotel_id')))
                                            ->reactive(),

                                        TableRepeater::make('rooms')
                                        ->hidden(function(Get $get){
                                            if ($get('hotel_id')!=null)
                                                return false;
                                            return  true;
                                        })
                                            ->relationship()
                                            ->columns(2)
                                            ->headers([
                                                Header::make('name'),

                                                Header::make('Allocations'),
                                                Header::make('Occupancies'),


                                                Header::make('status'),
                                            ])
                                            ->schema([
                                                TextInput::make('name'),
                                                Actions::make([
                                                    Action::make('allocation')
                                                    ->fillForm(function (Get $get, $data, $record) {
                                                        // Get allocation data from the record, not from form context
                                                        $dataallocation = $record ? $record->allocation : null;

                                                        // Check if it's a string and decode it
                                                        if (is_string($dataallocation)) {
                                                            $dataallocation = json_decode($dataallocation, true);
                                                        }

                                                        $array_geht_zu_form = [];
                                                        if (is_array($dataallocation) && !empty($dataallocation)) {
                                                            foreach ($dataallocation as $key => $hh) {
                                                                $array_geht_zu_form[] = [
                                                                    'season' => $hh['season'] ?? '',
                                                                    'start_date_allocation' => $hh['start_date_allocation'] ?? '',
                                                                    'end_date_allocation' => $hh['end_date_allocation'] ?? '',
                                                                    'allocation_type' => $hh['allocation_type'] ?? '',
                                                                    'release' => $hh['release'] ?? '',
                                                                    'price' => $hh['price'] ?? '',
                                                                ];
                                                            }
                                                        }

                                                        return [
                                                            'Allocations' => $array_geht_zu_form,
                                                        ];
                                                    })

                                                        ->action(function ($record, $data, Get $get) {
                                                            // Check if record exists
                                                            if (!$record) {
                                                                // If no record exists, we need to create one first
                                                                // This should not happen if the room is properly saved first
                                                                throw new \Exception('Room must be saved before adding allocations. Please save the room first.');
                                                            }

                                                            // Wrap the single item in an array to match TableRepeater expectations
                                                            $record->allocation = $data['Allocations'];
                                                            $record->save();
                                                        })

                                                        ->form([
                                                        Repeater::make("Allocations")
                                                            ->schema([

                                                                TextInput::make('season')
                                                                    ->required(),

                                                                Forms\Components\DatePicker::make('start_date_allocation')
                                                                    ->label('Start Date')
                                                                    ->required()
                                                                    ->default(now())
                                                                    ->displayFormat('Y-m-d')
                                                                    ->columnSpan(1)
                                                                    ->minDate(fn (callable $get) => $get('../../start_date')), // show restriction in UI


                                                                Forms\Components\DatePicker::make('end_date_allocation')
                                                                    ->label('End Date')
                                                                    ->required()
                                                                    ->default(now())
                                                                    ->displayFormat('Y-m-d')
                                                                    ->columnSpan(1)
                                                                    ->rules([
                                                                        function (callable $get) {
                                                                            return function ($attribute, $value, $fail) use ($get) {
                                                                                $start = $get('start_date_allocation');
                                                                                if ($start && $value < $start) {
                                                                                    $fail('The end date must be after the start date.');
                                                                                }
                                                                            };
                                                                        }
                                                                    ]),

                                                                Forms\Components\Select::make('allocation_type')
                                                                    ->required()
                                                                    ->options([
                                                                        'Allocation (stock)' => 'Allocation (stock)',
                                                                        'Sale And Report' => 'Sale And Report',
                                                                        'On Request' => 'On Request',
                                                                        'Free Sale' => 'Free Sale',
                                                                    ])->columnSpan(1),
                                                                Forms\Components\TextInput::make('release')
                                                                    ->numeric()
                                                                    ->required()
                                                                    ->columnSpan(1),

                                                                Forms\Components\TextInput::make('price')
                                                                    ->numeric()
                                                                    ->required()
                                                                    ->columnSpan(1),

                                                            ])->columns(6),


                                                    ]),

                                                ]),
                                                Actions::make([
                                                    Action::make('occupancy')
                                                        ->modalWidth('full')
                                                        ->fillForm(
                                                            function (Get $get, $data, $record) {
                                                                // Get occupancy data from the record, not from form context
                                                                $dataoccupancy = $record ? $record->occupancy : null;

                                                                $array_geht_zu_form = [];
                                                                if (is_array($dataoccupancy) && !empty($dataoccupancy)) {
                                                                    foreach ($dataoccupancy as $key => $hh) {
                                                                        $array_geht_zu_form[] = [
                                                                            'pax_min_capacity' => is_array($hh) ? $hh['pax_min_capacity'] : '' ,
                                                                            'pax_max_capacity' => is_array($hh) ? $hh['pax_max_capacity'] : '',
                                                                            'adult_min_capacity' => is_array($hh) ? $hh['adult_min_capacity'] : '',
                                                                            'adult_max_capacity' => is_array($hh) ? $hh['adult_max_capacity'] : '',
                                                                            'children_max_capacity' => is_array($hh) ? $hh['children_max_capacity'] : '',
                                                                            'infants_max_capacity' => is_array($hh) ? $hh['infants_max_capacity'] : '',
                                                                            'max_extra_bed' => is_array($hh) ? $hh['max_extra_bed'] : '',
                                                                            'max_extra_children' => is_array($hh) ? $hh['max_extra_children'] : '',
                                                                            'max_extra_cots' => is_array($hh) ? $hh['max_extra_cots'] : '',
                                                                        ];
                                                                    }
                                                                }

                                                                return [
                                                                    'occupancies' => $array_geht_zu_form,
                                                                ];
                                                            }
                                                        )
                                                        ->action(function ($record, $data, Get $get) {
                                                            // Check if record exists
                                                            if (!$record) {
                                                                // If no record exists, we need to create one first
                                                                // This should not happen if the room is properly saved first
                                                                throw new \Exception('Room must be saved before adding occupancy. Please save the room first.');
                                                            }

                                                            // The data comes directly from the TableRepeater
                                                            if (isset($data['occupancies'])) {
                                                                $record->occupancy = $data['occupancies'];
                                                            } else {
                                                                // If no 'occupancies' key, the data might be the occupancies directly
                                                                $record->occupancy = $data;
                                                            }
                                                            $record->save();
                                                        })
                                                        ->form([
                                                            TableRepeater::make('occupancies')
                                                                ->headers([
                                                                    Header::make('Pax Capacity'),
                                                                    Header::make('Adult Capacity'),
                                                                    Header::make('Children Capacity'),
                                                                    Header::make('Infants Capacity'),
                                                                    Header::make('Extra Capacity'),
                                                                ])
                                                                ->schema([

                                                                    Grid::make('1')

                                                                        ->schema([
                                                                            Forms\Components\TextInput::make('pax_min_capacity')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                            Forms\Components\TextInput::make('pax_max_capacity')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                        ]),
                                                                    Grid::make('1')

                                                                        ->schema([
                                                                            Forms\Components\TextInput::make('adult_min_capacity')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                            Forms\Components\TextInput::make('adult_max_capacity')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                        ]),
                                                                        Grid::make('1')
                                                                        ->schema([
                                                                    Forms\Components\TextInput::make('children_max_capacity')
                                                                        ->numeric()
                                                                        ->required()
                                                                        ->columnSpan(1),


                                                                    Forms\Components\TextInput::make('infants_max_capacity')
                                                                        ->numeric()
                                                                        ->required()
                                                                        ->columnSpan(1),
                                                                ]),
                                                                    Grid::make('1')

                                                                        ->schema([
                                                                            Forms\Components\TextInput::make('max_extra_bed')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                            Forms\Components\TextInput::make('max_extra_children')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                            Forms\Components\TextInput::make('max_extra_cots')
                                                                                ->numeric()
                                                                                ->required()
                                                                                ->columnSpan(1),
                                                                        ])
                                                                ])
                                                                ->columns(5),


                                                                        ])

                                                ])->columns(2),

                                                ToggleButtons::make('status')
                                                    ->inline()
                                                    ->options(RoomStatus::class)



                                            ])->columns(5),


                                                                        ]),

                                    Tabs\Tab::make('Supplement & special offers')
                                ->schema([

                                    Placeholder::make('Supplement & special offers per Hotel'),

                                    Placeholder::make('hotel_name_preview')
                                    ->label('Selected Hotel')
                                    ->content(function (Get $get) {
                                        $hotelId = $get('hotel_id');
                                    return $hotelId ? \App\Models\Hotel::find($hotelId)?->name : '-';
                                    })
                                    ->visible(fn (Get $get) => filled($get('hotel_id')))
                                    ->reactive(),


                                     TableRepeater::make('special_offers')

                                        ->hidden(function(Get $get){
                                            if ($get('hotel_id')!=null)
                                                return false;
                                            return  true;
                                        })
                                        ->columns(2)
                                        ->headers([
                                            Header::make('Special offer'),
                                            Header::make('Period Start'),
                                            Header::make('Period End'),
                                            Header::make('Show Offer'),

                                            Header::make('Actions'),
                                        ])
                                        ->schema([
                                            TextInput::make('special_offer'),
                                            DatePicker::make('period_start')->label('Period Start'),
                                            DatePicker::make('period_end')->label('Period End'),

                                            Actions::make([
                                            Action::make('offer_details')
                                            ->label('Show Offer Details')
                                            ->icon('heroicon-o-information-circle')
                                            ->modalHeading('Offer Details')
                                            ->modalCancelActionLabel('Close')
                                            ->modalSubmitActionLabel('Save Offer Details')
                                            ->modalWidth('7xl')
                                            ->fillForm(function (Get $get, $data, $record) {
                                                // Get offer details from the record, not from form context (like policies)
                                                $dataspecial_offers = $record ? $record->special_offers : null;

                                                // Check if it's a string and decode it
                                                if (is_string($dataspecial_offers)) {
                                                    $dataspecial_offers = json_decode($dataspecial_offers, true);
                                                }

                                                // Initialize form data
                                                $formData = [
                                                    'room_type' => [],
                                                    'applies_on' => [],
                                                    'offers' => [],
                                                    'types_organization' => '',
                                                    'conditions' => [],
                                                ];

                                                if (is_array($dataspecial_offers) && !empty($dataspecial_offers)) {
                                                    foreach ($dataspecial_offers as $offer) {
                                                        // Extract offer_details from saved offers
                                                        if (isset($offer['offer_details']) && is_array($offer['offer_details'])) {
                                                            $offerDetails = $offer['offer_details'];

                                                            // Extract top-level fields
                                                            $formData['room_type'] = $offerDetails['room_type'] ?? [];
                                                            $formData['applies_on'] = $offerDetails['applies_on'] ?? [];
                                                            $formData['types_organization'] = $offerDetails['types_organization'] ?? '';
                                                            $formData['conditions'] = $offerDetails['conditions'] ?? [];

                                                            // Extract offers repeater data
                                                            if (isset($offerDetails['offers']) && is_array($offerDetails['offers'])) {
                                                                $formData['offers'] = $offerDetails['offers'];
                                                            }

                                                            break; // Take the first offer_details found
                                                        }
                                                    }
                                                }

                                                return $formData;
                                            })
                                            ->action(function ($record, $data, Get $get) {
                                                // Check if record exists (like policies)
                                                if (!$record) {
                                                    throw new \Exception('Record must be saved before adding offer details. Please save the contract first.');
                                                }

                                                // Get current special offers and find/update the offer_details section
                                                $currentOffers = $record->special_offers ?? [];

                                                // Prepare the complete offer details data
                                                $offerDetailsData = [
                                                    'room_type' => $data['room_type'] ?? [],
                                                    'applies_on' => $data['applies_on'] ?? [],
                                                    'offers' => $data['offers'] ?? [],
                                                    'types_organization' => $data['types_organization'] ?? '',
                                                    'conditions' => $data['conditions'] ?? [],
                                                ];

                                                // Find existing offer_details entry or create new one
                                                $offerDetailsFound = false;
                                                foreach ($currentOffers as &$offer) {
                                                    if (isset($offer['offer_details'])) {
                                                        // Update existing offer details with ALL form data
                                                        $offer['offer_details'] = $offerDetailsData;
                                                        $offer['updated_at'] = now()->toDateTimeString();
                                                        $offerDetailsFound = true;
                                                        break;
                                                    }
                                                }

                                                // If no offer_details found, create a new entry
                                                if (!$offerDetailsFound) {
                                                    $currentOffers[] = [
                                                        'id' => uniqid('offer_', true),
                                                        'period_start' => null,
                                                        'period_end' => null,
                                                        'special_offer' => 'Offer Details',
                                                        'offer_details' => $offerDetailsData,
                                                        'created_at' => now()->toDateTimeString()
                                                    ];
                                                }

                                                // Save to record (like policies)
                                                $record->special_offers = $currentOffers;
                                                $record->save();

                                                // Success notification
                                                Notification::make()
                                                    ->title('Offer details saved successfully!')
                                                    ->success()
                                                    ->send();
                                            })
                                            ->form([
                                                section::make(null)
                                                ->Schema([



                                                Forms\Components\Select::make('room_type')->label('Room Type')
                                                ->required()
                                                ->multiple()
                                                ->columnSpan(1)
                                                ->options(function (Get $get, $record) {
                                                    // Get room names from the current contract's rooms
                                                    if (!$record) {
                                                        return ['no_record' => 'Please save the contract first to see rooms'];
                                                    }

                                                    // Get rooms using query builder approach (most reliable)
                                                    try {
                                                        $rooms = \App\Models\Room::where('hotel_contract_id', $record->id)->get();

                                                        if ($rooms->isEmpty()) {
                                                            return ['no_rooms' => 'No rooms found. Please add rooms in the Rooms tab first.'];
                                                        }

                                                        // Create options array with room names
                                                        $options = [];
                                                        foreach ($rooms as $room) {
                                                            if (!empty($room->name)) {
                                                                $options[$room->name] = $room->name;
                                                            }
                                                        }

                                                        return $options ?: ['no_valid_rooms' => 'No valid room names found'];

                                                    } catch (\Exception $e) {
                                                        return ['error' => 'Error loading rooms: ' . $e->getMessage()];
                                                    }
                                                })
                                                ->placeholder('Select room types')
                                                ->helperText('Room names are fetched from the Rooms tab'),
                                                Forms\Components\Select::make('applies_on')->label('Applies On')
                                                ->required()
                                                ->multiple()
                                                ->columnSpan(1)
                                                ->options([
                                                    'unit_rates' => 'Unit Rates',
                                                    'board' => 'Board',
                                                    'extras' => 'Extras',

                                                ]),
                                                Repeater::make('offers')
                                                    ->schema([

                                                        Forms\Components\Select::make('type_pax')->label('Type Pax')

                                                        ->required()
                                                        ->columnSpan(1)
                                                        ->options([
                                                            'adult' => 'Adult',
                                                            'child' => 'Child',
                                                            'infant' => 'Infant',
                                                        ]),

                                                        Forms\Components\Select::make('level')->label('Level')
                                                        ->required()
                                                        ->columnSpan(1)
                                                        ->options([
                                                            'all' => 'All',
                                                            'first' => 'First',
                                                            'second' => 'Second',
                                                            'third' => 'Third',
                                                            'fourth' => 'Fourth',
                                                        ]),
                                                        Forms\Components\Select::make('type_reduction')
                                                                ->label('Type Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->options([
                                                                    'flat' => 'Flat',
                                                                    'percentage' => '%',
                                                                ])
                                                                ->live(), // Add this to trigger reactivity
                                                            Forms\Components\TextInput::make('percentage_rate')
                                                                ->numeric()
                                                                ->label('Percentage')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'percentage'),

                                                            Forms\Components\TextInput::make('reduction')
                                                                ->numeric()
                                                                ->label('Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'flat'),

                                                            Forms\Components\Select::make('currency')
                                                                ->label('Currency')
                                                                ->options(Currency::class)
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'flat'),

                                                                ])->columns(4),
                                                Forms\Components\Select::make('types_organization')->label('Types organization of conditions (AND/OR)')
                                                                ->required()
                                                                ->options([
                                                                    'and' => 'And',
                                                                    'or' => 'OR',

                                                                ]),
                                                Repeater::make('conditions')
                                                    ->schema([
                                                            Forms\Components\Select::make('condition_type')->label('Condition Type')
                                                            ->required()
                                                            ->columnSpan(3)
                                                            ->live()
                                                            ->options([
                                                                'age' => 'Age',
                                                                'stays' => 'Stays',
                                                                'days_before_checking' => 'Days before checking',
                                                                'booking_date' => 'Booking date',
                                                            ]),

                                                            Forms\Components\Select::make('comparison_type')
                                                                ->label('Comparison Type')
                                                                ->required()
                                                                ->columnSpan(2)
                                                                ->options([
                                                                    'before' => 'Before',
                                                                    'after' => 'After',
                                                                    'equal' => 'Equal',
                                                                    'not_equal' => 'Not Equal',
                                                                    'between' => 'Between',
                                                                    'not_between' => 'Not Between',
                                                                ])
                                                                ->live(),
                                                            TextInput::make('value')->required()->numeric()->label('Value')->columnSpan(1)
                                                            ->visible(fn (Get $get) =>
                                                            in_array($get('condition_type'), ['age', 'stays', 'days_before_checking'])
                                                              ),

                                                            TextInput::make('second_value')->required()->numeric()->label('Second Value')->columnSpan(1)
                                                              ->visible(fn (Get $get) =>
                                                              in_array($get('condition_type'), ['age', 'stays', 'days_before_checking']) &&
                                                              in_array($get('comparison_type'), ['between', 'not_between'])
                                                            ),


                                                            DatePicker::make('value_date')
                                                                ->label('Value')
                                                                ->required()

                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) =>
                                                                    in_array($get('condition_type'), ['booking_date'])
                                                                ),
                                                                DatePicker::make('second_value_date')
                                                                ->label('Second Value')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) =>
                                                                    in_array($get('condition_type'), ['booking_date']) &&
                                                                    in_array($get('comparison_type'), ['between', 'not_between'])
                                                                )


                                                            ])->columns(4)


                                            ]),






                                            // Add more fields if needed
                                            ])


                                            ]),
                                        ])


                                ]),


                                Tabs\Tab::make('Policies')
                                   /* ->hidden(function($operation){
                                        if ($operation=='create')
                                        return true;
                                    return false;
                                    })*/


                                    ->schema([

                                            Placeholder::make('Policies per Hotel')
                                            ,

                                            Placeholder::make('hotel_name_preview')
                                            ->label('Selected Hotel')
                                            ->content(function (Get $get) {
                                                $hotelId = $get('hotel_id');
                                            return $hotelId ? \App\Models\Hotel::find($hotelId)?->name : '-';
                                            })
                                            ->visible(fn (Get $get) => filled($get('hotel_id')))
                                            ->reactive(),

                                            TableRepeater::make('policies')
                                                ->hidden(function(Get $get){
                                                if ($get('hotel_id')!=null)
                                                  return false;
                                                return  true;
                                                })
                                                ->columns(2)
                                                ->headers([
                                                    Header::make('Period Start'),
                                                    Header::make('Period End'),
                                                    Header::make('Policies'),
                                                    Header::make('Show Policies Details'),
                                                    Header::make('Actions'),
                                                ])
                                                ->schema([
                                                    DatePicker::make('period_start')->label('Period Start'),
                                                    DatePicker::make('period_end')->label('Period End'),
                                                    TextInput::make('pol'),
                                                    Actions::make([

                                                        Action::make('show_policies_details')
                                                            ->label('Show Policies Details')
                                                            ->icon('heroicon-o-information-circle')
                                                            ->modalHeading('Policies Details')
                                                            ->modalCancelActionLabel('Close')
                                                            ->modalSubmitActionLabel('Save Policy Details')
                                                            ->modalWidth('7xl')

                                                                ->fillForm(function (Get $get, $data, $record) {
                                                                    // Get policy details from the record, not from form context (like allocations and occupancy)
                                                                    $datapolicies = $record ? $record->policies : null;

                                                                    // Check if it's a string and decode it
                                                                    if (is_string($datapolicies)) {
                                                                        $datapolicies = json_decode($datapolicies, true);
                                                                    }

                                                                    $array_geht_zu_form = [];
                                                                    if (is_array($datapolicies) && !empty($datapolicies)) {
                                                                        foreach ($datapolicies as $policy) {
                                                                            // Only extract policy_details from saved policies
                                                                            if (isset($policy['policy_details']) && is_array($policy['policy_details'])) {
                                                                                foreach ($policy['policy_details'] as $detail) {
                                                                                    $array_geht_zu_form[] = [
                                                                                        'types_policies' => $detail['types_policies'] ?? [],
                                                                                        'type_charge' => $detail['type_charge'] ?? '',
                                                                                        'days_before_checkin' => $detail['days_before_checkin'] ?? '',
                                                                                        'charge_type' => $detail['charge_type'] ?? '',
                                                                                        'value_percentage' => $detail['value_percentage'] ?? '',
                                                                                        'value' => $detail['value'] ?? '',
                                                                                        'currency' => $detail['currency'] ?? '',
                                                                                        'apply_to' => $detail['apply_to'] ?? '',
                                                                                    ];
                                                                                }
                                                                            }
                                                                        }
                                                                    }

                                                                    return [
                                                                        'policy' => $array_geht_zu_form,
                                                                    ];
                                                                })

                                                        ->action(function ($record, $data, Get $get) {
                                                            // Check if record exists (like allocations and occupancy)
                                                            if (!$record) {
                                                                throw new \Exception('Record must be saved before adding policy details. Please save the contract first.');
                                                            }

                                                            // Get current policies and find/update the policy_details section
                                                            $currentPolicies = $record->policies ?? [];

                                                            // Find existing policy_details entry or create new one
                                                            $policyDetailsFound = false;
                                                            foreach ($currentPolicies as &$policy) {
                                                                if (isset($policy['policy_details'])) {
                                                                    // Update existing policy details
                                                                    $policy['policy_details'] = $data['policy'];
                                                                    $policy['updated_at'] = now()->toDateTimeString();
                                                                    $policyDetailsFound = true;
                                                                    break;
                                                                }
                                                            }

                                                            // If no policy_details found, create a new entry
                                                            if (!$policyDetailsFound) {
                                                                $currentPolicies[] = [
                                                                    'id' => uniqid('policy_', true),
                                                                    'period_start' => null,
                                                                    'period_end' => null,
                                                                    'pol' => 'Policy Details',
                                                                    'policy_details' => $data['policy'],
                                                                    'created_at' => now()->toDateTimeString()
                                                                ];
                                                            }

                                                            // Save to record (like allocations and occupancy)
                                                            $record->policies = $currentPolicies;
                                                            $record->save();

                                                            // Success notification
                                                            Notification::make()
                                                                ->title('Policy details saved successfully!')
                                                                ->success()
                                                                ->send();
                                                        })
                                                            ->form([
                                                                Section::make(null)
                                                                    ->schema([


                                                                                Repeater::make('policy')
                                                                                ->schema([

                                                                                Forms\Components\Select::make('types_policies')->label('Types Policies')
                                                                                    ->multiple()
                                                                                    ->required()
                                                                                    ->options([
                                                                                        'cancelation' => 'Cancelation',
                                                                                        'modification' => 'Modification',
                                                                                        'no_show' => 'No Show',
                                                                                    ])->columnSpan(3),

                                                                                Forms\Components\Select::make('type_charge')->label('Type Charge')
                                                                                    ->required()
                                                                                    ->options([
                                                                                        'charge' => 'Charge',
                                                                                        'extra' => 'Extra',


                                                                                    ])->columnSpan(1),

                                                                                Forms\Components\TextInput::make('days_before_checkin')
                                                                                ->columnSpan(1)->numeric()->required()->label('Days before CheckIn'),

                                                                                Forms\Components\Select::make('charge_type')->label('Charge Type')
                                                                                    ->required()
                                                                                    ->live()
                                                                                    ->options([
                                                                                        'free' => 'Free',
                                                                                        'night' => 'Night',
                                                                                        'percentage' => 'Percentage',
                                                                                        'flat' => 'Flat',
                                                                                        'none_refundable' => 'None Refundable',

                                                                                    ])->columnSpan(2),

                                                                                Forms\Components\TextInput::make('value_percentage')->numeric()
                                                                                ->label('Value (Percentage)')
                                                                                ->columnSpan(1)
                                                                                ->visible(fn (Get $get) =>
                                                                                in_array($get('charge_type'), ['percentage'])
                                                                                ),
                                                                                Forms\Components\TextInput::make('value')->numeric()
                                                                                ->visible(fn (Get $get) =>
                                                                                in_array($get('charge_type'), ['flat', 'night'])
                                                                                )
                                                                                ->label('Value')->columnSpan(1),
                                                                                Forms\Components\Select::make('currency')->label('Currency')->columnSpan(1)
                                                                                    ->options(Currency::class)
                                                                                    ->visible(fn (Get $get) =>
                                                                                in_array($get('charge_type'), ['flat'])
                                                                                    ),
                                                                                Forms\Components\Select::make('apply_to')->label('Apply To')
                                                                                    ->required()
                                                                                    ->options([
                                                                                        'total_stay' => 'Total stay',
                                                                                        'first_night' => 'First night',
                                                                                    ])->columnSpan(1),

                                                                            ])->columns(4)
                                                                            ->columnSpan(1),




                                                            ])
                                                            ])

                                                         ]),
                                                 ])

                                    ]),
                                Tabs\Tab::make('Channels')
                                    ->schema([
                                        Section::make("Channels contract")
                                            ->schema([
                                                Repeater::make('channels contract')
                                                    ->schema([
                                                        Forms\Components\Select::make('channels')
                                                            ->required()
                                                            ->options([
                                                                'test1' => 'test1',
                                                                'test2' => 'test2',
                                                            ])->columnSpan(1)
                                                            ->placeholder('Sélectionnez une option'),

                                                        Forms\Components\Select::make('commission')
                                                            ->required()
                                                            ->options([
                                                                'test1' => 'test1',
                                                                'test2' => 'test2',
                                                            ])->columnSpan(1)
                                                            ->placeholder('Sélectionnez une option'),
                                                    ])->columns(2),
                                            ])->columnSpanFull()

                                    ]),

                            ])

                    ])
                    ->columns(3)
                    ->columnSpan(3),

                // Second section: Radio Buttons and Date Picker (Top-Right Position)
                Section::make('Settings')
                    ->schema([
                        // Radio Button for contract status
                        ToggleButtons::make('contract_status')
                            ->label('Contract Status')
                            ->options(ContractStatus::class)
                            ->default('signed')

                            ->required(),

                        // Date Picker for contract date
                        DatePicker::make('signed_date')
                            ->label('Signed Date')

                            ->default(now())
                            ->displayFormat('Y-m-d'),

                        DatePicker::make('start_date')
                            ->label('Start Date')
                            ->required()
                            ->default(now())
                            ->displayFormat('Y-m-d')
                            ->minDate(fn (callable $get) => $get('signed_date')),

                        DatePicker::make('end_date')
                            ->label('End Date')
                            ->required()
                            ->default(now())
                            ->displayFormat('Y-m-d')
                            ->minDate(fn (callable $get) => $get('start_date')),
                    ])
                    ->grow(false) // Prevents it from growing, keeps it compact
                    ->columnSpan(1), // This section takes up 1 column

            ])

            ->columns(4);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('contract_number'),

                Tables\Columns\TextColumn::make('description')->searchable(),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('signed_date'),
                Tables\Columns\TextColumn::make('start_date'),
                Tables\Columns\TextColumn::make('end_date'),
                Tables\Columns\TextColumn::make('contract_status')->badge(),


                Tables\Columns\TextColumn::make('markets.name')->badge(),



            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHotelContracts::route('/'),
            'create' => Pages\CreateHotelContract::route('/create'),
            'edit' => Pages\EditHotelContract::route('/{record}/edit'),
        ];
    }
}