<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add polymorphic relationship columns
            $table->string('bookable_type')->after('user_id');
            $table->unsignedBigInteger('bookable_id')->after('bookable_type');

            // Add total_amount column (rename from total_price for consistency)
            $table->decimal('total_amount', 10, 2)->after('total_price');

            // Add index for polymorphic relationship
            $table->index(['bookable_type', 'bookable_id']);
        });

        // Migrate existing data from activity_id to polymorphic structure
        DB::statement("
            UPDATE bookings
            SET bookable_type = 'App\\\\Models\\\\Activity',
                bookable_id = activity_id,
                total_amount = total_price
            WHERE activity_id IS NOT NULL
        ");

        Schema::table('bookings', function (Blueprint $table) {
            // Remove old activity_id foreign key constraint
            $table->dropForeign(['activity_id']);

            // Drop the old columns
            $table->dropColumn(['activity_id', 'total_price']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add back the old columns
            $table->foreignId('activity_id')->nullable()->constrained()->onDelete('cascade');
            $table->decimal('total_price', 10, 2)->after('total_amount');

            // Drop the index
            $table->dropIndex(['bookable_type', 'bookable_id']);
        });

        // Migrate data back to activity_id structure
        DB::statement("
            UPDATE bookings
            SET activity_id = bookable_id,
                total_price = total_amount
            WHERE bookable_type = 'App\\\\Models\\\\Activity'
        ");

        Schema::table('bookings', function (Blueprint $table) {
            // Remove polymorphic columns
            $table->dropColumn(['bookable_type', 'bookable_id', 'total_amount']);
        });
    }
};
