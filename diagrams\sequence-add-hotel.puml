@startuml Add Hotel Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor <PERSON><PERSON> as A
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over A, M : Add Hotel Operation - MVC Architecture

A -> V : Navigate to admin panel
V -> C : GET /admin/hotels
C -> V : Return hotels list view
V -> A : Display hotels management page

A -> V : Click "Create New Hotel"
V -> C : GET /admin/hotels/create
C -> V : Return hotel creation form
V -> A : Display hotel form\n(name, description, location, images)

A -> V : Fill hotel information\nand upload images
V -> C : POST /admin/hotels\n(form data + files)
C -> C : Validate input data

alt Valid data
    C -> C : Process image uploads
    C -> M : Create new hotel record
    M -> M : Save hotel to database
    
    alt Hotel created successfully
        M -> C : Return hotel object
        C -> C : Log creation action
        C -> V : Redirect with success message
        V -> A : Display "Hotel created successfully"
    else Database error
        M -> C : Return creation error
        C -> C : Clean up uploaded files
        C -> V : Return error view
        V -> A : Display "Failed to create hotel"
    end
    
else Invalid data
    C -> V : Return form with validation errors
    V -> A : Display validation error messages
end

note over A, M : Hotel creation with image management and validation

@enduml
