<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.1.2/dist/tailwind.min.css" rel="stylesheet">
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Alternative Font Awesome CDN -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css">
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <title>{{ $title ?? 'Travel Shaper' }}</title>

    <!-- Custom Animated Background Styles -->
    <style>
        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: #ffffff;
            overflow: hidden;
        }

        /* Removed gradient overlay to keep background pure white */

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.15;
            animation: float 6s ease-in-out infinite;
            filter: drop-shadow(0 0 8px rgba(255, 20, 147, 0.2));
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
            animation-duration: 8s;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 10%;
            animation-delay: 2s;
            animation-duration: 10s;
        }

        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
            animation-duration: 12s;
        }

        .floating-element:nth-child(4) {
            bottom: 10%;
            right: 20%;
            animation-delay: 6s;
            animation-duration: 9s;
        }

        .floating-element:nth-child(5) {
            top: 50%;
            left: 50%;
            animation-delay: 1s;
            animation-duration: 11s;
        }

        /* Animations */

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            33% {
                transform: translateY(-20px) rotate(120deg);
            }

            66% {
                transform: translateY(10px) rotate(240deg);
            }
        }

        /* Particles */
        .particle {
            position: absolute;
            background: rgba(255, 20, 147, 0.3);
            border-radius: 50%;
            animation: particle 15s linear infinite;
            box-shadow: 0 0 10px rgba(255, 20, 147, 0.2);
        }

        .particle:nth-child(odd) {
            animation-duration: 20s;
            background: rgba(255, 182, 193, 0.4);
            box-shadow: 0 0 8px rgba(255, 182, 193, 0.3);
        }

        .particle:nth-child(even) {
            animation-duration: 25s;
            background: rgba(255, 105, 180, 0.4);
            box-shadow: 0 0 8px rgba(255, 105, 180, 0.3);
        }

        @keyframes particle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Content overlay */
        .content-overlay {
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body>
    <!-- Animated Background -->
    <div class="animated-bg">
        <!-- Floating Travel Elements -->
        <div class="floating-element">
            <svg width="60" height="60" viewBox="0 0 24 24" fill="currentColor" class="text-pink-400">
                <path
                    d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z" />
            </svg>
        </div>
        <div class="floating-element">
            <svg width="50" height="50" viewBox="0 0 24 24" fill="currentColor" class="text-pink-500">
                <path
                    d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
            </svg>
        </div>
        <div class="floating-element">
            <svg width="55" height="55" viewBox="0 0 24 24" fill="currentColor" class="text-pink-600">
                <path
                    d="M14 6V4h-4v2h4zM4 8v11h16V8H4zm16-2c1.11 0 2 .89 2 2v11c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2l.01-11c0-1.11.88-2 1.99-2h4V4c0-1.11.89-2 2-2h4c1.11 0 2 .89 2 2v2h4z" />
            </svg>
        </div>
        <div class="floating-element">
            <svg width="45" height="45" viewBox="0 0 24 24" fill="currentColor" class="text-rose-400">
                <path
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
        </div>
        <div class="floating-element">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor" class="text-rose-500">
                <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
        </div>

        <!-- Animated Particles -->
        <div class="particle" style="left: 10%; width: 4px; height: 4px; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; width: 6px; height: 6px; animation-delay: 2s;"></div>
        <div class="particle" style="left: 30%; width: 3px; height: 3px; animation-delay: 4s;"></div>
        <div class="particle" style="left: 40%; width: 5px; height: 5px; animation-delay: 6s;"></div>
        <div class="particle" style="left: 50%; width: 4px; height: 4px; animation-delay: 8s;"></div>
        <div class="particle" style="left: 60%; width: 7px; height: 7px; animation-delay: 10s;"></div>
        <div class="particle" style="left: 70%; width: 3px; height: 3px; animation-delay: 12s;"></div>
        <div class="particle" style="left: 80%; width: 5px; height: 5px; animation-delay: 14s;"></div>
        <div class="particle" style="left: 90%; width: 4px; height: 4px; animation-delay: 16s;"></div>
    </div>

    <div class="min-h-screen flex flex-col m-auto sm:w-full md:w-3/4 relative z-10">
        <!-- Custom Header with Breeze Functionality -->
        <header
            class="bg-white/95 backdrop-blur-sm w-full px-4 py-2 flex flex-col md:flex-row justify-between items-center gap-2 shadow-lg border-b border-pink-100 relative z-50">
            <!-- Left side: Logo and Navigation -->
            <div class="flex flex-col gap-3 w-full">
                <div class="flex justify-between items-center w-full">
                    <div class="flex items-center gap-3">
                        <a href="/"
                            class="text-pink-600 font-extrabold text-lg hover:text-pink-700 hover:scale-105 transition-all duration-200 cursor-pointer select-none">
                            TRAVEL<br>SHAPER
                        </a>
                    </div>

                    <!-- Right side: Currency & User Actions -->
                    <div class="flex items-center gap-2">
                        <!-- Currency Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                class="flex items-center gap-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-2 py-1 rounded-full hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z">
                                    </path>
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="font-semibold text-xs"
                                    id="currentCurrency">{{ getCurrentCurrency() }}</span>
                                <svg class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': open }"
                                    fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                        clip-rule="evenodd"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Menu -->
                            <div x-show="open" @click.away="open = false"
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="transform opacity-0 scale-95"
                                x-transition:enter-end="transform opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-75"
                                x-transition:leave-start="transform opacity-100 scale-100"
                                x-transition:leave-end="transform opacity-0 scale-95"
                                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-[9999]"
                                style="z-index: 9999 !important;">
                                <div class="py-1">
                                    <div
                                        class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                                        Select Currency
                                    </div>
                                    @foreach (\App\Services\CurrencyService::getAllCurrencies() as $code => $label)
                                        <button onclick="switchCurrency('{{ $code }}')"
                                            class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center justify-between {{ getCurrentCurrency() === $code ? 'bg-blue-50 text-blue-700' : '' }}">
                                            <span>{{ $label }}</span>
                                            @if (getCurrentCurrency() === $code)
                                                <svg class="w-4 h-4 text-blue-600" fill="currentColor"
                                                    viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                        clip-rule="evenodd"></path>
                                                </svg>
                                            @endif
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        @auth
                            <!-- Chatbot button - always visible -->
                            <a href="/chatbot"
                                class="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-3 py-1.5 rounded-full hover:from-pink-600 hover:to-pink-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="font-semibold">Chatbot</span>
                            </a>

                            <a href="{{ route('profile.edit') }}"
                                class="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-3 py-1.5 rounded-full hover:from-pink-600 hover:to-pink-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="font-semibold">Profile</span>
                            </a>
                            <a href="{{ route('mytrips') }}"
                                class="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-3 py-1.5 rounded-full hover:from-pink-600 hover:to-pink-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
                                    </path>
                                </svg>
                                <span class="font-semibold">My Trips</span>
                            </a>
                            <a href="{{ route('favorites') }}"
                                class="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-3 py-1.5 rounded-full hover:from-pink-600 hover:to-pink-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="font-semibold">Favorites</span>
                            </a>

                            <!-- Logout Button -->
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <button type="submit"
                                    class="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1.5 rounded-full hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="font-semibold">Logout</span>
                                </button>
                            </form>
                        @else
                            <!-- Guest buttons -->
                            <a href="{{ route('login') }}"
                                class="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-3 py-1.5 rounded-full hover:from-pink-600 hover:to-pink-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="font-semibold">Login</span>
                            </a>

                            <a href="{{ route('register') }}"
                                class="flex items-center gap-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-3 py-1.5 rounded-full hover:from-pink-600 hover:to-pink-700 transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z">
                                    </path>
                                </svg>
                                <span class="font-semibold">Register</span>
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        {{ $slot }}

        <!-- Large separation before footer (especially for chatbot page) -->
        <div class="h-25 md:h-39 lg:h-55"></div>

        <!-- Your existing footer -->
        <footer
            class="bg-[#060713]/90 backdrop-blur-md text-white px-6 md:px-20 py-12 rounded-lg mt-7 border border-white/10">
            <div class="flex flex-col md:flex-row justify-between items-start gap-12">
                <!-- Newsletter -->
                <div class="bg-gradient-to-r from-[#dc4a67] to-[#e06e80] rounded-2xl p-6 w-full md:max-w-md text-sm">
                    <p class="font-semibold">Subscribe to our newsletter</p>
                    <p class="text-white/80 text-xs mb-4">Subtitle newsletter</p>
                    <label class="flex items-start gap-2 mb-4">
                        <input type="checkbox" class="mt-1 form-checkbox text-pink-600 rounded" />
                        <span>J'accepte de recevoir par e-mail les newsletters et actualités <br><strong>Travel
                                Shaper</strong></span>
                    </label>
                    <div class="flex rounded overflow-hidden">
                        <input type="email" placeholder="Email address"
                            class="w-full px-4 py-2 text-black outline-none" />
                        <button class="bg-black px-4 flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" stroke-width="2"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="flex flex-col md:flex-row gap-8 text-sm font-semibold mt-4 md:mt-0">
                    <a href="/" class="hover:text-pink-400">Accueil</a>
                    <a href="#" class="hover:text-pink-400">Media</a>
                    <a href="#" class="hover:text-pink-400">About</a>
                    <a href="{{ route('support.contact') }}" class="hover:text-pink-400">Contact Support</a>
                </div>
            </div>

            <hr class="my-8 border-white/10" />

            <div class="flex flex-col md:flex-row justify-between items-center text-sm text-white/70">
                <!-- Logo and legal -->
                <div class="flex flex-col md:flex-row md:items-center gap-4">
                    <div class="text-xl font-bold leading-tight">
                        <div class="leading-none">TRAVEL</div>
                        <div class="leading-none">SHAPER</div>
                    </div>
                    <div class="flex gap-6 mt-2 md:mt-0">
                        <a href="#" class="hover:underline">Terms</a>
                        <a href="#" class="hover:underline">Privacy</a>
                        <a href="#" class="hover:underline">Cookies</a>
                    </div>
                </div>

                <!-- Socials -->
                <div class="flex gap-4 mt-6 md:mt-0">
                    <a href="#"
                        class="w-8 h-8 flex items-center justify-center border border-white/30 rounded-full hover:border-white">
                        <i class="fab fa-tiktok"></i>
                    </a>
                    <a href="#"
                        class="w-8 h-8 flex items-center justify-center border border-white/30 rounded-full hover:border-white">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#"
                        class="w-8 h-8 flex items-center justify-center border border-white/30 rounded-full hover:border-white">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#"
                        class="w-8 h-8 flex items-center justify-center border border-white/30 rounded-full hover:border-white">
                        <i class="fas fa-link"></i>
                    </a>
                </div>
            </div>

            <p class="text-center text-xs text-white/50 mt-6">TRAVEL SHAPER © 2025 All Rights Reserved</p>
        </footer>

    </div>

    <!-- Alpine JS -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    @livewireScripts

    <!-- Currency switching functionality -->
    <script>
        function switchCurrency(currency) {
            // Show loading state
            const currentCurrencyElement = document.getElementById('currentCurrency');
            const originalText = currentCurrencyElement.textContent;
            currentCurrencyElement.textContent = '...';

            // Make AJAX request to switch currency
            fetch('{{ route('currency.switch') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        currency: currency
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the currency display
                        currentCurrencyElement.textContent = data.currency;

                        // Reload the page to update all prices
                        window.location.reload();
                    } else {
                        // Restore original text on error
                        currentCurrencyElement.textContent = originalText;
                        console.error('Failed to switch currency');
                    }
                })
                .catch(error => {
                    // Restore original text on error
                    currentCurrencyElement.textContent = originalText;
                    console.error('Error switching currency:', error);
                });
        }
    </script>

    <!-- Dynamic Particles Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const animatedBg = document.querySelector('.animated-bg');

            // Create additional dynamic particles
            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 8px
                const size = Math.random() * 6 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random horizontal position
                particle.style.left = Math.random() * 100 + '%';

                // Random colors from pink palette
                const colors = [
                    'rgba(255, 182, 193, 0.4)',
                    'rgba(255, 105, 180, 0.4)',
                    'rgba(255, 20, 147, 0.4)',
                    'rgba(199, 21, 133, 0.4)',
                    'rgba(219, 112, 147, 0.4)'
                ];
                const selectedColor = colors[Math.floor(Math.random() * colors.length)];
                particle.style.background = selectedColor;
                particle.style.boxShadow = `0 0 8px ${selectedColor}`;

                // Random animation duration
                particle.style.animationDuration = (Math.random() * 10 + 15) + 's';

                animatedBg.appendChild(particle);

                // Remove particle after animation
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 25000);
            }

            // Create a new particle every 3 seconds
            setInterval(createParticle, 3000);

            // Create initial burst of particles
            for (let i = 0; i < 5; i++) {
                setTimeout(createParticle, i * 1000);
            }
        });
    </script>
</body>

</html>
