<?php

namespace App\Filament\Forms\Components;

use Filament\Forms\Components\Field;

class LeafletMap extends Field
{
    protected string $view = 'filament.forms.components.leaflet-map';

    protected function setUp(): void
    {
        parent::setUp();

        $this->afterStateHydrated(function (LeafletMap $component, ?string $state): void {
            if ($state) {
                $component->state($state);
            } else {
                $component->state(json_encode([
                    'lat' => 48.8566, // Paris par défaut
                    'lng' => 2.3522,
                ]));
            }
        });

        $this->dehydrated(fn ($state) => filled($state));
    }
}
