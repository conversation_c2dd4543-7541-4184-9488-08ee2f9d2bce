<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('hotel__contracts', function (Blueprint $table) {
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->date('signed_date')->nullable();
            $table->enum('channels', ['b2b_europe', 'b2b2c'])->default('b2b_europe');
            $table->enum('commission', ['net', 'commisionnable'])->default('net');

            $table->json('price')->nullable();
            $table->enum('contract_status', ['draft', 'in_progress', 'signed', 'cancelled', 'lost'])->default('Signed');
        });
    }

    public function down(): void
    {
        Schema::table('hotel__contracts', function (Blueprint $table) {
            $table->dropColumn(['start_date', 'end_date', 'price', 'signed_date', 'contract_status', 'channels', 'commission']);
        });
    }
};