<?php

use App\Models\Activity;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\ActivityContract;
use App\Models\TransferContract;
use App\Models\Markets;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Pivot table: activity_contract <-> market
        Schema::create('activity_contract_market', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(ActivityContract::class);
            $table->foreignIdFor(Markets::class);
            $table->timestamps();
        });

        // Pivot table: transfer_contract <-> market
        Schema::create('transfer_contract_market', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(TransferContract::class);
            $table->foreignIdFor(Markets::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transfer_contract_market');
        Schema::dropIfExists('activity_contract_market');
    }
};