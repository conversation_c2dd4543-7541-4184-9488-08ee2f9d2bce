<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['recommendations']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['recommendations']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!--[if BLOCK]><![endif]--><?php if(!empty($recommendations)): ?>
    <div class="mt-4 space-y-4">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recommendations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!--[if BLOCK]><![endif]--><?php if(!empty($section['items'])): ?>
                <div class="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-4 border border-pink-200">
                    <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
                        <!--[if BLOCK]><![endif]--><?php switch($type):
                            case ('popular'): ?>
                                <svg class="w-5 h-5 mr-2 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                            <?php break; ?>

                            <?php case ('personalized'): ?>
                                <svg class="w-5 h-5 mr-2 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                        clip-rule="evenodd" />
                                </svg>
                            <?php break; ?>

                            <?php case ('cross_selling'): ?>
                                <svg class="w-5 h-5 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                                        clip-rule="evenodd" />
                                </svg>
                            <?php break; ?>

                            <?php case ('location_based'): ?>
                                <svg class="w-5 h-5 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                        clip-rule="evenodd" />
                                </svg>
                            <?php break; ?>

                            <?php case ('budget_similar'): ?>
                                <svg class="w-5 h-5 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                                        clip-rule="evenodd" />
                                </svg>
                            <?php break; ?>

                            <?php default: ?>
                                <svg class="w-5 h-5 mr-2 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                        clip-rule="evenodd" />
                                </svg>
                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php echo e($section['title']); ?>

                        <span class="ml-2 text-sm text-gray-500">(<?php echo e($section['count']); ?>)</span>
                    </h4>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = array_slice($section['items'], 0, 6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div onclick="viewItemDetails('<?php echo e($item['type']); ?>', <?php echo e($item['id']); ?>)"
                                class="bg-white rounded-lg p-3 border border-gray-200 hover:border-pink-300 transition-colors cursor-pointer group hover:shadow-md">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h5
                                            class="font-medium text-gray-900 group-hover:text-pink-600 transition-colors">
                                            <?php echo e($item['name'] ?? ($item['title'] ?? 'Unknown')); ?>

                                        </h5>

                                        <!--[if BLOCK]><![endif]--><?php if(isset($item['city'])): ?>
                                            <p class="text-sm text-gray-600 mt-1">
                                                📍 <?php echo e($item['city']); ?>

                                                <!--[if BLOCK]><![endif]--><?php if(isset($item['country'])): ?>
                                                    , <?php echo e($item['country']); ?>

                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if(isset($item['rating'])): ?>
                                            <div class="flex items-center mt-1">
                                                <div class="flex text-yellow-400">
                                                    <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 5; $i++): ?>
                                                        <!--[if BLOCK]><![endif]--><?php if($i <= $item['rating']): ?>
                                                            ⭐
                                                        <?php else: ?>
                                                            ☆
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                                <span class="text-sm text-gray-600 ml-1"><?php echo e($item['rating']); ?>/5</span>
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if(isset($item['activity_type'])): ?>
                                            <span
                                                class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-2">
                                                <?php echo e($item['activity_type']); ?>

                                            </span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if(isset($item['vehicle_type'])): ?>
                                            <span
                                                class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mt-2">
                                                <?php echo e($item['vehicle_type']); ?>

                                            </span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if(isset($item['recommendation_reason'])): ?>
                                            <p class="text-xs text-gray-500 mt-2 italic">
                                                <?php echo e($item['recommendation_reason']); ?>

                                            </p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <div class="ml-3 flex-shrink-0">
                                        <!--[if BLOCK]><![endif]--><?php switch($item['type']):
                                            case ('hotel'): ?>
                                                <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
                                                    🏨
                                                </div>
                                            <?php break; ?>

                                            <?php case ('activity'): ?>
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    🎯
                                                </div>
                                            <?php break; ?>

                                            <?php case ('transfer'): ?>
                                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                    🚗
                                                </div>
                                            <?php break; ?>

                                            <?php case ('destination'): ?>
                                                <div
                                                    class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                                    🌍
                                                </div>
                                            <?php break; ?>

                                            <?php default: ?>
                                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                                    ❓
                                                </div>
                                        <?php endswitch; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>

                                <div class="mt-3 flex space-x-2">
                                    <button
                                        onclick="event.stopPropagation(); viewItemDetails('<?php echo e($item['type']); ?>', <?php echo e($item['id']); ?>)"
                                        class="flex-1 bg-gradient-to-r from-pink-500 to-purple-600 text-white text-xs py-2 px-3 rounded-md hover:from-pink-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 cursor-pointer">
                                        View Details
                                    </button>
                                    <button
                                        onclick="event.stopPropagation(); addToTrip('<?php echo e($item['type']); ?>', <?php echo e($item['id']); ?>, '<?php echo e(addslashes($item['name'] ?? ($item['title'] ?? 'Unknown'))); ?>')"
                                        class="bg-gray-100 text-gray-700 text-xs py-2 px-3 rounded-md hover:bg-gray-200 transition-colors cursor-pointer">
                                        Add to Trip
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if(count($section['items']) > 6): ?>
                        <div class="mt-3 text-center">
                            <button class="text-pink-600 hover:text-pink-700 text-sm font-medium">
                                View <?php echo e(count($section['items']) - 6); ?> more recommendations →
                            </button>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php /**PATH C:\laragon\www\travel-platform\resources\views/components/chatbot-recommendations.blade.php ENDPATH**/ ?>