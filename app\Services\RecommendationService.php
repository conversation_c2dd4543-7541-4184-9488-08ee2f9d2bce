<?php

namespace App\Services;

use App\Models\User;
use App\Models\Hotel;
use App\Models\Activity;
use App\Models\Transfer;
use App\Models\Destination;
use App\Models\Booking;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class RecommendationService
{
    /**
     * Get comprehensive recommendations based on user context and intent
     */
    public function getRecommendations(
        string $intent, 
        array $entities, 
        ?User $user = null, 
        array $context = []
    ): array {
        try {
            $recommendations = [];

            // 1. Popularity-based recommendations
            $recommendations['popular'] = $this->getPopularityBasedRecommendations($intent, $entities);

            // 2. User behavior-based suggestions (if user is authenticated)
            if ($user) {
                $recommendations['personalized'] = $this->getUserBehaviorRecommendations($user, $intent, $entities);
                $recommendations['cross_selling'] = $this->getCrossSellingRecommendations($user, $intent, $entities);
            }

            // 3. Location-based suggestions
            $recommendations['location_based'] = $this->getLocationBasedRecommendations($intent, $entities);

            // 4. Similar budget options
            $recommendations['budget_similar'] = $this->getSimilarBudgetRecommendations($intent, $entities);

            // 5. Context-aware recommendations
            if (!empty($context)) {
                $recommendations['contextual'] = $this->getContextualRecommendations($intent, $entities, $context);
            }

            return $this->formatRecommendations($recommendations, $intent);

        } catch (\Exception $e) {
            Log::error('Recommendation service failed', [
                'intent' => $intent,
                'entities' => $entities,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Get popularity-based recommendations
     */
    private function getPopularityBasedRecommendations(string $intent, array $entities): array
    {
        switch ($intent) {
            case 'voir_hotel':
                return $this->getPopularHotels($entities);
            case 'voir_activité':
                return $this->getPopularActivities($entities);
            case 'voir_transfert':
                return $this->getPopularTransfers($entities);
            case 'voir_destination':
                return $this->getPopularDestinations($entities);
            default:
                return [];
        }
    }

    /**
     * Get popular hotels based on booking frequency
     */
    private function getPopularHotels(array $entities): array
    {
        $query = Hotel::select('hotels.*')
            ->leftJoin('bookings', function($join) {
                $join->on('hotels.id', '=', 'bookings.bookable_id')
                     ->where('bookings.bookable_type', '=', Hotel::class);
            })
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->groupBy('hotels.id');

        // Apply location filter if provided
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('hotels.city', 'LIKE', '%' . $location . '%')
                  ->orWhere('hotels.country', 'LIKE', '%' . $location . '%');
            });
        }

        return $query->orderByDesc('booking_count')
                    ->orderByDesc('hotels.rating')
                    ->limit(5)
                    ->get()
                    ->map(function ($hotel) {
                        return [
                            'id' => $hotel->id,
                            'name' => $hotel->name,
                            'city' => $hotel->city,
                            'country' => $hotel->country,
                            'rating' => $hotel->rating,
                            'booking_count' => $hotel->booking_count,
                            'type' => 'hotel',
                            'recommendation_reason' => 'Popular choice - ' . $hotel->booking_count . ' bookings'
                        ];
                    })->toArray();
    }

    /**
     * Get popular activities based on booking frequency
     */
    private function getPopularActivities(array $entities): array
    {
        $query = Activity::select('activities.*')
            ->leftJoin('bookings', function($join) {
                $join->on('activities.id', '=', 'bookings.bookable_id')
                     ->where('bookings.bookable_type', '=', Activity::class);
            })
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->groupBy('activities.id');

        // Apply location filter if provided
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('activities.city', 'LIKE', '%' . $location . '%')
                  ->orWhere('activities.public_address', 'LIKE', '%' . $location . '%');
            });
        }

        return $query->orderByDesc('booking_count')
                    ->limit(5)
                    ->get()
                    ->map(function ($activity) {
                        return [
                            'id' => $activity->id,
                            'title' => $activity->title,
                            'city' => $activity->city,
                            'activity_type' => $activity->activity_type,
                            'booking_count' => $activity->booking_count,
                            'type' => 'activity',
                            'recommendation_reason' => 'Popular activity - ' . $activity->booking_count . ' bookings'
                        ];
                    })->toArray();
    }

    /**
     * Get popular transfers
     */
    private function getPopularTransfers(array $entities): array
    {
        $query = Transfer::select('transfers.*')
            ->leftJoin('bookings', function($join) {
                $join->on('transfers.id', '=', 'bookings.bookable_id')
                     ->where('bookings.bookable_type', '=', Transfer::class);
            })
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->groupBy('transfers.id');

        return $query->orderByDesc('booking_count')
                    ->limit(5)
                    ->get()
                    ->map(function ($transfer) {
                        return [
                            'id' => $transfer->id,
                            'name' => $transfer->name,
                            'vehicle_type' => $transfer->vehicle_type,
                            'booking_count' => $transfer->booking_count,
                            'type' => 'transfer',
                            'recommendation_reason' => 'Popular transfer option'
                        ];
                    })->toArray();
    }

    /**
     * Get popular destinations
     */
    private function getPopularDestinations(array $entities): array
    {
        $query = Destination::select('destinations.*')
            ->leftJoin('activities', 'destinations.id', '=', 'activities.destination_id')
            ->leftJoin('bookings', function($join) {
                $join->on('activities.id', '=', 'bookings.bookable_id')
                     ->where('bookings.bookable_type', '=', Activity::class);
            })
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->groupBy('destinations.id');

        return $query->orderByDesc('booking_count')
                    ->limit(5)
                    ->get()
                    ->map(function ($destination) {
                        return [
                            'id' => $destination->id,
                            'name' => $destination->name,
                            'country' => $destination->country,
                            'booking_count' => $destination->booking_count,
                            'type' => 'destination',
                            'recommendation_reason' => 'Popular destination'
                        ];
                    })->toArray();
    }

    /**
     * Get user behavior-based recommendations
     */
    private function getUserBehaviorRecommendations(User $user, string $intent, array $entities): array
    {
        // Analyze user's booking history to understand preferences
        $userBookings = $user->bookings()
            ->with('bookable')
            ->where('status', 'confirmed')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        if ($userBookings->isEmpty()) {
            return [];
        }

        // Extract user preferences
        $preferences = $this->extractUserPreferences($userBookings);

        switch ($intent) {
            case 'voir_hotel':
                return $this->getPersonalizedHotels($preferences, $entities);
            case 'voir_activité':
                return $this->getPersonalizedActivities($preferences, $entities);
            case 'voir_transfert':
                return $this->getPersonalizedTransfers($preferences, $entities);
            default:
                return [];
        }
    }

    /**
     * Extract user preferences from booking history
     */
    private function extractUserPreferences(Collection $bookings): array
    {
        $preferences = [
            'preferred_locations' => [],
            'preferred_price_range' => [],
            'preferred_types' => [],
            'booking_patterns' => []
        ];

        foreach ($bookings as $booking) {
            $bookable = $booking->bookable;
            
            // Extract location preferences
            if ($bookable && isset($bookable->city)) {
                $preferences['preferred_locations'][] = $bookable->city;
            }

            // Extract price preferences
            $preferences['preferred_price_range'][] = $booking->total_amount;

            // Extract type preferences
            if ($bookable) {
                $preferences['preferred_types'][] = get_class($bookable);
            }
        }

        // Calculate average price range
        if (!empty($preferences['preferred_price_range'])) {
            $avgPrice = array_sum($preferences['preferred_price_range']) / count($preferences['preferred_price_range']);
            $preferences['avg_price'] = $avgPrice;
            $preferences['price_tolerance'] = $avgPrice * 0.3; // 30% tolerance
        }

        // Get most frequent locations
        $preferences['top_locations'] = array_count_values($preferences['preferred_locations']);
        arsort($preferences['top_locations']);

        return $preferences;
    }

    /**
     * Get personalized hotel recommendations
     */
    private function getPersonalizedHotels(array $preferences, array $entities): array
    {
        $query = Hotel::query();

        // Prefer locations user has booked before
        if (!empty($preferences['top_locations'])) {
            $topLocations = array_keys(array_slice($preferences['top_locations'], 0, 3));
            $query->whereIn('city', $topLocations);
        }

        // Apply price range based on user's history
        if (isset($preferences['avg_price'])) {
            $minPrice = $preferences['avg_price'] - $preferences['price_tolerance'];
            $maxPrice = $preferences['avg_price'] + $preferences['price_tolerance'];

            $query->whereHas('hotelcontracts', function ($q) use ($minPrice, $maxPrice) {
                $q->whereBetween('price', [$minPrice, $maxPrice]);
            });
        }

        return $query->limit(5)
                    ->get()
                    ->map(function ($hotel) {
                        return [
                            'id' => $hotel->id,
                            'name' => $hotel->name,
                            'city' => $hotel->city,
                            'country' => $hotel->country,
                            'rating' => $hotel->rating,
                            'type' => 'hotel',
                            'recommendation_reason' => 'Based on your previous bookings'
                        ];
                    })->toArray();
    }

    /**
     * Get personalized activity recommendations
     */
    private function getPersonalizedActivities(array $preferences, array $entities): array
    {
        $query = Activity::query();

        // Prefer locations user has visited before
        if (!empty($preferences['top_locations'])) {
            $topLocations = array_keys(array_slice($preferences['top_locations'], 0, 3));
            $query->whereIn('city', $topLocations);
        }

        return $query->limit(5)
                    ->get()
                    ->map(function ($activity) {
                        return [
                            'id' => $activity->id,
                            'title' => $activity->title,
                            'city' => $activity->city,
                            'activity_type' => $activity->activity_type,
                            'type' => 'activity',
                            'recommendation_reason' => 'Based on your travel history'
                        ];
                    })->toArray();
    }

    /**
     * Get personalized transfer recommendations
     */
    private function getPersonalizedTransfers(array $preferences, array $entities): array
    {
        $query = Transfer::query();

        // Apply price preferences if available
        if (isset($preferences['avg_price'])) {
            $minPrice = $preferences['avg_price'] - $preferences['price_tolerance'];
            $maxPrice = $preferences['avg_price'] + $preferences['price_tolerance'];

            $query->whereHas('transferContracts', function ($q) use ($minPrice, $maxPrice) {
                $q->whereBetween('price', [$minPrice, $maxPrice]);
            });
        }

        return $query->limit(5)
                    ->get()
                    ->map(function ($transfer) {
                        return [
                            'id' => $transfer->id,
                            'name' => $transfer->name,
                            'vehicle_type' => $transfer->vehicle_type,
                            'type' => 'transfer',
                            'recommendation_reason' => 'Matches your budget preferences'
                        ];
                    })->toArray();
    }

    /**
     * Get cross-selling recommendations (users who booked X also booked Y)
     */
    private function getCrossSellingRecommendations(User $user, string $intent, array $entities): array
    {
        // Get user's recent bookings
        $userBookings = $user->bookings()
            ->where('status', 'confirmed')
            ->with('bookable')
            ->limit(5)
            ->get();

        if ($userBookings->isEmpty()) {
            return [];
        }

        $recommendations = [];

        foreach ($userBookings as $booking) {
            $crossSellItems = $this->findCrossSellingItems($booking, $intent);
            $recommendations = array_merge($recommendations, $crossSellItems);
        }

        // Remove duplicates and limit results
        $uniqueRecommendations = collect($recommendations)
            ->unique('id')
            ->take(5)
            ->values()
            ->toArray();

        return $uniqueRecommendations;
    }

    /**
     * Find items that other users booked along with the given booking
     */
    private function findCrossSellingItems(Booking $booking, string $targetIntent): array
    {
        // Find users who booked the same item
        $similarUsers = Booking::where('bookable_type', $booking->bookable_type)
            ->where('bookable_id', $booking->bookable_id)
            ->where('user_id', '!=', $booking->user_id)
            ->where('status', 'confirmed')
            ->pluck('user_id')
            ->unique();

        if ($similarUsers->isEmpty()) {
            return [];
        }

        // Find what else these users booked
        $targetType = $this->getTargetTypeFromIntent($targetIntent);
        if (!$targetType) {
            return [];
        }

        $crossSellBookings = Booking::whereIn('user_id', $similarUsers)
            ->where('bookable_type', $targetType)
            ->where('status', 'confirmed')
            ->with('bookable')
            ->get()
            ->groupBy('bookable_id')
            ->map(function ($bookings) {
                return $bookings->count();
            })
            ->sortDesc()
            ->take(5);

        $recommendations = [];
        foreach ($crossSellBookings as $bookableId => $count) {
            $bookable = $targetType::find($bookableId);
            if ($bookable) {
                $recommendations[] = [
                    'id' => $bookable->id,
                    'name' => $bookable->name ?? $bookable->title,
                    'type' => strtolower(class_basename($targetType)),
                    'booking_count' => $count,
                    'recommendation_reason' => "Users who booked {$booking->bookable->name} also liked this"
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Get target model type from intent
     */
    private function getTargetTypeFromIntent(string $intent): ?string
    {
        switch ($intent) {
            case 'voir_hotel':
                return Hotel::class;
            case 'voir_activité':
                return Activity::class;
            case 'voir_transfert':
                return Transfer::class;
            default:
                return null;
        }
    }

    /**
     * Get location-based recommendations
     */
    private function getLocationBasedRecommendations(string $intent, array $entities): array
    {
        if (empty($entities['location'])) {
            return [];
        }

        $location = $entities['location'];

        switch ($intent) {
            case 'voir_hotel':
                return $this->getNearbyHotels($location);
            case 'voir_activité':
                return $this->getNearbyActivities($location);
            case 'voir_transfert':
                return $this->getLocationTransfers($location);
            default:
                return [];
        }
    }

    /**
     * Get hotels in the same location
     */
    private function getNearbyHotels(string $location): array
    {
        $hotels = Hotel::where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('country', 'LIKE', '%' . $location . '%');
            })
            ->orderByDesc('rating')
            ->limit(5)
            ->get();

        return $hotels->map(function ($hotel) use ($location) {
            return [
                'id' => $hotel->id,
                'name' => $hotel->name,
                'city' => $hotel->city,
                'country' => $hotel->country,
                'rating' => $hotel->rating,
                'type' => 'hotel',
                'recommendation_reason' => "Other hotels in {$location}"
            ];
        })->toArray();
    }

    /**
     * Get activities in the same location
     */
    private function getNearbyActivities(string $location): array
    {
        $activities = Activity::where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('public_address', 'LIKE', '%' . $location . '%');
            })
            ->limit(5)
            ->get();

        return $activities->map(function ($activity) use ($location) {
            return [
                'id' => $activity->id,
                'title' => $activity->title,
                'city' => $activity->city,
                'activity_type' => $activity->activity_type,
                'type' => 'activity',
                'recommendation_reason' => "Activities in {$location}"
            ];
        })->toArray();
    }

    /**
     * Get transfers for the location
     */
    private function getLocationTransfers(string $location): array
    {
        $transfers = Transfer::limit(5)->get();

        return $transfers->map(function ($transfer) use ($location) {
            return [
                'id' => $transfer->id,
                'name' => $transfer->name,
                'vehicle_type' => $transfer->vehicle_type,
                'type' => 'transfer',
                'recommendation_reason' => "Transfer options for {$location}"
            ];
        })->toArray();
    }

    /**
     * Get similar budget recommendations
     */
    private function getSimilarBudgetRecommendations(string $intent, array $entities): array
    {
        if (empty($entities['prix'])) {
            return [];
        }

        $priceRange = $this->parsePriceRange($entities['prix']);
        if (!$priceRange) {
            return [];
        }

        switch ($intent) {
            case 'voir_hotel':
                return $this->getSimilarBudgetHotels($priceRange, $entities);
            case 'voir_activité':
                return $this->getSimilarBudgetActivities($priceRange, $entities);
            case 'voir_transfert':
                return $this->getSimilarBudgetTransfers($priceRange, $entities);
            default:
                return [];
        }
    }

    /**
     * Get hotels in similar price range
     */
    private function getSimilarBudgetHotels(array $priceRange, array $entities): array
    {
        $query = Hotel::whereHas('hotelcontracts', function ($q) use ($priceRange) {
            if (isset($priceRange['min'])) {
                $q->where('price', '>=', $priceRange['min']);
            }
            if (isset($priceRange['max'])) {
                $q->where('price', '<=', $priceRange['max']);
            }
        });

        // Apply location filter if provided
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('country', 'LIKE', '%' . $location . '%');
            });
        }

        return $query->limit(5)
                    ->get()
                    ->map(function ($hotel) {
                        return [
                            'id' => $hotel->id,
                            'name' => $hotel->name,
                            'city' => $hotel->city,
                            'country' => $hotel->country,
                            'rating' => $hotel->rating,
                            'type' => 'hotel',
                            'recommendation_reason' => 'Similar budget options'
                        ];
                    })->toArray();
    }

    /**
     * Get activities in similar price range
     */
    private function getSimilarBudgetActivities(array $priceRange, array $entities): array
    {
        $query = Activity::whereHas('activityContracts', function ($q) use ($priceRange) {
            if (isset($priceRange['min'])) {
                $q->where('price', '>=', $priceRange['min']);
            }
            if (isset($priceRange['max'])) {
                $q->where('price', '<=', $priceRange['max']);
            }
        });

        // Apply location filter if provided
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('public_address', 'LIKE', '%' . $location . '%');
            });
        }

        return $query->limit(5)
                    ->get()
                    ->map(function ($activity) {
                        return [
                            'id' => $activity->id,
                            'title' => $activity->title,
                            'city' => $activity->city,
                            'activity_type' => $activity->activity_type,
                            'type' => 'activity',
                            'recommendation_reason' => 'Similar budget activities'
                        ];
                    })->toArray();
    }

    /**
     * Get transfers in similar price range
     */
    private function getSimilarBudgetTransfers(array $priceRange, array $entities): array
    {
        $query = Transfer::whereHas('transferContracts', function ($q) use ($priceRange) {
            if (isset($priceRange['min'])) {
                $q->where('price', '>=', $priceRange['min']);
            }
            if (isset($priceRange['max'])) {
                $q->where('price', '<=', $priceRange['max']);
            }
        });

        return $query->limit(5)
                    ->get()
                    ->map(function ($transfer) {
                        return [
                            'id' => $transfer->id,
                            'name' => $transfer->name,
                            'vehicle_type' => $transfer->vehicle_type,
                            'type' => 'transfer',
                            'recommendation_reason' => 'Similar budget transfers'
                        ];
                    })->toArray();
    }

    /**
     * Get contextual recommendations based on conversation context
     */
    private function getContextualRecommendations(string $intent, array $entities, array $context): array
    {
        $recommendations = [];

        // If user previously asked about a destination, suggest related services
        if (isset($context['previous_destination'])) {
            $destination = $context['previous_destination'];

            switch ($intent) {
                case 'voir_hotel':
                    $recommendations = $this->getHotelsInDestination($destination);
                    break;
                case 'voir_activité':
                    $recommendations = $this->getActivitiesInDestination($destination);
                    break;
            }
        }

        return $recommendations;
    }

    /**
     * Get hotels in a specific destination
     */
    private function getHotelsInDestination(string $destination): array
    {
        $hotels = Hotel::where('city', 'LIKE', '%' . $destination . '%')
            ->orderByDesc('rating')
            ->limit(5)
            ->get();

        return $hotels->map(function ($hotel) use ($destination) {
            return [
                'id' => $hotel->id,
                'name' => $hotel->name,
                'city' => $hotel->city,
                'rating' => $hotel->rating,
                'type' => 'hotel',
                'recommendation_reason' => "Hotels in {$destination} (from your previous search)"
            ];
        })->toArray();
    }

    /**
     * Get activities in a specific destination
     */
    private function getActivitiesInDestination(string $destination): array
    {
        $activities = Activity::where('city', 'LIKE', '%' . $destination . '%')
            ->limit(5)
            ->get();

        return $activities->map(function ($activity) use ($destination) {
            return [
                'id' => $activity->id,
                'title' => $activity->title,
                'city' => $activity->city,
                'activity_type' => $activity->activity_type,
                'type' => 'activity',
                'recommendation_reason' => "Activities in {$destination} (from your previous search)"
            ];
        })->toArray();
    }

    /**
     * Parse price range from text
     */
    private function parsePriceRange(string $priceText): ?array
    {
        $priceText = strtolower($priceText);
        $range = [];

        // Extract numbers
        preg_match_all('/\d+/', $priceText, $matches);
        $numbers = array_map('intval', $matches[0]);

        if (strpos($priceText, 'moins de') !== false && !empty($numbers)) {
            $range['max'] = $numbers[0];
        } elseif (strpos($priceText, 'plus de') !== false && !empty($numbers)) {
            $range['min'] = $numbers[0];
        } elseif (strpos($priceText, 'entre') !== false && count($numbers) >= 2) {
            $range['min'] = min($numbers);
            $range['max'] = max($numbers);
        } elseif (count($numbers) === 1) {
            // Exact or approximate price
            $range['min'] = $numbers[0] * 0.8; // -20%
            $range['max'] = $numbers[0] * 1.2; // +20%
        }

        return empty($range) ? null : $range;
    }

    /**
     * Format recommendations for response
     */
    private function formatRecommendations(array $recommendations, string $intent): array
    {
        $formatted = [];

        foreach ($recommendations as $type => $items) {
            if (!empty($items)) {
                $formatted[$type] = [
                    'title' => $this->getRecommendationTitle($type, $intent),
                    'items' => $items,
                    'count' => count($items)
                ];
            }
        }

        return $formatted;
    }

    /**
     * Get recommendation section title
     */
    private function getRecommendationTitle(string $type, string $intent): string
    {
        $titles = [
            'popular' => 'Most Popular',
            'personalized' => 'Recommended for You',
            'cross_selling' => 'Others Also Liked',
            'location_based' => 'In the Same Area',
            'budget_similar' => 'Similar Budget Options',
            'contextual' => 'Based on Your Search'
        ];

        return $titles[$type] ?? 'Recommendations';
    }
}
