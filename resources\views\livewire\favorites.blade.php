<main class="min-h-screen bg-white py-8 px-8">
    <div class="max-w-6xl mx-auto">
        <!-- <PERSON> Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">My Favorites</h1>
            <p class="text-gray-600">Your saved hotels, activities, transfers, and destinations</p>
        </div>

        @if ($hasFavorites)
            <!-- Hotels Section -->
            @if (isset($favorites['hotels']) && count($favorites['hotels']) > 0)
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                        <svg class="w-6 h-6 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z">
                            </path>
                        </svg>
                        Hotels ({{ count($favorites['hotels']) }})
                    </h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach ($favorites['hotels'] as $hotel)
                            <div
                                class="bg-white rounded-xl shadow-sm hover:shadow-xl border border-gray-200 overflow-hidden transition-all duration-300 group">
                                <div class="relative">
                                    @if ($hotel->image)
                                        <img src="{{ asset('storage/' . $hotel->image) }}" alt="{{ $hotel->name }}"
                                            class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                    @else
                                        <div
                                            class="w-full h-48 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                                            <span class="text-white text-3xl">🏨</span>
                                        </div>
                                    @endif

                                    <!-- Remove from favorites button -->
                                    <button wire:click="removeFromFavorites('hotel', {{ $hotel->id }})"
                                        class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300">
                                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-bold text-lg mb-2 line-clamp-2">{{ $hotel->name }}</h3>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $hotel->description }}</p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-blue-600 font-semibold">Hotel</span>
                                        <span class="text-sm text-gray-500">⭐ 4.8</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif

            <!-- Activities Section -->
            @if (isset($favorites['activities']) && count($favorites['activities']) > 0)
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                        <svg class="w-6 h-6 text-orange-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                                clip-rule="evenodd"></path>
                        </svg>
                        Activities ({{ count($favorites['activities']) }})
                    </h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach ($favorites['activities'] as $activity)
                            <div
                                class="bg-white rounded-xl shadow-sm hover:shadow-xl border border-gray-200 overflow-hidden transition-all duration-300 group">
                                <div class="relative">
                                    @if ($activity->image)
                                        <img src="{{ asset('storage/' . $activity->image) }}"
                                            alt="{{ $activity->title }}"
                                            class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                    @else
                                        <div
                                            class="w-full h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                            <span class="text-white text-3xl">🎯</span>
                                        </div>
                                    @endif

                                    <!-- Remove from favorites button -->
                                    <button wire:click="removeFromFavorites('activity', {{ $activity->id }})"
                                        class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300">
                                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-bold text-lg mb-2 line-clamp-2">{{ $activity->title }}</h3>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $activity->type }}</p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-orange-600 font-semibold">Activity</span>
                                        <span class="text-sm text-gray-500">🕒
                                            {{ $activity->duration ?? 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif

            <!-- Transfers Section -->
            @if (isset($favorites['transfers']) && count($favorites['transfers']) > 0)
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                        <svg class="w-6 h-6 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z">
                            </path>
                            <path
                                d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z">
                            </path>
                        </svg>
                        Transfers ({{ count($favorites['transfers']) }})
                    </h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach ($favorites['transfers'] as $transfer)
                            <div
                                class="bg-white rounded-xl shadow-sm hover:shadow-xl border border-gray-200 overflow-hidden transition-all duration-300 group">
                                <div class="relative">
                                    @if ($transfer->main_image)
                                        <img src="{{ asset('storage/' . $transfer->main_image) }}"
                                            alt="{{ $transfer->name }}"
                                            class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                    @else
                                        <div
                                            class="w-full h-48 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center">
                                            <span class="text-white text-3xl">🚗</span>
                                        </div>
                                    @endif

                                    <!-- Remove from favorites button -->
                                    <button wire:click="removeFromFavorites('transfer', {{ $transfer->id }})"
                                        class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300">
                                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-bold text-lg mb-2 line-clamp-2">{{ $transfer->name }}</h3>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $transfer->description }}</p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-green-600 font-semibold">Transfer</span>
                                        <span class="text-sm text-gray-500">🚗 Fast</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif

            <!-- Destinations Section -->
            @if (isset($favorites['destinations']) && count($favorites['destinations']) > 0)
                <section class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                        <svg class="w-6 h-6 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                clip-rule="evenodd"></path>
                        </svg>
                        Destinations ({{ count($favorites['destinations']) }})
                    </h2>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach ($favorites['destinations'] as $destination)
                            <div
                                class="bg-white rounded-xl shadow-sm hover:shadow-xl border border-gray-200 overflow-hidden transition-all duration-300 group">
                                <div class="relative">
                                    @if ($destination->image)
                                        <img src="{{ asset('storage/' . $destination->image) }}"
                                            alt="{{ $destination->name }}"
                                            class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                    @else
                                        <div
                                            class="w-full h-48 bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                                            <span class="text-white text-3xl">📍</span>
                                        </div>
                                    @endif

                                    <!-- Remove from favorites button -->
                                    <button wire:click="removeFromFavorites('destination', {{ $destination->id }})"
                                        class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300">
                                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-bold text-lg mb-2 line-clamp-2">{{ $destination->name }}</h3>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                        {{ $destination->country ?? 'Destination' }}</p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-purple-600 font-semibold">Destination</span>
                                        <a href="{{ route('destinations.show', $destination->id) }}"
                                            class="text-sm text-blue-500 hover:text-blue-700">View Details</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif
        @else
            <!-- Empty State -->
            <div class="flex items-center justify-center min-h-[60vh]">
                <div class="flex flex-col items-center space-y-4 text-center">
                    <div class="flex items-center justify-center w-20 h-20 bg-pink-100 rounded-full">
                        <svg class="w-10 h-10 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-semibold text-gray-700">Your favorites list is empty</h2>
                    <p class="text-gray-500 max-w-md">Start exploring and click the heart icon on hotels, activities,
                        transfers, and destinations to add them to your favorites!</p>
                    <a href="/"
                        class="mt-4 bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-colors">
                        Start Exploring
                    </a>
                </div>
            </div>
        @endif
    </div>
</main>
