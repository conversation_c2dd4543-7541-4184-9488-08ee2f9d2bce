<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\StripeService;
use Illuminate\Support\Facades\Log;

class StripeWebhookController extends Controller
{
    protected $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Handle Stripe webhook events
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $signature = $request->header('Stripe-Signature');

        if (!$signature) {
            Log::error('Missing Stripe signature header');
            return response('Missing signature', 400);
        }

        $result = $this->stripeService->handleWebhook($payload, $signature);

        if ($result['success']) {
            return response('Webhook handled successfully', 200);
        } else {
            Log::error('Webhook handling failed: ' . $result['error']);
            return response('Webhook handling failed', 400);
        }
    }
}
