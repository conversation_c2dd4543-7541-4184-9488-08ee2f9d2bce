<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('transfer_contracts', function (Blueprint $table) {
            
            $table->json('policies')->nullable();
            $table->json('supplements_and_special_offers')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('transfer_contracts', function (Blueprint $table) {
            $table->dropColumn(['supplements_and_special_offers', 'policies']);
        });
    }
};