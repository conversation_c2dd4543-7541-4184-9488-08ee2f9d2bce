<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    /**
     * Display the specified activity with all details.
     */
    public function show($id)
    {
        $activity = Activity::with([
            'destination',
            'meetingPoints',
            'activityContracts.markets'
        ])->findOrFail($id);

        // Get similar activities (same destination, different activity)
        $similarActivities = Activity::with(['destination', 'activityContracts.markets'])
            ->where('destination_id', $activity->destination_id)
            ->where('id', '!=', $activity->id)
            ->limit(4)
            ->get();

        // Get the best price from contracts
        $bestPrice = $activity->activityContracts->min('adult_price');

        return view('activities.show', compact('activity', 'similarActivities', 'bestPrice'));
    }
}
