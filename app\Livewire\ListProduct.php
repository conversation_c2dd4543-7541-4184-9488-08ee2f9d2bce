<?php

namespace App\Livewire;

use App\Models\Activity;
use App\Models\Hotel;
use App\Models\Transfer;
use App\Models\Destination;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ListProduct extends Component
{
    /**
     * Toggle favorite status for an item.
     */
    public function toggleFavorite($type, $id)
    {
        // Debug logging
        Log::info('Livewire toggleFavorite called', [
            'type' => $type,
            'id' => $id,
            'user_id' => Auth::id()
        ]);

        if (!Auth::check()) {
            $this->dispatch('show-login-modal');
            return;
        }

        $user = Auth::user();
        $model = $this->getModel($type, $id);

        Log::info('Model found', [
            'model' => $model ? get_class($model) : 'null',
            'model_id' => $model ? $model->id : 'null'
        ]);

        if (!$model) {
            session()->flash('error', 'Item not found');
            Log::error('Model not found in Livewire', ['type' => $type, 'id' => $id]);
            return;
        }

        $isFavorited = $user->hasFavorited($model);

        Log::info('Before toggle', [
            'isFavorited' => $isFavorited,
            'favorites_count' => $user->favorites()->count()
        ]);

        if ($isFavorited) {
            $user->removeFromFavorites($model);
            $message = 'Removed from favorites';
        } else {
            $user->addToFavorites($model);
            $message = 'Added to favorites';
        }

        Log::info('After toggle', [
            'message' => $message,
            'favorites_count' => $user->favorites()->count()
        ]);

        session()->flash('success', $message);

        // Refresh the component to update the heart icons
        $this->dispatch('$refresh');
    }

    /**
     * Check if user has favorited an item.
     */
    public function isFavorited($type, $id)
    {
        if (!Auth::check()) {
            return false;
        }

        $model = $this->getModel($type, $id);
        return $model ? Auth::user()->hasFavorited($model) : false;
    }

    /**
     * Get the appropriate model instance.
     */
    private function getModel($type, $id)
    {
        return match ($type) {
            'hotel' => Hotel::find($id),
            'activity' => Activity::find($id),
            'transfer' => Transfer::find($id),
            'destination' => Destination::find($id),
            default => null,
        };
    }

    public function render()
    {
        $hotels = Hotel::all();
        $hotels=Hotel:: whereHas('hotelcontracts',function($query){
            $query->where('end_date','>', now())->where('contract_status', 'signed');

        })->get();
        // Get activities with relations
        $activities = Activity::with(['destination', 'activityContracts.markets'])->get();
        // $activities=Activity:: whereHas('activityContracts',function($query){
        //     $query->where('end_date','>', now())->where('contract_status', 'signed');
        // })->get();
        $transfers = Transfer::all();
        $transfers=Transfer:: whereHas('transferContracts',function($query){
            $query->where('end_date','>', now())->where('contract_status', 'signed');

        })->get();

        // Get destinations with images for the "Venture off the beaten path" section
        $destinations = Destination::whereNotNull('name')
            ->whereNotNull('image')
            ->orderBy('name')
            ->limit(5)
            ->get();

        return view('livewire.list-product',[
            'hotels'=>$hotels,
            'activities' => $activities,
            'transfers' => $transfers,
            'destinations' => $destinations,

    ]);
    }
}