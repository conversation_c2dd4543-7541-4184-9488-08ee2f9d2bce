<?php

namespace App\Filament\Widgets;

use App\Models\Booking;
use App\Services\AnalyticsService;
use Filament\Widgets\ChartWidget;

class RevenueChart extends ChartWidget
{
    protected static ?string $heading = '💰 Revenue Analytics';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '300px';

    public ?string $filter = '30days';

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '90days' => 'Last 3 months',
            '365days' => 'Last year',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;
        
        $days = match ($activeFilter) {
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            '365days' => 365,
            default => 30,
        };

        // Get revenue trend data using custom analytics service
        $currentPeriodData = AnalyticsService::getTrendData(Booking::class, $days, 'total_amount');
        $previousPeriodData = AnalyticsService::getTrendData(Booking::class, $days, 'total_amount');

        $revenueData = $currentPeriodData['data'];
        $labels = $currentPeriodData['labels'];
        $previousRevenueData = $previousPeriodData['data'];

        return [
            'datasets' => [
                [
                    'label' => 'Current Period Revenue (€)',
                    'data' => $revenueData,
                    'borderColor' => '#ec4899',
                    'backgroundColor' => 'rgba(236, 72, 153, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Previous Period Revenue (€)',
                    'data' => $previousRevenueData,
                    'borderColor' => '#94a3b8',
                    'backgroundColor' => 'rgba(148, 163, 184, 0.1)',
                    'fill' => false,
                    'borderDash' => [5, 5],
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.dataset.label + ": €" + context.parsed.y.toFixed(2);
                        }'
                    ]
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) {
                            return "€" + value.toFixed(0);
                        }'
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
            'elements' => [
                'point' => [
                    'radius' => 4,
                    'hoverRadius' => 6,
                ],
            ],
        ];
    }
}
