<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SupportMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class SupportController extends Controller
{
    /**
     * Show the contact support form.
     */
    public function index(): View
    {
        return view('support.contact');
    }

    /**
     * Store a new support message.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:5000',
        ]);

        // Add user_id if authenticated
        if (Auth::check()) {
            $validated['user_id'] = Auth::id();
        }

        SupportMessage::create($validated);

        return redirect()->route('support.contact')
            ->with('success', 'Your message has been sent successfully! We will get back to you soon.');
    }

    /**
     * Show all support messages (admin only).
     */
    public function admin(): View
    {
        $messages = SupportMessage::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('support.admin', compact('messages'));
    }

    /**
     * Update message status (admin only).
     */
    public function updateStatus(Request $request, SupportMessage $message): RedirectResponse
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,resolved,closed',
        ]);

        $message->update($validated);

        if ($validated['status'] === 'resolved') {
            $message->markAsResolved();
        }

        return redirect()->back()
            ->with('success', 'Message status updated successfully.');
    }
}
