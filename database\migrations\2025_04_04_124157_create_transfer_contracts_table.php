<?php

use App\Models\Transfer;
use App\Models\TransferContract;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transfer_contracts', function (Blueprint $table) {
            $table->id();

            $table->string('contract_number');
            $table->string('type');

            $table->boolean('direct_supplier')->default(true);
            $table->string('main_supplier');
            $table->string('intermediate_supplier')->nullable();

            $table->string('markets')->nullable();

            $table->text('description');
            $table->json('whats_included')->nullable();
            $table->json('whats_not_included')->nullable();
            
            $table->timestamps();
        });

        Schema::create('transfer_transfer_contract', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Transfer::class);
            $table->foreignIdFor(TransferContract::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transfer_contracts');
        Schema::dropIfExists('transfer_transfer_contract');
    }
};