<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::create('transfers', function (Blueprint $table) {
        $table->id();
        $table->string('name');  // Name field
        $table->text('description');  // Description field
        $table->string('main_image')->nullable();  // Main image field
        $table->string('transfer_type');  // Transfer type
        $table->string('vehicle_type');  // Type vehicle
        $table->integer('min_capacity');  // Min capacity
        $table->integer('max_capacity');  // Max capacity
        $table->integer('suit_cases');  // Suitcases
        $table->integer('small_bag');  // Small bag
        $table->decimal('tax', 8, 2)->nullable();  // Tax
        $table->text('details_tax')->nullable();  // Details tax
        $table->string('suppliers')->nullable();  // Suppliers type
        $table->timestamps();
    });
}

public function down()
{
    Schema::dropIfExists('transfers');
}

};
