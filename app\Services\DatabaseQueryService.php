<?php

namespace App\Services;

use App\Models\Hotel;
use App\Models\Activity;
use App\Models\Transfer;
use App\Models\Destination;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;

class DatabaseQueryService
{
    public function queryByIntent(string $intent, array $entities): array
    {
        try {
            switch ($intent) {
                case 'voir_hotel':
                    return $this->queryHotels($entities);

                case 'voir_activité':
                    return $this->queryActivities($entities);

                case 'voir_transfert':
                    return $this->queryTransfers($entities);

                case 'voir_destination':
                    return $this->queryDestinations($entities);

                default:
                    return [];
            }
        } catch (\Exception $e) {
            Log::error('Database query failed', [
                'intent' => $intent,
                'entities' => $entities,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    private function queryHotels(array $entities): array
    {
        $query = Hotel::query();

        // Filtrer par nom d'hôtel
        if (!empty($entities['nom_hotel'])) {
            $query->where('name', 'LIKE', '%' . $entities['nom_hotel'] . '%');
        }

        // Filtrer par location
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('country', 'LIKE', '%' . $location . '%')
                  ->orWhere('address', 'LIKE', '%' . $location . '%');
            });
        }

        // Filtrer par prix (via les contrats)
        if (!empty($entities['prix'])) {
            $priceRange = $this->parsePriceRange($entities['prix']);
            if ($priceRange) {
                $query->whereHas('hotelcontracts', function ($q) use ($priceRange) {
                    if (isset($priceRange['min'])) {
                        $q->where('price', '>=', $priceRange['min']);
                    }
                    if (isset($priceRange['max'])) {
                        $q->where('price', '<=', $priceRange['max']);
                    }
                });
            }
        }

        $hotels = $query->limit(10)->get();

        return $this->formatHotelsForResponse($hotels);
    }

    private function queryActivities(array $entities): array
    {
        $query = Activity::query();

        // Filtrer par nom d'activité
        if (!empty($entities['nom_activité'])) {
            $query->where('title', 'LIKE', '%' . $entities['nom_activité'] . '%');
        }

        // Filtrer par type d'activité
        if (!empty($entities['type_activité'])) {
            $type = $entities['type_activité'];
            $query->where(function ($q) use ($type) {
                $q->where('activity_type', 'LIKE', '%' . $type . '%')
                  ->orWhere('activity_nature', 'LIKE', '%' . $type . '%')
                  ->orWhere('description', 'LIKE', '%' . $type . '%');
            });
        }

        // Filtrer par location
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('public_address', 'LIKE', '%' . $location . '%')
                  ->orWhere('map_address', 'LIKE', '%' . $location . '%');
            });
        }

        // Filtrer par prix (via les contrats)
        if (!empty($entities['prix'])) {
            $priceRange = $this->parsePriceRange($entities['prix']);
            if ($priceRange) {
                $query->whereHas('activityContracts', function ($q) use ($priceRange) {
                    if (isset($priceRange['min'])) {
                        $q->where('price', '>=', $priceRange['min']);
                    }
                    if (isset($priceRange['max'])) {
                        $q->where('price', '<=', $priceRange['max']);
                    }
                });
            }
        }

        $activities = $query->limit(10)->get();

        return $this->formatActivitiesForResponse($activities);
    }

    private function queryTransfers(array $entities): array
    {
        $query = Transfer::query();

        // Filtrer par nom de transfert
        if (!empty($entities['transfert_name'])) {
            $query->where('name', 'LIKE', '%' . $entities['transfert_name'] . '%');
        }

        // Filtrer par type de véhicule
        if (!empty($entities['vehicule_type'])) {
            $query->where('vehicle_type', 'LIKE', '%' . $entities['vehicule_type'] . '%');
        }

        // Filtrer par prix (via les contrats)
        if (!empty($entities['prix'])) {
            $priceRange = $this->parsePriceRange($entities['prix']);
            if ($priceRange) {
                $query->whereHas('transferContracts', function ($q) use ($priceRange) {
                    if (isset($priceRange['min'])) {
                        $q->where('price', '>=', $priceRange['min']);
                    }
                    if (isset($priceRange['max'])) {
                        $q->where('price', '<=', $priceRange['max']);
                    }
                });
            }
        }

        $transfers = $query->limit(10)->get();

        return $this->formatTransfersForResponse($transfers);
    }

    private function queryDestinations(array $entities): array
    {
        Log::info('QueryDestinations called', ['entities' => $entities]);

        $query = Destination::query();

        // Filtrer par ID de destination (pour les sélections spécifiques)
        if (!empty($entities['destination_id'])) {
            Log::info('Filtering by destination_id', ['id' => $entities['destination_id']]);
            $query->where('id', $entities['destination_id']);
        }
        // Filtrer par nom de destination
        elseif (!empty($entities['nom_destination'])) {
            Log::info('Filtering by nom_destination', ['nom' => $entities['nom_destination']]);
            $query->where('name', 'LIKE', '%' . $entities['nom_destination'] . '%');
        }
        // Filtrer par location (pays, région)
        elseif (!empty($entities['location'])) {
            Log::info('Filtering by location', ['location' => $entities['location']]);
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('name', 'LIKE', '%' . $location . '%')
                  ->orWhere('country', 'LIKE', '%' . $location . '%')
                  ->orWhere('region', 'LIKE', '%' . $location . '%');
            });
        }
        // Si aucune entité spécifique, retourner des destinations populaires
        else {
            Log::info('No specific entity, returning popular destinations');
            $query->orderBy('created_at', 'desc'); // Ou par popularité si vous avez ce champ
        }

        $destinations = $query->limit(10)->get();
        Log::info('Destinations found', ['count' => $destinations->count()]);

        return $this->formatDestinationsForResponse($destinations);
    }

    private function parsePriceRange(string $priceText): ?array
    {
        $priceText = strtolower($priceText);
        $range = [];

        // Extraire les nombres
        preg_match_all('/\d+/', $priceText, $matches);
        $numbers = array_map('intval', $matches[0]);

        if (strpos($priceText, 'moins de') !== false && !empty($numbers)) {
            $range['max'] = $numbers[0];
        } elseif (strpos($priceText, 'plus de') !== false && !empty($numbers)) {
            $range['min'] = $numbers[0];
        } elseif (strpos($priceText, 'entre') !== false && count($numbers) >= 2) {
            $range['min'] = min($numbers);
            $range['max'] = max($numbers);
        } elseif (count($numbers) === 1) {
            // Prix exact ou approximatif
            $range['min'] = $numbers[0] * 0.8; // -20%
            $range['max'] = $numbers[0] * 1.2; // +20%
        }

        return empty($range) ? null : $range;
    }

    private function formatHotelsForResponse(Collection $hotels): array
    {
        return $hotels->map(function ($hotel) {
            return [
                'id' => $hotel->id,
                'name' => $hotel->name,
                'city' => $hotel->city,
                'country' => $hotel->country,
                'rating' => $hotel->rating,
                'description' => $hotel->short_description,
                'type' => 'hotel'
            ];
        })->toArray();
    }

    private function formatActivitiesForResponse(Collection $activities): array
    {
        return $activities->map(function ($activity) {
            return [
                'id' => $activity->id,
                'title' => $activity->title,
                'city' => $activity->city,
                'activity_type' => $activity->activity_type,
                'description' => $activity->description,
                'difficulty_level' => $activity->difficulty_level,
                'type' => 'activity'
            ];
        })->toArray();
    }

    private function formatTransfersForResponse(Collection $transfers): array
    {
        return $transfers->map(function ($transfer) {
            return [
                'id' => $transfer->id,
                'name' => $transfer->name,
                'vehicle_type' => $transfer->vehicle_type,
                'max_capacity' => $transfer->max_capacity,
                'description' => $transfer->description,
                'type' => 'transfer'
            ];
        })->toArray();
    }

    private function formatDestinationsForResponse(Collection $destinations): array
    {
        return $destinations->map(function ($destination) {
            return [
                'id' => $destination->id,
                'name' => $destination->name,
                'country' => $destination->country,
                'region' => $destination->region ?? null,
                'description' => $destination->description,
                'image' => $destination->image,
                'type' => 'destination'
            ];
        })->toArray();
    }

    public function getRecommendations(string $intent, array $entities): array
    {
        // Fournir des recommandations basées sur l'intention et les entités
        switch ($intent) {
            case 'voir_hotel':
                return $this->getHotelRecommendations($entities);

            case 'voir_activité':
                return $this->getActivityRecommendations($entities);

            case 'voir_transfert':
                return $this->getTransferRecommendations($entities);

            case 'voir_destination':
                return $this->getDestinationRecommendations($entities);

            default:
                return [];
        }
    }

    private function getHotelRecommendations(array $entities): array
    {
        // Recommandations d'hôtels populaires ou bien notés
        $query = Hotel::where('rating', '>=', 4);

        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('country', 'LIKE', '%' . $location . '%');
            });
        }

        return $this->formatHotelsForResponse($query->limit(5)->get());
    }

    private function getActivityRecommendations(array $entities): array
    {
        // Recommandations d'activités populaires
        $query = Activity::query();

        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('city', 'LIKE', '%' . $location . '%')
                  ->orWhere('public_address', 'LIKE', '%' . $location . '%');
            });
        }

        return $this->formatActivitiesForResponse($query->limit(5)->get());
    }

    private function getTransferRecommendations(array $entities): array
    {
        // Recommandations de transferts
        $query = Transfer::query();

        if (!empty($entities['vehicule_type'])) {
            $query->where('vehicle_type', 'LIKE', '%' . $entities['vehicule_type'] . '%');
        }

        return $this->formatTransfersForResponse($query->limit(5)->get());
    }

    private function getDestinationRecommendations(array $entities): array
    {
        // Recommandations de destinations populaires
        $query = Destination::query();

        // Si une région ou pays est mentionné, filtrer par cela
        if (!empty($entities['location'])) {
            $location = $entities['location'];
            $query->where(function ($q) use ($location) {
                $q->where('country', 'LIKE', '%' . $location . '%')
                  ->orWhere('region', 'LIKE', '%' . $location . '%');
            });
        }

        // Sinon, retourner des destinations populaires (vous pouvez ajouter un champ popularity)
        $query->orderBy('created_at', 'desc');

        return $this->formatDestinationsForResponse($query->limit(5)->get());
    }
}
