<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Event in {{ $destination->name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #1f2937;
        }

        .event-card {
            background: linear-gradient(135deg, #fef7ff 0%, #fdf2f8 100%);
            border: 2px solid #f3e8ff;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
            overflow: hidden;
        }

        .event-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ec4899, #f97316);
        }

        .event-title {
            font-size: 24px;
            font-weight: 700;
            color: #be185d;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .event-title::before {
            content: '🎉';
            margin-right: 10px;
            font-size: 28px;
        }

        .event-description {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 20px;
            line-height: 1.7;
        }

        .event-details {
            display: grid;
            gap: 12px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            font-size: 15px;
            color: #374151;
        }

        .detail-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .detail-label {
            font-weight: 600;
            margin-right: 8px;
            min-width: 80px;
        }

        .location-highlight {
            background: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .location-highlight .location-name {
            font-size: 20px;
            font-weight: 700;
            color: #5b21b6;
            margin-bottom: 5px;
        }

        .location-highlight .location-country {
            font-size: 14px;
            color: #6b7280;
        }

        .cta-section {
            text-align: center;
            margin: 35px 0;
        }

        .cta-text {
            font-size: 18px;
            color: #1f2937;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 14px 28px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .event-badge {
            display: inline-block;
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 10px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-style: italic;
            color: #92400e;
        }

        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
            margin: 30px 0;
        }

        .footer {
            background-color: #f9fafb;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .footer-signature {
            font-size: 16px;
            color: #374151;
            font-weight: 600;
        }

        .footer-brand {
            background: linear-gradient(135deg, #ec4899, #f97316);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }

            .content {
                padding: 25px 20px;
            }

            .event-card {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 280px;
            }
        }
    </style>
</head>

<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🌟 Exciting News!</h1>
            <p>A new event has been added to your favorite destination</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello <strong>{{ $user->name }}</strong>,
            </div>

            <p style="font-size: 16px; color: #4b5563; margin-bottom: 25px;">
                We have fantastic news! A new event has been created in <strong>{{ $destination->name }}</strong>,
                one of your favorite destinations. This is a perfect opportunity to plan your next adventure!
            </p>

            <!-- Event Card -->
            <div class="event-card">
                <div class="event-badge">New Event</div>
                <div class="event-title">{{ $event->name }}</div>

                @if ($event->description)
                    <div class="event-description">{{ $event->description }}</div>
                @endif

                @if ($event->start_date && $event->end_date && $event->start_date->diffInDays($event->end_date) > 7)
                    <div class="highlight-box">
                        🎯 <strong>Extended Event:</strong> This is a multi-day event spanning
                        {{ $event->start_date->diffInDays($event->end_date) + 1 }} days - perfect for a longer vacation!
                    </div>
                @endif

                <div class="event-details">
                    @if ($event->duration)
                        <div class="detail-item">
                            <span class="detail-icon">⏱️</span>
                            <span class="detail-label">Duration:</span>
                            <span>{{ $event->duration }} hours</span>
                        </div>
                    @endif

                    @if ($event->start_date)
                        <div class="detail-item">
                            <span class="detail-icon">📅</span>
                            <span class="detail-label">Start Date:</span>
                            <span>{{ $event->start_date->format('F j, Y') }}</span>
                        </div>
                    @endif

                    @if ($event->end_date)
                        <div class="detail-item">
                            <span class="detail-icon">🏁</span>
                            <span class="detail-label">End Date:</span>
                            <span>{{ $event->end_date->format('F j, Y') }}</span>
                        </div>
                    @endif
                </div>

                <div class="location-highlight">
                    <div class="location-name">📍 {{ $destination->name }}</div>
                    @if ($destination->country)
                        <div class="location-country">{{ $destination->country }}@if ($destination->continent)
                                , {{ $destination->continent }}
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <div class="cta-text">Ready to plan your perfect trip?</div>
                <div class="button-group">
                    <a href="{{ route('destinations.show', $destination->id) }}" class="btn btn-primary">
                        🗺️ Explore {{ $destination->name }}
                    </a>
                    <a href="{{ route('chatbot') }}" class="btn btn-secondary">
                        🤖 Get AI Recommendations
                    </a>
                </div>

                <!-- Additional Links -->
                <div style="margin-top: 20px; font-size: 14px; color: #6b7280;">
                    <a href="{{ route('home') }}" style="color: #ec4899; text-decoration: none; margin: 0 10px;">Browse
                        All Destinations</a>
                    <span style="color: #d1d5db;">|</span>
                    <a href="{{ route('favorites') }}"
                        style="color: #ec4899; text-decoration: none; margin: 0 10px;">Manage Favorites</a>
                    <span style="color: #d1d5db;">|</span>
                    <a href="{{ route('mytrips') }}" style="color: #ec4899; text-decoration: none; margin: 0 10px;">My
                        Trips</a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <!-- Social Media Section -->
            <div style="text-align: center; margin-bottom: 20px;">
                <div style="font-size: 14px; color: #6b7280; margin-bottom: 10px;">Follow us for more travel inspiration
                </div>
                <div style="display: inline-flex; gap: 15px; justify-content: center;">
                    <a href="#" style="color: #3b82f6; text-decoration: none; font-size: 20px;">📘</a>
                    <a href="#" style="color: #1da1f2; text-decoration: none; font-size: 20px;">🐦</a>
                    <a href="#" style="color: #e4405f; text-decoration: none; font-size: 20px;">📷</a>
                    <a href="#" style="color: #0077b5; text-decoration: none; font-size: 20px;">💼</a>
                </div>
            </div>

            <div class="footer-text">
                You're receiving this email because <strong>{{ $destination->name }}</strong> is in your favorites
                list.
                You can <a href="{{ route('favorites') }}" style="color: #ec4899; text-decoration: none;">manage your
                    favorites</a>
                and notification preferences in your account settings.
            </div>

            <div class="divider"></div>

            <div class="footer-signature">
                Happy travels! ✈️<br>
                The <span class="footer-brand">{{ config('app.name') }}</span> Team
            </div>

            <!-- Copyright and Links -->
            <div style="margin-top: 15px; font-size: 12px; color: #9ca3af;">
                © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.<br>
                <a href="{{ route('support.contact') }}" style="color: #6b7280; text-decoration: none;">Contact
                    Support</a> |
                <a href="#" style="color: #6b7280; text-decoration: none;">Privacy Policy</a> |
                <a href="#" style="color: #6b7280; text-decoration: none;">Terms of Service</a>
            </div>
        </div>
    </div>
</body>

</html>
