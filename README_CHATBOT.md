# 🤖 Chatbot Intelligent - Guide d'utilisation

## 📋 Vue d'ensemble

Le chatbot intelligent a été développé pour votre plateforme de réservation touristique. Il utilise l'API Gemini de Google pour comprendre les intentions des utilisateurs et fournir des réponses personnalisées basées sur votre base de données.

## 🎯 Fonctionnalités principales

### ✅ Détection d'intentions
- **voir_hotel** : Recherche d'hôtels
- **voir_activité** : Recherche d'activités
- **voir_transfert** : Recherche de transferts
- **autre** : Demandes générales

### ✅ Extraction d'entités
- **nom_hotel** : Nom spécifique d'un hôtel
- **location** : Ville, pays, région (Paris, Marrakech, etc.)
- **prix** : Plage de prix ("moins de 100€", "entre 50 et 80€")
- **nom_activité** : Nom spécifique d'une activité
- **type_activité** : Type d'activité (randonnée, spa, musée, etc.)
- **transfert_name** : Nom spécifique d'un transfert
- **vehicule_type** : Type de véhicule (voiture, minivan, bus)

### ✅ Gestion du contexte
- Historique des conversations sur 1 heure
- Continuité contextuelle entre les messages
- Mémorisation des entités importantes

### ✅ Correction automatique
- Correction des fautes d'orthographe courantes
- Normalisation des expressions de prix
- Suggestions de clarification

## 🚀 Exemples d'utilisation

### Recherche d'hôtels
```
"Montre-moi les hôtels à Paris"
"Je cherche un hôtel à Marrakech entre 50 et 100€"
"Y a-t-il un hôtel nommé Royal Palace ?"
```

### Recherche d'activités
```
"Quelles activités à Rome ?"
"Je veux faire de la plongée à Nice"
"Montre-moi les musées à Paris"
```

### Recherche de transferts
```
"Je cherche un transfert en minivan"
"Y a-t-il un transfert depuis l'aéroport ?"
"Je veux un transfert VIP"
```

## 🛠 Architecture technique

### Services principaux
- **ChatbotService** : Service principal orchestrant le traitement
- **GeminiService** : Interface avec l'API Gemini
- **IntentDetectionService** : Détection d'intentions et entités
- **ContextManager** : Gestion du contexte de conversation
- **DatabaseQueryService** : Requêtes dynamiques à la base de données

### Modèles de données
- **ChatSession** : Sessions de conversation
- **ChatMessage** : Messages individuels avec métadonnées

### Composant Livewire
- **Chatbot** : Interface utilisateur réactive

## ⚙️ Configuration

### Variables d'environnement requises
```env
GEMINI_API_KEY=votre_clé_api_gemini
```

### Configuration du chatbot
Le fichier `config/chatbot.php` contient toutes les configurations :
- Intentions et entités supportées
- Corrections orthographiques
- Destinations communes
- Seuils de confiance
- Templates de réponses

## 🧪 Tests

### Tests unitaires
```bash
php artisan test --filter=ChatbotTest
```

### Tests Livewire
```bash
php artisan test --filter=ChatbotLivewireTest
```

## 📊 Monitoring et logs

Le chatbot enregistre automatiquement :
- Erreurs de traitement
- Intentions détectées
- Entités extraites
- Données de réponse

Consultez les logs Laravel pour le debugging :
```bash
tail -f storage/logs/laravel.log
```

## 🔧 Maintenance

### Ajout de nouvelles intentions
1. Modifier `config/chatbot.php`
2. Mettre à jour `IntentDetectionService`
3. Ajouter la logique dans `DatabaseQueryService`

### Ajout de nouvelles entités
1. Ajouter dans `config/chatbot.php`
2. Mettre à jour les prompts Gemini
3. Adapter les requêtes de base de données

### Amélioration des réponses
1. Modifier les prompts dans `GeminiService`
2. Ajuster les templates dans `config/chatbot.php`
3. Tester avec différents scénarios

## 🚨 Dépannage

### Problèmes courants

**Le chatbot ne répond pas :**
- Vérifier la clé API Gemini
- Contrôler les logs d'erreur
- Vérifier la connectivité internet

**Intentions mal détectées :**
- Ajuster les prompts Gemini
- Améliorer les exemples d'entraînement
- Vérifier les corrections orthographiques

**Données non trouvées :**
- Vérifier la structure de la base de données
- Contrôler les requêtes dans `DatabaseQueryService`
- Ajouter des données de test

## 📈 Améliorations futures

- Intégration avec plus de services (météo, devises)
- Support multilingue
- Apprentissage automatique des préférences
- Intégration avec des systèmes de réservation
- Analytics avancés des conversations

## 🔗 Accès

Le chatbot est accessible à l'adresse : `/chatbot`

---

*Développé avec ❤️ pour votre plateforme de voyage*
