<?php

namespace App\Jobs;

use App\Mail\EventCreatedNotification;
use App\Models\Event;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendEventNotificationEmail implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The queue connection that should handle the job.
     *
     * @var string
     */
    public $connection = 'database';

    /**
     * The queue that the job should be dispatched to.
     *
     * @var string
     */
    public $queue = 'high';

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Event $event,
        public User $user
    ) {
        // Set high priority for email notifications
        $this->onQueue('high');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Mail::to($this->user->email)
            ->send(new EventCreatedNotification($this->event, $this->user));
    }
}
