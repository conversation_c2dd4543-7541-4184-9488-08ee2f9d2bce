<?php

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Volt\Component;

new #[Layout('layouts.guest')] class extends Component
{
    #[Locked]
    public string $token = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';

    /**
     * Mount the component.
     */
    public function mount(string $token): void
    {
        $this->token = $token;

        $this->email = request()->string('email');
    }

    /**
     * Reset the password for the given user.
     */
    public function resetPassword(): void
    {
        $this->validate([
            'token' => ['required'],
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
        ]);

        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.
        $status = Password::reset(
            $this->only('email', 'password', 'password_confirmation', 'token'),
            function ($user) {
                $user->forceFill([
                    'password' => Hash::make($this->password),
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($user));
            }
        );

        // If the password was successfully reset, we will redirect the user back to
        // the application's home authenticated view. If there is an error we can
        // redirect them back to where they came from with their error message.
        if ($status != Password::PASSWORD_RESET) {
            $this->addError('email', __($status));

            return;
        }

        Session::flash('status', __($status));

        $this->redirectRoute('login', navigate: true);
    }
}; ?>

<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">

        <!-- Header -->
        <div class="flex flex-col items-center space-y-3">
            <img src="{{ asset('storage/avatar.jpg') }}" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Set New Password</h2>
            <p class="text-center text-gray-500">
                Create a new strong password to secure your account.
            </p>
        </div>

        <!-- Session Status -->
        @if (session('status'))
            <div class="p-3 text-sm text-green-600 bg-green-100 border border-green-300 rounded">
                {{ session('status') }}
            </div>
        @endif

        <!-- Livewire Form -->
        <form wire:submit="resetPassword" class="space-y-4">

            <!-- Email (hidden field) -->
            <input
                type="hidden"
                wire:model="email"
                name="email"
            />

            <!-- New Password -->
            <input
                type="password"
                wire:model.defer="password"
                placeholder="New Password"
                class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                required
                autocomplete="new-password"
            />
            @error('password')
                <p class="text-sm text-red-600">{{ $message }}</p>
            @enderror

            <!-- Confirm Password -->
            <input
                type="password"
                wire:model.defer="password_confirmation"
                placeholder="Confirm Password"
                class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                required
                autocomplete="new-password"
            />
            @error('password_confirmation')
                <p class="text-sm text-red-600">{{ $message }}</p>
            @enderror

            <!-- Submit Button -->
            <button type="submit" class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md">
                Reset Password
            </button>
        </form>

        <!-- Back to Login -->
        <p class="text-sm text-center text-gray-400">
            Back to <a href="{{ route('login') }}" class="font-semibold hover:underline">Login</a>
        </p>
    </div>
</main>