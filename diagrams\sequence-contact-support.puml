@startuml Contact Support Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor User as U
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over U, M : Contact Support Operation - MVC Architecture

U -> V : Navigate to "Contact Support" page
V -> C : GET /support
C -> C : Check user authentication

alt User authenticated
    C -> M : Get user information
    M -> M : Query user profile data
    M -> C : Return user details
    C -> V : Return support form with\npre-filled user information
    V -> U : Display contact support form\n(subject, message, priority)
    
    U -> V : Fill support form fields\n(subject, message, select priority)
    V -> V : Validate form inputs\n(client-side validation)
    V -> U : Show validation feedback\n(real-time field validation)
    
    U -> V : Click "Send Message" button
    V -> C : POST /support/messages\n(subject, message, priority)
    C -> C : Validate support message data
    
    alt Valid message data
        C -> M : Create support message record
        M -> M : INSERT INTO support_messages\n(user_id, subject, message, priority, status='open')
        
        alt Message created successfully
            M -> C : Return support message object
            C -> C : Generate ticket number
            C -> C : Log support request
            C -> V : Return success response\nwith ticket number
            V -> V : Clear form fields
            V -> V : Show success message
            V -> U : Display "Support message sent successfully!\nTicket #[number]"
            
            C -> C : Send notification to admin\n(email/internal notification)
            
        else Database error
            M -> C : Return creation error
            C -> V : Return error response
            V -> U : Display "Failed to send message.\nPlease try again"
        end
        
    else Invalid message data
        C -> V : Return validation errors\n(missing subject, empty message)
        V -> U : Display field-specific\nerror messages
    end
    
else User not authenticated
    C -> V : Return authentication error
    V -> U : Redirect to login page with\nmessage "Please login to contact support"
end

note over U, M : Support message created with ticket tracking

' Additional flow for viewing support history
U -> V : Click "View Support History"\n(optional feature)
V -> C : GET /support/history
C -> C : Check user authentication

alt User authenticated
    C -> M : Get user's support messages
    M -> M : Query support_messages table\n(user_id, ORDER BY created_at DESC)
    M -> C : Return user's support history
    C -> V : Return support history view
    V -> U : Display list of previous\nsupport tickets with status
    
    U -> V : Click on specific ticket\nto view details
    V -> C : GET /support/messages/{id}
    C -> M : Find support message by ID
    M -> M : Query message with responses\n(if any admin replies exist)
    
    alt Message found and belongs to user
        M -> C : Return message details\nwith conversation thread
        C -> V : Return message detail view
        V -> U : Display full conversation\nwith admin responses
        
    else Message not found or unauthorized
        M -> C : Message not found or\ndoesn't belong to user
        C -> V : Return not found error
        V -> U : Display "Message not found"
    end
    
else User not authenticated
    C -> V : Return authentication error
    V -> U : Redirect to login page
end

note over U, M : Complete support system with history tracking and conversation threads

@enduml
