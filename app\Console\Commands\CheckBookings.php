<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Booking;
use App\Models\User;
use App\Models\Activity;

class CheckBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:bookings {--recent : Show only recent bookings}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check booking transactions and payment status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking Booking Transactions...');
        $this->newLine();

        // Get bookings
        $query = Booking::with(['user', 'activity']);

        if ($this->option('recent')) {
            $query->where('created_at', '>=', now()->subHours(24));
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        if ($bookings->isEmpty()) {
            $this->warn('❌ No bookings found.');
            return;
        }

        // Create table
        $headers = ['ID', 'User', 'Activity', 'Date', 'Status', 'Payment Status', 'Amount', 'Payment Intent', 'Created'];
        $rows = [];

        foreach ($bookings as $booking) {
            $rows[] = [
                $booking->id,
                $booking->user->name ?? 'N/A',
                $booking->activity->name ?? 'N/A',
                $booking->booking_date,
                $this->getStatusIcon($booking->status) . ' ' . $booking->status,
                $this->getPaymentStatusIcon($booking->payment_status) . ' ' . ($booking->payment_status ?? 'N/A'),
                '$' . number_format($booking->total_price, 2),
                $booking->payment_intent_id ? substr($booking->payment_intent_id, 0, 20) . '...' : 'N/A',
                $booking->created_at->format('Y-m-d H:i:s')
            ];
        }

        $this->table($headers, $rows);

        // Summary
        $this->newLine();
        $this->info('📊 Summary:');
        $this->line('Total Bookings: ' . $bookings->count());
        $this->line('Confirmed: ' . $bookings->where('status', 'confirmed')->count());
        $this->line('Pending: ' . $bookings->where('status', 'pending')->count());
        $this->line('Paid: ' . $bookings->where('payment_status', 'paid')->count());
        $this->line('Failed: ' . $bookings->where('payment_status', 'failed')->count());
    }

    private function getStatusIcon($status)
    {
        return match($status) {
            'confirmed' => '✅',
            'pending' => '⏳',
            'cancelled' => '❌',
            'payment_failed' => '💳❌',
            default => '❓'
        };
    }

    private function getPaymentStatusIcon($paymentStatus)
    {
        return match($paymentStatus) {
            'paid' => '💰',
            'pending' => '⏳',
            'failed' => '❌',
            'refunded' => '↩️',
            default => '❓'
        };
    }
}
