<?php

namespace App\Filament\Widgets;

use App\Models\Destination;
use App\Models\Booking;
use App\Models\Activity;
use App\Models\Hotel;
use App\Models\Favorite;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class PopularDestinationsChart extends ChartWidget
{
    protected static ?string $heading = '🗺️ Most Popular Destinations';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'bookings';

    protected function getFilters(): ?array
    {
        return [
            'bookings' => 'By Bookings',
            'favorites' => 'By Favorites',
            'revenue' => 'By Revenue',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        switch ($activeFilter) {
            case 'favorites':
                return $this->getFavoritesData();
            case 'revenue':
                return $this->getRevenueData();
            default:
                return $this->getBookingsData();
        }
    }

    private function getBookingsData(): array
    {
        // Get bookings by destination through polymorphic relationships
        $activityBookings = DB::table('bookings')
            ->join('activities', function($join) {
                $join->on('bookings.bookable_id', '=', 'activities.id')
                     ->where('bookings.bookable_type', '=', Activity::class);
            })
            ->join('destinations', 'activities.destination_id', '=', 'destinations.id')
            ->select('destinations.name', 'destinations.id', DB::raw('count(*) as booking_count'))
            ->groupBy('destinations.id', 'destinations.name');

        $hotelBookings = DB::table('bookings')
            ->join('hotels', function($join) {
                $join->on('bookings.bookable_id', '=', 'hotels.id')
                     ->where('bookings.bookable_type', '=', Hotel::class);
            })
            ->join('destinations', 'hotels.destination_id', '=', 'destinations.id')
            ->select('destinations.name', 'destinations.id', DB::raw('count(*) as booking_count'))
            ->groupBy('destinations.id', 'destinations.name');

        // Combine activity and hotel bookings
        $data = DB::query()
            ->fromSub($activityBookings->unionAll($hotelBookings), 'combined_bookings')
            ->select('name', DB::raw('sum(booking_count) as total_bookings'))
            ->groupBy('name')
            ->orderByDesc('total_bookings')
            ->limit(10)
            ->get();

        // Fallback: Get destinations with most activities and hotels if no bookings
        if ($data->isEmpty()) {
            $data = Destination::withCount(['activities', 'hotels'])
                ->orderByDesc('activities_count')
                ->orderByDesc('hotels_count')
                ->limit(10)
                ->get()
                ->map(function($dest) {
                    return (object)[
                        'name' => $dest->name,
                        'total_bookings' => $dest->activities_count + $dest->hotels_count
                    ];
                });
        }

        return [
            'datasets' => [
                [
                    'label' => 'Number of Bookings',
                    'data' => $data->pluck('total_bookings')->toArray(),
                    'backgroundColor' => [
                        '#ec4899', '#f97316', '#8b5cf6', '#06b6d4', '#10b981',
                        '#f59e0b', '#ef4444', '#6366f1', '#84cc16', '#f43f5e'
                    ],
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $data->pluck('name')->toArray(),
        ];
    }

    private function getFavoritesData(): array
    {
        $data = Favorite::where('favoritable_type', 'App\\Models\\Destination')
            ->join('destinations', 'favorites.favoritable_id', '=', 'destinations.id')
            ->select('destinations.name', DB::raw('count(*) as favorites_count'))
            ->groupBy('destinations.id', 'destinations.name')
            ->orderByDesc('favorites_count')
            ->limit(10)
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Number of Favorites',
                    'data' => $data->pluck('favorites_count')->toArray(),
                    'backgroundColor' => [
                        '#f43f5e', '#ec4899', '#d946ef', '#a855f7', '#8b5cf6',
                        '#7c3aed', '#6366f1', '#3b82f6', '#0ea5e9', '#06b6d4'
                    ],
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $data->pluck('name')->toArray(),
        ];
    }

    private function getRevenueData(): array
    {
        // Get revenue by destination through polymorphic relationships
        $activityRevenue = DB::table('bookings')
            ->join('activities', function($join) {
                $join->on('bookings.bookable_id', '=', 'activities.id')
                     ->where('bookings.bookable_type', '=', Activity::class);
            })
            ->join('destinations', 'activities.destination_id', '=', 'destinations.id')
            ->select('destinations.name', 'destinations.id', DB::raw('sum(bookings.total_amount) as revenue'))
            ->where('bookings.status', 'confirmed')
            ->groupBy('destinations.id', 'destinations.name');

        $hotelRevenue = DB::table('bookings')
            ->join('hotels', function($join) {
                $join->on('bookings.bookable_id', '=', 'hotels.id')
                     ->where('bookings.bookable_type', '=', Hotel::class);
            })
            ->join('destinations', 'hotels.destination_id', '=', 'destinations.id')
            ->select('destinations.name', 'destinations.id', DB::raw('sum(bookings.total_amount) as revenue'))
            ->where('bookings.status', 'confirmed')
            ->groupBy('destinations.id', 'destinations.name');

        // Combine activity and hotel revenue
        $data = DB::query()
            ->fromSub($activityRevenue->unionAll($hotelRevenue), 'combined_revenue')
            ->select('name', DB::raw('sum(revenue) as total_revenue'))
            ->groupBy('name')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Revenue (€)',
                    'data' => $data->pluck('total_revenue')->toArray(),
                    'backgroundColor' => [
                        '#10b981', '#059669', '#047857', '#065f46', '#064e3b',
                        '#6ee7b7', '#34d399', '#10b981', '#059669', '#047857'
                    ],
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $data->pluck('name')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const filter = "' . $this->filter . '";
                            if (filter === "revenue") {
                                return "Revenue: €" + context.parsed.y.toFixed(2);
                            }
                            return context.dataset.label + ": " + context.parsed.y;
                        }'
                    ]
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) {
                            const filter = "' . $this->filter . '";
                            if (filter === "revenue") {
                                return "€" + value.toFixed(0);
                            }
                            return value;
                        }'
                    ],
                ],
                'x' => [
                    'ticks' => [
                        'maxRotation' => 45,
                        'minRotation' => 45,
                    ],
                ],
            ],
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }
}
