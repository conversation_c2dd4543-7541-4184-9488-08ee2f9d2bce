<?php

namespace App\Filament\Widgets;

use App\Models\Destination;
use App\Models\Activity;
use App\Models\Hotel;
use App\Models\Favorite;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class PopularDestinationsChart extends ChartWidget
{
    protected static ?string $heading = '🗺️ Most Popular Destinations';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'bookings';

    protected function getFilters(): ?array
    {
        return [
            'bookings' => 'By Bookings',
            'favorites' => 'By Favorites',
            'revenue' => 'By Revenue',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        switch ($activeFilter) {
            case 'favorites':
                return $this->getFavoritesData();
            case 'revenue':
                return $this->getRevenueData();
            default:
                return $this->getBookingsData();
        }
    }

    private function getBookingsData(): array
    {
        // Use a simpler approach with raw SQL to get destination booking counts

        // Get activity bookings by destination
        $activityBookings = DB::select("
            SELECT d.name, COUNT(*) as booking_count
            FROM bookings b
            JOIN activities a ON b.bookable_id = a.id AND b.bookable_type = ?
            JOIN destinations d ON a.destination_id = d.id
            GROUP BY d.id, d.name
        ", [Activity::class]);

        // Get hotel bookings by destination
        $hotelBookings = DB::select("
            SELECT d.name, COUNT(*) as booking_count
            FROM bookings b
            JOIN hotels h ON b.bookable_id = h.id AND b.bookable_type = ?
            JOIN destinations d ON h.destination_id = d.id
            GROUP BY d.id, d.name
        ", [Hotel::class]);

        // Combine and aggregate the results
        $destinationCounts = [];

        foreach ($activityBookings as $booking) {
            $destinationCounts[$booking->name] = ($destinationCounts[$booking->name] ?? 0) + $booking->booking_count;
        }

        foreach ($hotelBookings as $booking) {
            $destinationCounts[$booking->name] = ($destinationCounts[$booking->name] ?? 0) + $booking->booking_count;
        }

        // Sort by booking count and take top 10
        arsort($destinationCounts);
        $destinationCounts = array_slice($destinationCounts, 0, 10, true);

        // Convert to the format expected by the chart
        $labels = array_keys($destinationCounts);
        $bookingCounts = array_values($destinationCounts);

        // Fallback: Get destinations with most activities and hotels if no bookings
        if (empty($destinationCounts)) {
            $fallbackData = Destination::withCount(['activities', 'hotels'])
                ->orderByDesc('activities_count')
                ->orderByDesc('hotels_count')
                ->limit(10)
                ->get();

            $labels = [];
            $bookingCounts = [];

            foreach ($fallbackData as $dest) {
                $labels[] = $dest->name;
                $bookingCounts[] = $dest->activities_count + $dest->hotels_count;
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Number of Bookings',
                    'data' => $bookingCounts,
                    'backgroundColor' => [
                        '#ec4899', '#f97316', '#8b5cf6', '#06b6d4', '#10b981',
                        '#f59e0b', '#ef4444', '#6366f1', '#84cc16', '#f43f5e'
                    ],
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    private function getFavoritesData(): array
    {
        $data = Favorite::where('favoritable_type', 'App\\Models\\Destination')
            ->join('destinations', 'favorites.favoritable_id', '=', 'destinations.id')
            ->select('destinations.name', DB::raw('count(*) as favorites_count'))
            ->groupBy('destinations.id', 'destinations.name')
            ->orderByDesc('favorites_count')
            ->limit(10)
            ->get();

        // Convert to arrays safely
        $labels = [];
        $favoriteCounts = [];

        foreach ($data as $item) {
            $labels[] = $item->name;
            $favoriteCounts[] = $item->favorites_count;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Number of Favorites',
                    'data' => $favoriteCounts,
                    'backgroundColor' => [
                        '#f43f5e', '#ec4899', '#d946ef', '#a855f7', '#8b5cf6',
                        '#7c3aed', '#6366f1', '#3b82f6', '#0ea5e9', '#06b6d4'
                    ],
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    private function getRevenueData(): array
    {
        // Get activity revenue by destination
        $activityRevenue = DB::select("
            SELECT d.name, SUM(b.total_amount) as revenue
            FROM bookings b
            JOIN activities a ON b.bookable_id = a.id AND b.bookable_type = ?
            JOIN destinations d ON a.destination_id = d.id
            WHERE b.status = 'confirmed'
            GROUP BY d.id, d.name
        ", [Activity::class]);

        // Get hotel revenue by destination
        $hotelRevenue = DB::select("
            SELECT d.name, SUM(b.total_amount) as revenue
            FROM bookings b
            JOIN hotels h ON b.bookable_id = h.id AND b.bookable_type = ?
            JOIN destinations d ON h.destination_id = d.id
            WHERE b.status = 'confirmed'
            GROUP BY d.id, d.name
        ", [Hotel::class]);

        // Combine and aggregate the results
        $destinationRevenue = [];

        foreach ($activityRevenue as $revenue) {
            $destinationRevenue[$revenue->name] = ($destinationRevenue[$revenue->name] ?? 0) + $revenue->revenue;
        }

        foreach ($hotelRevenue as $revenue) {
            $destinationRevenue[$revenue->name] = ($destinationRevenue[$revenue->name] ?? 0) + $revenue->revenue;
        }

        // Sort by revenue and take top 10
        arsort($destinationRevenue);
        $destinationRevenue = array_slice($destinationRevenue, 0, 10, true);

        // Convert to the format expected by the chart
        $labels = array_keys($destinationRevenue);
        $revenueValues = array_values($destinationRevenue);

        return [
            'datasets' => [
                [
                    'label' => 'Revenue (€)',
                    'data' => $revenueValues,
                    'backgroundColor' => [
                        '#10b981', '#059669', '#047857', '#065f46', '#064e3b',
                        '#6ee7b7', '#34d399', '#10b981', '#059669', '#047857'
                    ],
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const filter = "' . $this->filter . '";
                            if (filter === "revenue") {
                                return "Revenue: €" + context.parsed.y.toFixed(2);
                            }
                            return context.dataset.label + ": " + context.parsed.y;
                        }'
                    ]
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) {
                            const filter = "' . $this->filter . '";
                            if (filter === "revenue") {
                                return "€" + value.toFixed(0);
                            }
                            return value;
                        }'
                    ],
                ],
                'x' => [
                    'ticks' => [
                        'maxRotation' => 45,
                        'minRotation' => 45,
                    ],
                ],
            ],
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }
}
