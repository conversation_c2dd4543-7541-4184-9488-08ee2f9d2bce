<?php

namespace App\Listeners;

use App\Events\NewEventCreated;
use App\Jobs\SendEventNotificationEmail;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendEventNotifications implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NewEventCreated $event): void
    {
        Log::info('SendEventNotifications listener triggered for event: ' . $event->event->id);

        // Get all users who have favorited this destination
        $destination = $event->event->destination;

        Log::info('Looking for users who favorited destination: ' . $destination->id . ' (' . $destination->name . ')');

        // Get users who have favorited this destination
        $users = User::whereHas('favorites', function ($query) use ($destination) {
            $query->where('favoritable_type', 'App\Models\Destination')
                  ->where('favoritable_id', $destination->id);
        })->get();

        Log::info('Found ' . $users->count() . ' users who favorited this destination');

        // Dispatch email jobs for each user
        foreach ($users as $user) {
            Log::info('Dispatching email job for user: ' . $user->email);
            SendEventNotificationEmail::dispatch($event->event, $user);
        }

        Log::info('All email jobs dispatched for event: ' . $event->event->id);
    }
}
