# 2.4 ENVIRONNEMENT TECHNIQUE - TRAVEL PLATFORM

## 2.4.1 Environnement logiciel

Le choix des logiciels pour un projet dès le début est très important, car il permet de faciliter le travail et permet aussi de gagner beaucoup de temps.

**Visual Studio Code (VSCode)** : VSCode est un éditeur de code source développé par Microsoft. Il est léger, puissant et extensible, supportant une multitude de langages de programmation grâce à des extensions. Ses fonctionnalités incluent le debugging intégré, la mise en surbrillance de la syntaxe, les snippets de code, la gestion Git intégrée, et de nombreuses autres fonctionnalités qui en font un outil populaire parmi les développeurs. Pour notre projet, nous utilisons des extensions spécifiques à Laravel, PHP, et Livewire pour optimiser le développement.

**Laragon** : Laragon est un environnement de développement local pour Windows qui permet de créer rapidement un environnement LAMP/LEMP. Il offre une interface simple pour gérer Apache/Nginx, MySQL/MariaDB, PHP, et Node.js. Laragon facilite la gestion des hôtes virtuels, des certificats SSL, et l'installation de frameworks comme Laravel. C'est un outil essentiel pour le développement local de notre plateforme de réservation touristique.

**SQLite Browser** : SQLite Browser est un outil graphique de gestion de bases de données SQLite destiné aux développeurs. Il offre une interface utilisateur intuitive pour effectuer des requêtes, visualiser et manipuler les données, et gérer la structure de la base de données. Pour notre projet en développement, SQLite nous permet de prototyper rapidement sans configuration complexe.

**Postman** : Postman est un outil collaboratif pour le développement et les tests d'API. Il permet aux développeurs de concevoir, tester, documenter et surveiller les API de manière efficace. Postman offre une interface utilisateur intuitive pour envoyer des requêtes HTTP, visualiser les réponses, créer des collections de requêtes, et automatiser les tests. C'est un outil essentiel pour tester notre API Gemini et les endpoints de notre application.

**Git & GitHub** : Git est un système de contrôle de version distribué qui permet de suivre les modifications du code source. GitHub est une plateforme d'hébergement de code qui utilise Git. Ces outils sont essentiels pour la collaboration, la sauvegarde du code, et le déploiement de notre Travel Platform.

## 2.4.2 Environnement de développement

**Laravel 11 (PHP 8.2+)** : Laravel est un framework de développement web PHP élégant et expressif, conçu pour créer des applications web modernes et robustes. Laravel 11 offre des fonctionnalités avancées telles que l'ORM Eloquent, le système de routage, les migrations de base de données, l'authentification intégrée, et un écosystème riche. Avec PHP 8.2+, nous bénéficions des dernières améliorations de performance et des nouvelles fonctionnalités du langage. Laravel facilite le développement rapide d'applications sécurisées et maintenables.

**Livewire 3** : Livewire est un framework full-stack pour Laravel qui permet de créer des interfaces utilisateur dynamiques sans écrire de JavaScript. Livewire 3 offre une approche réactive moderne avec des composants interactifs, la gestion d'état en temps réel, et une intégration transparente avec Laravel. Il permet de créer des expériences utilisateur riches tout en conservant la simplicité du développement côté serveur. Pour notre plateforme, Livewire gère les composants interactifs comme le chatbot, les modals de réservation, et la gestion des favoris.

**Filament 3** : Filament est un framework d'administration moderne pour Laravel qui permet de créer rapidement des interfaces d'administration élégantes et fonctionnelles. Filament 3 offre des composants pré-construits, une gestion avancée des formulaires, des tableaux de données, et un système de permissions intégré. Il facilite la création de panels d'administration robustes avec une expérience utilisateur moderne. Dans notre projet, Filament gère l'administration des destinations, activités, hôtels, et utilisateurs.

**TailwindCSS 4** : TailwindCSS est un framework CSS utility-first qui permet de créer rapidement des interfaces utilisateur personnalisées. TailwindCSS 4 offre une approche moderne du styling avec des classes utilitaires, un système de design cohérent, et une optimisation automatique du CSS. Il permet de créer des designs responsives et accessibles sans écrire de CSS personnalisé. Notre Travel Platform utilise TailwindCSS pour un design moderne avec un thème rose/violet cohérent.

**Alpine.js** : Alpine.js est un framework JavaScript léger qui offre la réactivité et la nature déclarative de frameworks comme Vue ou React, mais avec une complexité beaucoup moindre. Il s'intègre parfaitement avec Livewire pour ajouter des interactions côté client sans la complexité d'un SPA. Dans notre projet, Alpine.js gère les animations, les modals, et les interactions utilisateur avancées.

## 2.4.3 Architecture logique

Le choix de l'architecture est très important et a un impact direct sur la performance et la rapidité de l'application. Selon les technologies que nous devons implémenter, nous prenons le choix de l'architecture MVC (Model-View-Controller) avec une approche full-stack Laravel/Livewire. Cette architecture est composée de plusieurs couches :

**Couche de données persistantes**
Cette couche correspond aux données qui sont destinées à être conservées sur la durée, voire de manière définitive. Au sein de cette couche, nous utilisons SQLite pour le développement et MySQL/PostgreSQL pour la production. L'ORM Eloquent de Laravel facilite la gestion des relations entre les modèles (User, Activity, Destination, Booking, etc.) et assure la cohérence des données.

**Couche de traitement métier**
Le traitement métier des données correspond à la mise en œuvre de l'ensemble des règles de gestion et de la logique applicative. Avec l'utilisation du framework Laravel, nous pouvons traiter les données déjà sauvegardées dans la base de données. Cette couche inclut :
- Les Services métier (GeminiService, ChatbotService, BookingService)
- Les Contrôleurs Laravel pour les routes web
- Les Composants Livewire pour la logique interactive
- Les Jobs et Queues pour les tâches asynchrones

**Couche de présentation**
Cette couche correspond à l'affichage, la restitution sur le navigateur, et le dialogue avec l'utilisateur. Pour notre projet, nous utilisons :
- **Livewire 3** comme framework de composants réactifs
- **Blade** comme moteur de templates Laravel
- **TailwindCSS 4** pour le styling et le design system
- **Alpine.js** pour les interactions côté client
- **Leaflet** pour les cartes interactives

**Couche d'intégration externe**
Cette couche gère les communications avec les services externes :
- **Google Gemini API** pour l'intelligence artificielle du chatbot
- **Google OAuth** pour l'authentification sociale
- **OpenStreetMap/Leaflet** pour la géolocalisation
- **Services de stockage** pour les images et médias

## 2.4.4 Architecture MVC Laravel

**Figure 1: Architecture MVC Laravel**

**Model (Modèle)** : Gère les données de manière logique, il traite essentiellement les données. Le modèle est connecté à la base de données, donc tout ce que vous faites avec les données ; ajouter ou récupérer des données se fait dans le composant modèle. Il répond aux demandes du contrôleur car le contrôleur ne communique jamais directement avec la base de données. Le modèle communique avec la base de données dans les deux sens, puis il fournit les données nécessaires au contrôleur. Dans notre Travel Platform, les modèles incluent User, Activity, Destination, Booking, Hotel, Transfer, etc.

**View (Vue)** : La représentation des données est effectuée par le composant vue. Il génère l'interface utilisateur pour l'utilisateur. Les vues sont créées à partir des données collectées par le composant modèle, mais ces données ne sont pas prises directement mais via le contrôleur, donc la vue ne communique qu'avec le contrôleur. Dans notre projet, nous utilisons Blade comme moteur de templates et Livewire pour les composants réactifs.

**Controller (Contrôleur)** : Il est connu comme l'homme principal car le contrôleur est le composant qui permet l'interconnexion entre les vues et le modèle, il agit donc comme un intermédiaire. Le contrôleur n'a pas à se soucier de la gestion de la logique des données, il dit simplement au modèle quoi faire. Après avoir reçu les données du modèle, il les traite, puis il prend toutes ces informations et les envoie à la vue en expliquant comment les représenter à l'utilisateur.

## 2.4.5 Architecture logicielle

"Travel Platform" est composée de 3 parties principales : l'interface web, le panel d'administration et les services backend.

L'interface web et le panel d'administration consomment les services Laravel qui sont construits avec PHP 8.2+, Laravel 11, et Livewire 3.

Ces services communiquent avec des APIs externes comme Google Gemini pour l'IA et Google OAuth pour l'authentification.

Les composants Livewire permettent une communication en temps réel entre le frontend et le backend sans nécessiter d'API REST traditionnelle, grâce à leur nature full-stack.

### ******* Architecture Backend Laravel

Laravel utilise une architecture basée sur le pattern MVC avec des améliorations modernes :

**Composants de l'architecture Laravel :**

**Requêtes** : Selon les actions qu'un utilisateur doit effectuer, les requêtes vers le serveur peuvent être soit bloquantes (complexes) soit non-bloquantes (simples). Laravel gère ces requêtes via son système de routage et middleware.

**Serveur Laravel** : Le serveur Laravel accepte les requêtes utilisateur, les traite via les contrôleurs et composants Livewire, et retourne les résultats aux utilisateurs.

**Queue System** : Le système de files d'attente de Laravel stocke les tâches asynchrones et les traite séquentiellement via les workers. Cela inclut l'envoi d'emails, le traitement d'images, et les appels API externes.

**Service Container** : Le conteneur de services Laravel gère l'injection de dépendances et la résolution des services. Il contient tous les services disponibles pour traiter les opérations requises.

**Event Loop** : Laravel utilise un système d'événements pour découpler les composants et permettre une architecture modulaire. Les événements sont déclenchés lors d'actions spécifiques (création d'utilisateur, réservation, etc.).

**Services Externes** : Pour gérer les requêtes complexes, des services externes sont utilisés. Ils peuvent être de tout type (IA Gemini, authentification OAuth, géolocalisation, etc.).

## 2.4.6 Architecture physique

Une architecture 3-tiers est une architecture client-serveur modulaire qui consiste en une couche de présentation, une couche d'application et une couche de données. La couche de données stocke les informations, la couche d'application gère la logique et la couche de présentation est une interface utilisateur graphique (GUI) qui communique avec les deux autres couches. Les trois couches sont logiques, pas physiques, et peuvent ou non s'exécuter sur le même serveur physique.

**Couche de présentation** : Cette couche, qui est construite avec Livewire 3, Blade, TailwindCSS 4 et Alpine.js, est déployée sur un dispositif informatique via un navigateur web. La couche de présentation communique avec les autres couches via des appels de composants Livewire et des requêtes HTTP traditionnelles.

**Couche d'application** : La couche d'application, qui peut également être appelée couche logique, est écrite en PHP avec le framework Laravel et contient la logique métier qui supporte les fonctions principales de l'application. Cette couche inclut les contrôleurs, les services (GeminiService, ChatbotService), les composants Livewire, et les jobs. La couche d'application sous-jacente peut être hébergée sur des serveurs distribués dans le cloud ou sur un serveur dédié en interne, selon la puissance de traitement requise par l'application.

**Couche de données** : La couche de données consiste en une base de données et un programme pour gérer l'accès en lecture et écriture à la base de données. Cette couche peut également être appelée couche de stockage et peut être hébergée sur site ou dans le cloud. Les systèmes de base de données populaires pour gérer l'accès en lecture/écriture incluent SQLite (développement), MySQL, PostgreSQL, et les solutions cloud comme Amazon RDS.

La figure ci-dessous met en évidence la décomposition de notre architecture ainsi que les technologies utilisées par chaque couche :

```
┌─────────────────────────────────────────────────────────────┐
│                    COUCHE PRÉSENTATION                     │
│  Livewire 3 + Blade + TailwindCSS 4 + Alpine.js          │
│              (Interface Utilisateur)                       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  COUCHE APPLICATION                        │
│     Laravel 11 + Services + Contrôleurs + Composants      │
│              (Logique Métier)                              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   COUCHE DONNÉES                           │
│        SQLite/MySQL + Eloquent ORM + Migrations           │
│              (Persistance des Données)                     │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                SERVICES EXTERNES                           │
│    Google Gemini API + Google OAuth + OpenStreetMap       │
│              (Intégrations Tierces)                        │
└─────────────────────────────────────────────────────────────┘
```

Cette architecture garantit une séparation claire des responsabilités, une maintenabilité élevée, et une scalabilité optimale pour notre plateforme de réservation touristique.
