<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\HasLocation;

class Activity extends Model
{
    use HasFactory;
   // use HasLocation;

    // Add 'title' to the fillable property
    protected $fillable = [
        'title',
        'description',
        'highlights',
        'hotspot_id',
        'image',
        'tax',
        'details_tax',
        'activity_type',
        'activity_nature',
        'difficulty_level',
        'duration', // Add duration field
        'recommend_equipments',
        'requires_adult_for_booking',
        'min_age',
        'max_age',
        'min_travelers_per_booking',
        'max_travelers_per_booking',
        'type_of_service',
        'min_participant',
        'max_participant',
        'pickup_and_meet_at_start_point',
        'starting_point',
        'meeting_point',
        'destination_id',
        'end_point_return_to',
        'public_address',
        'map_address',
        'street',
        'city',
        'state',
        'zip',
        'latitude',
        'longitude',
    ];

    /**
     * Accessor for name (uses title field)
     */
    public function getNameAttribute()
    {
        return $this->title;
    }

    /**
     * Accessor for difficulty (uses difficulty_level field)
     */
    public function getDifficultyAttribute()
    {
        return $this->difficulty_level;
    }

    /**
     * Get the destination that owns the activity.
     */
    public function destination()
    {
        return $this->belongsTo(Destination::class);
    }

    /**
     * Get the activity contracts for the activity.
     */
    public function activityContracts()
    {
        return $this->belongsToMany(ActivityContract::class);
    }

    /**
     * Get the meeting points for the activity.
     */
    public function meetingPoints()
    {
        return $this->belongsToMany(MeetingPoint::class);
    }

    /**
     * Get the starting points for the activity.
     */
    public function startingPoints()
    {
        return $this->belongsToMany(MeetingPoint::class);
    }

    /**
     * Get the ending points for the activity.
     */
    public function endingPoints()
    {
        return $this->belongsToMany(MeetingPoint::class);
    }

}