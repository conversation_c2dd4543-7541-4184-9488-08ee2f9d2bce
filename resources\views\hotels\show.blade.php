@extends('layouts.web')

@section('title', $hotel->name)

@push('styles')
    <style>
        #hotel-map {
            height: 256px !important;
            width: 100% !important;
            z-index: 1;
        }
    </style>
@endpush

@section('content')
    <div class="min-h-screen bg-white">
        <!-- Hero Section -->
        <div class="relative h-96 overflow-hidden">
            @if ($hotel->image)
                <img src="{{ asset('storage/' . $hotel->image) }}" class="w-full h-full object-cover"
                    alt="{{ $hotel->name }}">
            @else
                <div class="w-full h-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center">
                    <span class="text-white text-6xl">🏨</span>
                </div>
            @endif

            <!-- Overlay -->
            <div class="absolute inset-0 bg-black bg-opacity-40"></div>

            <!-- Hotel Info Overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                <div class="max-w-7xl mx-auto">
                    <div class="flex items-center mb-4">
                        @if ($hotel->rating)
                            <div class="flex items-center mr-4">
                                @for ($i = 1; $i <= 5; $i++)
                                    @if ($i <= $hotel->rating)
                                        <svg class="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path
                                                d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                                        </svg>
                                    @else
                                        <svg class="w-5 h-5 text-gray-300 fill-current" viewBox="0 0 20 20">
                                            <path
                                                d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                                        </svg>
                                    @endif
                                @endfor
                                <span class="ml-2 text-sm">{{ $hotel->rating }}/5</span>
                            </div>
                        @endif

                        @if ($hotel->Type)
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">{{ $hotel->Type }}</span>
                        @endif
                    </div>

                    <h1 class="text-4xl font-bold mb-2">{{ $hotel->name }}</h1>

                    @if ($hotel->address || $hotel->city)
                        <div class="flex items-center text-lg opacity-90">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            {{ $hotel->address ? $hotel->address . ', ' : '' }}{{ $hotel->city }}{{ $hotel->country ? ', ' . $hotel->country : '' }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Description -->
                    @if ($hotel->description)
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Hotel</h2>
                            <div class="prose prose-lg text-gray-600 leading-relaxed">
                                {!! nl2br(e($hotel->description)) !!}
                            </div>
                        </div>
                    @endif

                    <!-- Short Description -->
                    @if ($hotel->short_description && $hotel->short_description !== $hotel->description)
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <p class="text-gray-700 text-lg leading-relaxed">
                                    {{ $hotel->short_description }}
                                </p>
                            </div>
                        </div>
                    @endif

                    <!-- Location & Map -->
                    @if ($hotel->address || $hotel->city)
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">📍 Location</h2>

                            <!-- Address Information -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                                <div class="flex items-start">
                                    <div
                                        class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-semibold text-gray-900 mb-2">Hotel Address</h3>
                                        <div class="text-gray-600 space-y-1">
                                            @if ($hotel->address)
                                                <p>{{ $hotel->address }}</p>
                                            @endif
                                            @if ($hotel->street)
                                                <p>{{ $hotel->street }}</p>
                                            @endif
                                            <p>
                                                @if ($hotel->city)
                                                    {{ $hotel->city }}
                                                @endif
                                                @if ($hotel->state && $hotel->city)
                                                    , {{ $hotel->state }}
                                                @endif
                                                @if ($hotel->zip)
                                                    {{ $hotel->zip }}
                                                @endif
                                            </p>
                                            @if ($hotel->country)
                                                <p class="font-medium">{{ $hotel->country }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @php
                                $addressParts = [
                                    $hotel->address ?? '',
                                    $hotel->street ?? '',
                                    $hotel->city ?? '',
                                    $hotel->state ?? '',
                                    $hotel->country ?? '',
                                ];
                                $address = trim(collect($addressParts)->filter()->implode(', '));
                            @endphp

                            @if ($address)
                                <div class="mb-4">
                                    <a href="https://www.google.com/maps/search/?api=1&query={{ urlencode($address) }}"
                                        target="_blank"
                                        class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14">
                                            </path>
                                        </svg>
                                        Open in Google Maps
                                    </a>
                                </div>
                            @endif

                            <!-- Interactive Map -->
                            <div class="relative h-64 bg-gray-200 rounded-lg overflow-hidden mb-6">
                                <div id="hotel-map" class="w-full h-full rounded-lg"></div>
                            </div>

                            <!-- Leaflet CSS and JS -->
                            <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
                            <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

                            <script>
                                // Wait for everything to load
                                setTimeout(function() {
                                    console.log('Initializing hotel map...');

                                    // Check if Leaflet is loaded
                                    if (typeof L === 'undefined') {
                                        console.error('Leaflet not loaded');
                                        return;
                                    }

                                    // Check if container exists
                                    const mapContainer = document.getElementById('hotel-map');
                                    if (!mapContainer) {
                                        console.error('Map container not found');
                                        return;
                                    }

                                    console.log('Creating map...');

                                    // Initialize map
                                    const map = L.map('hotel-map').setView([38.7223, -9.1393], 13);

                                    // Add tiles
                                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                        attribution: '© OpenStreetMap contributors'
                                    }).addTo(map);

                                    console.log('Map created successfully');

                                    // Address for geocoding
                                    const address = @json($address ?? '');
                                    console.log('Address:', address);

                                    if (address) {
                                        // Geocode using Nominatim
                                        fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`)
                                            .then(response => response.json())
                                            .then(data => {
                                                console.log('Geocoding result:', data);
                                                if (data && data.length > 0) {
                                                    const lat = parseFloat(data[0].lat);
                                                    const lon = parseFloat(data[0].lon);
                                                    console.log('Coordinates:', lat, lon);

                                                    // Set map view
                                                    map.setView([lat, lon], 15);

                                                    // Add marker
                                                    const marker = L.marker([lat, lon]).addTo(map);

                                                    // Add popup
                                                    marker.bindPopup(`
                                                        <div style="text-align: center;">
                                                            <strong>{{ $hotel->name }}</strong><br>
                                                            <span style="color: #666;">${address}</span>
                                                            @if ($hotel->rating)
                                                            <br><span style="color: #f59e0b;">⭐ {{ $hotel->rating }}/5</span>
                                                            @endif
                                                        </div>
                                                    `).openPopup();

                                                    console.log('Marker added successfully');
                                                } else {
                                                    console.warn('No geocoding results found');
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Geocoding error:', error);
                                            });
                                    } else {
                                        console.log('No address provided');
                                    }
                                }, 1000); // Wait 1 second for everything to load
                            </script>


                        </div>
                    @endif

                    <!-- Available Rooms Section -->
                    @if ($activeRooms && $activeRooms->count() > 0)
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Available Rooms</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach ($activeRooms as $room)
                                    <div
                                        class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-lg transition-shadow duration-300">
                                        <!-- Room Header -->
                                        <div class="flex items-center justify-between mb-4">
                                            <h3 class="text-lg font-semibold text-gray-900">{{ $room['name'] }}</h3>
                                            <span
                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </div>

                                        <!-- Room Details -->
                                        <div class="space-y-3">
                                            <!-- Price -->
                                            @if ($room['price'])
                                                <div class="flex items-center justify-between">
                                                    <span class="text-sm text-gray-600">Price per night</span>
                                                    <span
                                                        class="text-lg font-bold text-pink-600">{{ formatPrice($room['price']) }}</span>
                                                </div>
                                            @else
                                                <div class="flex items-center justify-between">
                                                    <span class="text-sm text-gray-600">Price per night</span>
                                                    <span class="text-sm text-gray-400">Contact for pricing</span>
                                                </div>
                                            @endif

                                            <!-- Availability -->
                                            @if ($room['availability'])
                                                <div class="flex items-center justify-between">
                                                    <span class="text-sm text-gray-600">Rooms available</span>
                                                    <span
                                                        class="text-sm font-medium text-gray-900">{{ $room['availability'] }}
                                                        rooms</span>
                                                </div>
                                            @else
                                                <div class="flex items-center justify-between">
                                                    <span class="text-sm text-gray-600">Availability</span>
                                                    <span class="text-sm text-gray-400">Contact hotel</span>
                                                </div>
                                            @endif

                                            <!-- Room Features -->
                                            <div class="pt-3 border-t border-gray-100">
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0">
                                                        </path>
                                                    </svg>
                                                    Standard amenities included
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Book Button -->
                                        <div class="mt-4 pt-4 border-t border-gray-100">
                                            <button
                                                onclick="openAvailabilityModal({{ $room['id'] }}, '{{ $room['name'] }}')"
                                                class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                                                Check Availability
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Available Rooms</h2>
                            <div class="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
                                <div
                                    class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Rooms Available</h3>
                                <p class="text-gray-600">Currently no active rooms are available for this hotel. Please
                                    contact the hotel directly for more information.</p>
                            </div>
                        </div>
                    @endif

                    <!-- Amenities & Features -->
                    <div class="mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Hotel Features</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if ($hotel->roomcount)
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0">
                                            </path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">Rooms Available</h3>
                                        <p class="text-gray-600">{{ $hotel->roomcount }} rooms</p>
                                    </div>
                                </div>
                            @endif

                            @if ($hotel->from_check_in_time && $hotel->to_check_in_time)
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">Check-in Time</h3>
                                        <p class="text-gray-600">{{ $hotel->from_check_in_time }} -
                                            {{ $hotel->to_check_in_time }}</p>
                                    </div>
                                </div>
                            @endif

                            @if ($hotel->from_check_out_time && $hotel->to_check_out_time)
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">Check-out Time</h3>
                                        <p class="text-gray-600">{{ $hotel->from_check_out_time }} -
                                            {{ $hotel->to_check_out_time }}</p>
                                    </div>
                                </div>
                            @endif

                            @if ($hotel->sustainability_score)
                                <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                            </path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900">Sustainability Score</h3>
                                        <p class="text-gray-600">{{ $hotel->sustainability_score }}/10</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Contact Information -->
                    <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Information</h3>

                        @if ($hotel->phone)
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                    </path>
                                </svg>
                                <span class="text-gray-600">{{ $hotel->phone }}</span>
                            </div>
                        @endif

                        @if ($hotel->email)
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span class="text-gray-600">{{ $hotel->email }}</span>
                            </div>
                        @endif

                        @if ($hotel->website)
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9">
                                    </path>
                                </svg>
                                <a href="{{ $hotel->website }}" target="_blank"
                                    class="text-pink-600 hover:text-pink-700">Visit Website</a>
                            </div>
                        @endif
                    </div>

                    <!-- Book Now Section -->
                    <div class="bg-gradient-to-r from-pink-500 to-purple-600 rounded-xl p-6 text-white">
                        <h3 class="text-xl font-bold mb-4">Ready to Book?</h3>
                        <p class="mb-6 opacity-90">Contact the hotel directly or visit their website to make a reservation.
                        </p>

                        <div class="space-y-3">
                            @if ($hotel->phone)
                                <a href="tel:{{ $hotel->phone }}"
                                    class="block w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-center py-3 px-4 rounded-lg font-medium transition-colors">
                                    📞 Call Hotel
                                </a>
                            @endif

                            @if ($hotel->website)
                                <a href="{{ $hotel->website }}" target="_blank"
                                    class="block w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-center py-3 px-4 rounded-lg font-medium transition-colors">
                                    🌐 Visit Website
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Availability Modal -->
    <div id="availabilityModal"
        class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-900" id="modalRoomName">Check Availability</h3>
                <button onclick="closeAvailabilityModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <!-- Date Selection Form -->
                <form id="availabilityForm" class="space-y-4">
                    <div>
                        <label for="checkIn" class="block text-sm font-medium text-gray-700 mb-2">Check-in Date</label>
                        <input type="date" id="checkIn" name="check_in" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                            min="{{ date('Y-m-d') }}">
                    </div>

                    <div>
                        <label for="checkOut" class="block text-sm font-medium text-gray-700 mb-2">Check-out Date</label>
                        <input type="date" id="checkOut" name="check_out" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                            min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                    </div>

                    <button type="submit"
                        class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all duration-300">
                        Get Pricing
                    </button>
                </form>

                <!-- Loading State -->
                <div id="loadingState" class="hidden text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
                    <p class="mt-2 text-gray-600">Checking availability...</p>
                </div>

                <!-- Pricing Results -->
                <div id="pricingResults" class="hidden mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold text-gray-900 mb-3">Pricing Details</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Season:</span>
                            <span class="font-medium" id="seasonName">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Price per night:</span>
                            <span class="font-medium" id="pricePerNight">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Number of nights:</span>
                            <span class="font-medium" id="numberOfNights">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Availability:</span>
                            <span class="font-medium" id="roomAvailability">-</span>
                        </div>
                        <div class="flex justify-between border-t border-gray-200 pt-2 mt-2">
                            <span class="text-gray-900 font-semibold">Total Price:</span>
                            <span class="text-pink-600 font-bold text-lg" id="totalPrice">-</span>
                        </div>
                    </div>

                    <!-- Book Now Button -->
                    <button
                        class="w-full mt-4 bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-300">
                        Book Now
                    </button>
                </div>

                <!-- Error State -->
                <div id="errorState" class="hidden mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-red-700" id="errorMessage">An error occurred while checking availability.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentRoomId = null;

        function openAvailabilityModal(roomId, roomName) {
            currentRoomId = roomId;
            document.getElementById('modalRoomName').textContent = `Check Availability - ${roomName}`;
            document.getElementById('availabilityModal').classList.remove('hidden');

            // Reset form and states
            document.getElementById('availabilityForm').reset();
            hideAllStates();

            // Set minimum dates
            const today = new Date().toISOString().split('T')[0];
            const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            document.getElementById('checkIn').min = today;
            document.getElementById('checkOut').min = tomorrow;
        }

        function closeAvailabilityModal() {
            document.getElementById('availabilityModal').classList.add('hidden');
            currentRoomId = null;
        }

        function hideAllStates() {
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('pricingResults').classList.add('hidden');
            document.getElementById('errorState').classList.add('hidden');
        }

        // Update check-out minimum date when check-in changes
        document.getElementById('checkIn').addEventListener('change', function() {
            const checkInDate = new Date(this.value);
            const nextDay = new Date(checkInDate.getTime() + 24 * 60 * 60 * 1000);
            document.getElementById('checkOut').min = nextDay.toISOString().split('T')[0];

            // Clear check-out if it's before the new minimum
            const checkOutInput = document.getElementById('checkOut');
            if (checkOutInput.value && checkOutInput.value <= this.value) {
                checkOutInput.value = '';
            }
        });

        // Handle form submission
        document.getElementById('availabilityForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const checkIn = document.getElementById('checkIn').value;
            const checkOut = document.getElementById('checkOut').value;

            if (!checkIn || !checkOut) {
                showError('Please select both check-in and check-out dates.');
                return;
            }

            if (checkOut <= checkIn) {
                showError('Check-out date must be after check-in date.');
                return;
            }

            // Show loading state
            hideAllStates();
            document.getElementById('loadingState').classList.remove('hidden');

            // Make AJAX request
            fetch('{{ route('hotels.seasonal-pricing') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        room_id: currentRoomId,
                        check_in: checkIn,
                        check_out: checkOut
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideAllStates();

                    if (data.success) {
                        showPricingResults(data.data);
                    } else {
                        showError(data.message || 'Unable to get pricing for selected dates.');
                    }
                })
                .catch(error => {
                    hideAllStates();
                    showError('An error occurred while checking availability. Please try again.');
                    console.error('Error:', error);
                });
        });

        function showPricingResults(data) {
            document.getElementById('seasonName').textContent = data.season;
            document.getElementById('pricePerNight').textContent = data.formatted_price_per_night;
            document.getElementById('numberOfNights').textContent = `${data.nights} night${data.nights !== 1 ? 's' : ''}`;
            document.getElementById('roomAvailability').textContent =
                `${data.availability} room${data.availability !== 1 ? 's' : ''} available`;
            document.getElementById('totalPrice').textContent = data.formatted_total_price;

            document.getElementById('pricingResults').classList.remove('hidden');
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorState').classList.remove('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('availabilityModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAvailabilityModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !document.getElementById('availabilityModal').classList.contains('hidden')) {
                closeAvailabilityModal();
            }
        });
    </script>
@endsection
