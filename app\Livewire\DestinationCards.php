<?php

namespace App\Livewire;

use Livewire\Component;

class DestinationCards extends Component
{
    public $destinations = [];
    public $title = "Select the city or cities you'd like to visit";

    public function mount($destinations = [], $title = null)
    {
        $this->destinations = $destinations;
        if ($title) {
            $this->title = $title;
        }
    }

    public function selectDestination($destinationId)
    {
        // Émettre un événement pour informer le chatbot parent de la sélection
        $this->dispatch('destination-selected', destinationId: $destinationId)->to('chatbot');
    }

    public function render()
    {
        return view('livewire.destination-cards');
    }
}
