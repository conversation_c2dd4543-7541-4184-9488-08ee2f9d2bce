<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Models\Booking;
use App\Services\StripeService;
use App\Mail\BookingConfirmation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

#[Layout('layouts.app')]
class PaymentPage extends Component
{
    public $booking;
    public $paymentIntentId = '';
    public $clientSecret = '';
    public $processing = false;

    public function mount($bookingId)
    {
        // Find the booking and verify it belongs to the authenticated user
        $this->booking = Booking::with(['activity', 'activity.destination'])
            ->where('id', $bookingId)
            ->where('user_id', Auth::id())
            ->where('status', 'pending')
            ->first();

        if (!$this->booking) {
            session()->flash('error', 'Booking not found or already processed.');
            return redirect()->route('mytrips');
        }

        // Create Stripe Payment Intent
        $this->initializePayment();
    }

    public function initializePayment()
    {
        try {
            $stripeService = new StripeService();

            $paymentIntentResult = $stripeService->createPaymentIntent(
                $this->booking->total_price,
                'usd',
                [
                    'booking_id' => $this->booking->id,
                    'activity_id' => $this->booking->activity_id,
                    'user_id' => Auth::id(),
                    'booking_date' => $this->booking->booking_date,
                    'booking_time' => $this->booking->booking_time,
                ]
            );

            if ($paymentIntentResult['success']) {
                // Update booking with payment intent ID
                $this->booking->update([
                    'payment_intent_id' => $paymentIntentResult['payment_intent_id']
                ]);

                $this->paymentIntentId = $paymentIntentResult['payment_intent_id'];
                $this->clientSecret = $paymentIntentResult['client_secret'];
            } else {
                session()->flash('error', 'Unable to initialize payment. Please try again.');
                return redirect()->route('mytrips');
            }
        } catch (\Exception $e) {
            Log::error('Payment initialization failed: ' . $e->getMessage());
            session()->flash('error', 'Payment initialization failed. Please try again.');
            return redirect()->route('mytrips');
        }
    }

    public function processPayment()
    {
        // This method is called after successful Stripe payment
        if (!$this->paymentIntentId) {
            session()->flash('error', 'Payment initialization failed. Please try again.');
            return;
        }

        // Update booking status to confirmed after successful payment
        $this->booking->update([
            'status' => 'confirmed',
            'payment_status' => 'paid'
        ]);

        // Send confirmation email
        try {
            Mail::to($this->booking->user->email)->send(new BookingConfirmation($this->booking));
            Log::info('Booking confirmation email sent to: ' . $this->booking->user->email);
        } catch (\Exception $e) {
            Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
            // Don't fail the payment process if email fails
        }

        // Show success message and redirect
        session()->flash('success', 'Payment successful! Your booking has been confirmed. Check your email for confirmation details.');
        return redirect()->route('mytrips');
    }

    public function cancelPayment()
    {
        // Redirect back to My Trips without processing payment
        return redirect()->route('mytrips');
    }

    public function render()
    {
        return view('livewire.payment-page');
    }
}
