<?php

namespace App\Filament\Contracts\Resources\HotelContractResource\Pages;

use App\Filament\Contracts\Resources\HotelContractResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditHotelContract extends EditRecord
{
    protected static string $resource = HotelContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Preserve policy_details and offer_details when saving the main form
        Log::info('=== MUTATE FORM DATA BEFORE SAVE ===', [
            'incoming_policies' => $data['policies'] ?? 'NOT SET',
            'policies_count' => isset($data['policies']) ? count($data['policies']) : 0,
            'incoming_special_offers' => $data['special_offers'] ?? 'NOT SET',
            'special_offers_count' => isset($data['special_offers']) ? count($data['special_offers']) : 0
        ]);

        $currentRecord = $this->getRecord();

        // Preserve policies with policy_details
        if (isset($data['policies']) && is_array($data['policies'])) {
            $existingPolicies = $currentRecord->policies ?? [];

            Log::info('Existing policies from database', [
                'existing_policies' => $existingPolicies,
                'existing_count' => count($existingPolicies)
            ]);

            // Start with form policies (from TableRepeater)
            $mergedPolicies = $data['policies'];

            // Add any existing policies that have policy_details (from modal actions)
            foreach ($existingPolicies as $existingPolicy) {
                if (isset($existingPolicy['policy_details'])) {
                    // This policy has details from modal action, preserve it
                    $mergedPolicies[] = $existingPolicy;
                    Log::info('Preserved policy with details', [
                        'preserved_policy' => $existingPolicy
                    ]);
                }
            }

            // Update the data with merged policies
            $data['policies'] = $mergedPolicies;
        }

        // Preserve special offers with offer_details
        if (isset($data['special_offers']) && is_array($data['special_offers'])) {
            $existingOffers = $currentRecord->special_offers ?? [];

            Log::info('Existing special offers from database', [
                'existing_offers' => $existingOffers,
                'existing_count' => count($existingOffers)
            ]);

            // Start with form special offers (from TableRepeater)
            $mergedOffers = $data['special_offers'];

            // Add any existing offers that have offer_details (from modal actions)
            foreach ($existingOffers as $existingOffer) {
                if (isset($existingOffer['offer_details'])) {
                    // This offer has details from modal action, preserve it
                    $mergedOffers[] = $existingOffer;
                    Log::info('Preserved offer with details', [
                        'preserved_offer' => $existingOffer
                    ]);
                }
            }

            // Update the data with merged offers
            $data['special_offers'] = $mergedOffers;
        }

        Log::info('Final data', [
            'final_policies' => $data['policies'] ?? 'NOT SET',
            'final_policies_count' => isset($data['policies']) ? count($data['policies']) : 0,
            'final_special_offers' => $data['special_offers'] ?? 'NOT SET',
            'final_special_offers_count' => isset($data['special_offers']) ? count($data['special_offers']) : 0
        ]);

        return $data;
    }
}
