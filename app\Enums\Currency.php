<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;


enum Currency: string implements HasLabel, HasIcon
{
    case EUR = 'EUR';
    case USD = 'USD';
    case TND = 'TND';
    case GBP = 'GBP';
    case JPY = 'JPY';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::EUR => 'Euro (€)',
            self::USD => 'US Dollar ($)',
            self::TND => 'Tunisian Dinar (TND)',
            self::GBP => 'British Pound (£)',
            self::JPY => 'Japanese Yen (¥)',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::EUR => '€',
            self::USD => '$',
            self::TND => 'DT',
            self::GBP => '£',
            self::JPY => '¥',
        };
    }
}