<?php

namespace App\Filament\Contracts\Resources\ActivityContractResource\Pages;

use App\Filament\Contracts\Resources\ActivityContractResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListActivityContracts extends ListRecords
{
    protected static string $resource = ActivityContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
