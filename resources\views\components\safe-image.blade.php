@props([
    'src' => null,
    'alt' => 'Image',
    'fallback' => null,
    'class' => '',
    'fallbackClass' => 'bg-gradient-to-br from-gray-400 to-gray-500 flex items-center justify-center',
    'fallbackIcon' => '🖼️'
])

@if($src && file_exists(storage_path('app/public/' . $src)))
    <img src="{{ asset('storage/' . $src) }}" alt="{{ $alt }}" {{ $attributes->merge(['class' => $class]) }}>
@elseif($fallback && file_exists(storage_path('app/public/' . $fallback)))
    <img src="{{ asset('storage/' . $fallback) }}" alt="{{ $alt }}" {{ $attributes->merge(['class' => $class]) }}>
@else
    <div {{ $attributes->merge(['class' => $fallbackClass]) }}>
        <span class="text-white text-2xl">{{ $fallbackIcon }}</span>
    </div>
@endif
