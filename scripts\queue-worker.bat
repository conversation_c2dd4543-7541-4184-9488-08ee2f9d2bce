@echo off
REM Travel Platform Queue Worker Script
REM This script manages the Laravel queue worker on Windows

setlocal enabledelayedexpansion

REM Configuration
set PROJECT_PATH=%~dp0..
set PHP_PATH=php
set ARTISAN_PATH=%PROJECT_PATH%\artisan
set LOG_PATH=%PROJECT_PATH%\storage\logs\queue-worker.log
set PID_FILE=%PROJECT_PATH%\storage\queue-worker.pid

REM Change to project directory
cd /d "%PROJECT_PATH%"

REM Parse command line arguments
set ACTION=%1
if "%ACTION%"=="" set ACTION=help

if "%ACTION%"=="start" goto start
if "%ACTION%"=="stop" goto stop
if "%ACTION%"=="restart" goto restart
if "%ACTION%"=="status" goto status
if "%ACTION%"=="help" goto help

echo Invalid action: %ACTION%
goto help

:start
echo Starting Travel Platform Queue Worker...
echo Timestamp: %date% %time%

REM Check if already running
if exist "%PID_FILE%" (
    echo Queue worker may already be running. Check with 'status' command.
    goto end
)

REM Start the queue worker in background
echo Starting queue worker...
start /B "" "%PHP_PATH%" "%ARTISAN_PATH%" queue:work --sleep=3 --tries=3 --timeout=60 --verbose >> "%LOG_PATH%" 2>&1

REM Save process info (simplified for Windows)
echo %date% %time% > "%PID_FILE%"

echo Queue worker started successfully!
echo Log file: %LOG_PATH%
echo To stop: %~nx0 stop
goto end

:stop
echo Stopping Travel Platform Queue Worker...

REM Send restart signal to Laravel queue workers
"%PHP_PATH%" "%ARTISAN_PATH%" queue:restart

REM Remove PID file
if exist "%PID_FILE%" del "%PID_FILE%"

echo Queue worker stopped.
goto end

:restart
echo Restarting Travel Platform Queue Worker...
call :stop
timeout /t 3 /nobreak > nul
call :start
goto end

:status
echo Travel Platform Queue Worker Status:
echo =====================================

REM Check if PID file exists
if exist "%PID_FILE%" (
    echo Status: Running
    echo Started: 
    type "%PID_FILE%"
) else (
    echo Status: Stopped
)

echo.
echo Queue Statistics:
"%PHP_PATH%" "%ARTISAN_PATH%" queue:manage status

goto end

:help
echo Travel Platform Queue Worker Manager
echo ===================================
echo.
echo Usage: %~nx0 [action]
echo.
echo Actions:
echo   start    - Start the queue worker
echo   stop     - Stop the queue worker  
echo   restart  - Restart the queue worker
echo   status   - Show queue worker status
echo   help     - Show this help message
echo.
echo Examples:
echo   %~nx0 start
echo   %~nx0 status
echo   %~nx0 restart
echo.

:end
endlocal
