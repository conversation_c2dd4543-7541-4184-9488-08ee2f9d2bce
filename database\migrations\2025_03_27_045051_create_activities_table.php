<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->text('highlights')->nullable();
            
            // Hotspot selection (linked to destinations table if applicable)
            $table->unsignedBigInteger('hotspot_id')->nullable();
            
            
            // Image Upload
            $table->string('image')->nullable();
            
            // Additional Information
            $table->decimal('tax', 8, 2)->nullable();
            $table->text('details_tax')->nullable();
            
            // Activity Details
            $table->string('activity_type'); // Dropdown
            $table->string('activity_nature'); // Dropdown
            $table->string('difficulty_level'); // Dropdown
            
            // Toggles & Number Fields
            $table->boolean('recommend_equipments')->default(false);
            $table->boolean('requires_adult_for_booking')->default(false);
            
            // Age & Traveler Restrictions
            $table->integer('min_age')->nullable();
            $table->integer('max_age')->nullable();
            $table->integer('min_travelers_per_booking')->nullable();
            $table->integer('max_travelers_per_booking')->nullable();
            
            // Service Type (Private or Shared)
            $table->enum('type_of_service', ['Private', 'Shared']);
            
            // Participants
            $table->integer('min_participant')->nullable();
            $table->integer('max_participant')->nullable();
            
            // Meeting & Destination Details
            $table->boolean('pickup_and_meet_at_start_point')->default(false);
            $table->boolean('meeting_point')->default(false);
            $table->unsignedBigInteger('destination_id'); // Reference to destination table
            
            // Return Point Selection
            $table->enum('end_point_return_to', ['Pick up point', 'Meeting point', 'Other']);
            $table->string('public_address')->nullable();
            $table->string('map_address')->nullable();
            
            // Location Details
            $table->string('street')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zip')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activities');
    }
};
