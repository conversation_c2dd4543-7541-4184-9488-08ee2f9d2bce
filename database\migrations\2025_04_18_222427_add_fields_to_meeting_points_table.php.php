<?php

use App\Models\MeetingPoint;
use App\Models\TransferContract;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_point_transfer_contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(TransferContract::class);
            $table->foreignIdFor(MeetingPoint::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_point_transfer_contracts');
    }
};