@startuml Visitor Homepage and Activity Details Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor Visitor as V
participant "View" as View
participant "Controller" as C
participant "Model" as M

note over V, M : Visitor Homepage and Activity Details Flow - MVC Architecture

V -> View : Access Travel Platform homepage
View -> C : GET / (HomePage request)
C -> M : Get featured destinations
M -> M : Query database for\nfeatured destinations
M -> C : Return destinations data

C -> M : Get featured activities
M -> M : Query database for\nfeatured activities
M -> C : Return activities data

C -> C : Process images and prepare data
C -> View : Return homepage view with data
View -> V : Display homepage\n(destinations, activities, navigation, chatbot)

V -> View : Scroll and browse featured content
View -> V : Show destination cards\nand activity cards

V -> View : Click on activity card
View -> C : GET /activities/{slug}
C -> M : Find activity by slug
M -> M : Query database for activity

alt Activity found
    M -> C : Return activity object
    C -> M : Get related activities
    M -> M : Query for related activities\nby destination
    M -> C : Return related activities data
    
    C -> C : Process activity images and data
    C -> View : Return activity detail view
    View -> V : Display activity details\n(images, description, map, pricing)
    
    V -> View : View activity information
    View -> V : Show detailed information:
    note over View, V
        - Hero image and gallery
        - Title, location, duration
        - Description and highlights
        - Interactive map with location
        - Pricing information
        - Related activities
    end note
    
    V -> View : Click "Add to Favorites" (disabled)
    View -> V : Show tooltip\n"Login required to add favorites"
    
    V -> View : Click "Book Now" button
    View -> C : Redirect to login page
    C -> View : Return login form
    View -> V : Display login page with message\n"Please login to book activities"
    
    V -> View : Navigate back to activity\nor homepage
    View -> C : Handle navigation request
    C -> View : Return requested page
    View -> V : Display requested content
    
else Activity not found
    M -> C : Activity not found
    C -> View : Return 404 error view
    View -> V : Display "Activity not found"\nwith navigation options
end

note over V, M
    Complete visitor journey from homepage to activity details
    Login prompts for protected actions (favorites, booking)
end note

@enduml
