@startuml Add to Favorites Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor User as U
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over U, M : Add to Favorites Operation - MVC Architecture

U -> V : Browse products on homepage\nor product listing
V -> U : Display product cards with\nheart icons (♡)

U -> V : Click heart icon (♡) on\nproduct card
V -> C : POST /favorites (product_id, product_type)
C -> C : Check user authentication

alt User authenticated
    C -> M : Check if product already favorited
    M -> M : Query favorites table\n(user_id, favoritable_id, favoritable_type)
    
    alt Product not in favorites
        M -> C : Product not favorited
        C -> M : Create new favorite record
        M -> M : INSERT INTO favorites\n(user_id, favoritable_id, favoritable_type)
        
        alt Favorite created successfully
            M -> C : Return favorite object
            C -> C : Log favorite action
            C -> V : Return success response\n(favorite_id, status)
            V -> V : Update heart icon to filled (♥)\nwith pink color
            V -> V : Show success animation
            V -> U : Display "Added to favorites"\ntooltip
            
        else Database error
            M -> C : Return creation error
            C -> V : Return error response
            V -> U : Display "Unable to add to favorites.\nPlease try again"
        end
        
    else Product already favorited
        M -> C : Product already in favorites
        C -> V : Return already exists response
        V -> U : Display "Already in your favorites"\ntooltip
    end
    
else User not authenticated
    C -> V : Return authentication error
    V -> U : Display "Login required to add favorites"\ntooltip (heart remains disabled)
end

note over U, M : Favorite added with visual feedback and database persistence

@enduml
