<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\DB;

class QueueManager extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:manage 
                            {action : The action to perform (start|stop|restart|status|monitor)}
                            {--daemon : Run as daemon}
                            {--timeout=60 : Timeout for queue worker}
                            {--tries=3 : Number of attempts for failed jobs}
                            {--sleep=3 : Sleep time between jobs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage queue workers with automatic restart and monitoring';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'start':
                return $this->startQueue();
            case 'stop':
                return $this->stopQueue();
            case 'restart':
                return $this->restartQueue();
            case 'status':
                return $this->queueStatus();
            case 'monitor':
                return $this->monitorQueue();
            default:
                $this->error("Invalid action: {$action}");
                $this->info('Available actions: start, stop, restart, status, monitor');
                return 1;
        }
    }

    /**
     * Start the queue worker
     */
    private function startQueue()
    {
        $this->info('Starting queue worker...');

        $timeout = $this->option('timeout');
        $tries = $this->option('tries');
        $sleep = $this->option('sleep');
        $daemon = $this->option('daemon');

        $command = "php artisan queue:work --timeout={$timeout} --tries={$tries} --sleep={$sleep}";
        
        if ($daemon) {
            $command .= ' --daemon';
        }

        $this->info("Command: {$command}");
        
        if ($daemon) {
            // For daemon mode, we'll use a different approach
            $this->startDaemon($command);
        } else {
            // For interactive mode
            $this->info('Queue worker started. Press Ctrl+C to stop.');
            passthru($command);
        }

        return 0;
    }

    /**
     * Stop the queue worker
     */
    private function stopQueue()
    {
        $this->info('Stopping queue workers...');
        
        // Send restart signal to all queue workers
        $this->call('queue:restart');
        
        $this->info('Queue workers stopped.');
        return 0;
    }

    /**
     * Restart the queue worker
     */
    private function restartQueue()
    {
        $this->info('Restarting queue workers...');
        
        $this->stopQueue();
        sleep(2);
        $this->startQueue();
        
        return 0;
    }

    /**
     * Show queue status
     */
    private function queueStatus()
    {
        $this->info('Queue Status:');
        $this->line('');

        // Count pending jobs
        $pendingJobs = DB::table('jobs')->count();
        $this->info("Pending jobs: {$pendingJobs}");

        // Count failed jobs
        $failedJobs = DB::table('failed_jobs')->count();
        $this->info("Failed jobs: {$failedJobs}");

        // Show recent jobs
        if ($pendingJobs > 0) {
            $this->line('');
            $this->info('Recent pending jobs:');
            $recentJobs = DB::table('jobs')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['id', 'queue', 'payload', 'created_at']);

            foreach ($recentJobs as $job) {
                $payload = json_decode($job->payload, true);
                $jobName = $payload['displayName'] ?? 'Unknown';
                $this->line("- Job #{$job->id}: {$jobName} (Queue: {$job->queue})");
            }
        }

        return 0;
    }

    /**
     * Monitor queue continuously
     */
    private function monitorQueue()
    {
        $this->info('Starting queue monitor... Press Ctrl+C to stop.');
        $this->line('');

        while (true) {
            $this->displayQueueStats();
            sleep(5); // Update every 5 seconds
        }
    }

    /**
     * Display queue statistics
     */
    private function displayQueueStats()
    {
        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();
        
        $timestamp = now()->format('Y-m-d H:i:s');
        
        $this->line("\r[{$timestamp}] Pending: {$pendingJobs} | Failed: {$failedJobs}", null, false);
    }

    /**
     * Start daemon process (Windows compatible)
     */
    private function startDaemon($command)
    {
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows approach
            $this->info('Starting queue worker as background process on Windows...');
            pclose(popen("start /B {$command}", "r"));
        } else {
            // Unix/Linux approach
            $this->info('Starting queue worker as daemon on Unix/Linux...');
            exec("{$command} > /dev/null 2>&1 &");
        }
        
        $this->info('Queue worker started in background.');
    }
}
