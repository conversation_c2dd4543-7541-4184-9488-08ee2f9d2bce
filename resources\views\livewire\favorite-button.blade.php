<button wire:click="toggleFavorite"
    class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 group/heart relative z-20"
    onclick="event.stopPropagation()">
    @if ($isFavorited)
        <svg class="w-4 h-4 text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
            fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
            </path>
        </svg>
    @else
        <svg class="w-4 h-4 text-gray-600 hover:text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
            fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
            </path>
        </svg>
    @endif
</button>
