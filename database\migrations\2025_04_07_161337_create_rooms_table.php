<?php

use App\Models\Hotel_Contract;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rooms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignIdFor(Hotel_Contract::class, 'hotel_contract_id');
            $table->enum('status', ['active', 'inactive']);



             // Allocation & occupancy
             $table->json('allocation')->nullable();
             $table->json('occupancy')->nullable();



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rooms');
    }
};