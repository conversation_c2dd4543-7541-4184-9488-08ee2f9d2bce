<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->string('email')->nullable();
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('phone')->nullable();
            $table->string('website')->nullable();
            $table->decimal('price', 10, 2)->nullable();
            $table->integer('roomcount')->nullable();
            $table->boolean('availability')->default(true);
            $table->text('amenities')->nullable();
            $table->time('from_check_in_time')->nullable();
            $table->time('to_check_in_time')->nullable();
            $table->time('from_check_out_time')->nullable();
            $table->time('to_check_out_time')->nullable();
        });
    }
    
    public function down()
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn([
                'email', 'address', 'city', 'country', 'phone', 
                'price', 'rooms_count', 'availability', 'amenities',
                'check_in_time', 'check_out_time'
            ]);
        });
    }
};