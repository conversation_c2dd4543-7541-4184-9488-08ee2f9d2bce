# Travel Platform Production Deployment Script
# PowerShell script for production deployment with queue management

param(
    [switch]$SkipMigrations,
    [switch]$SkipOptimization,
    [switch]$RestartQueue = $true
)

$ProjectPath = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectPath

Write-Host "Travel Platform Production Deployment" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Put application in maintenance mode
Write-Host "1. Enabling maintenance mode..." -ForegroundColor Yellow
php artisan down --retry=60

try {
    # Step 2: Update dependencies
    Write-Host "2. Installing/updating dependencies..." -ForegroundColor Yellow
    composer install --no-dev --optimize-autoloader
    npm ci --production

    # Step 3: Run migrations
    if (-not $SkipMigrations) {
        Write-Host "3. Running database migrations..." -ForegroundColor Yellow
        php artisan migrate --force
    } else {
        Write-Host "3. Skipping database migrations..." -ForegroundColor Gray
    }

    # Step 4: Clear and optimize caches
    if (-not $SkipOptimization) {
        Write-Host "4. Optimizing application..." -ForegroundColor Yellow
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache
        php artisan event:cache
        php artisan optimize
    } else {
        Write-Host "4. Skipping optimization..." -ForegroundColor Gray
    }

    # Step 5: Build assets
    Write-Host "5. Building production assets..." -ForegroundColor Yellow
    npm run build

    # Step 6: Restart queue workers
    if ($RestartQueue) {
        Write-Host "6. Restarting queue workers..." -ForegroundColor Yellow
        php artisan queue:restart
        
        # Start queue worker as service
        Write-Host "   Starting queue service..." -ForegroundColor Yellow
        & "$PSScriptRoot\queue-service.ps1" -Action start -Daemon
    } else {
        Write-Host "6. Skipping queue restart..." -ForegroundColor Gray
    }

    # Step 7: Run health checks
    Write-Host "7. Running health checks..." -ForegroundColor Yellow
    php artisan queue:health-check

    # Step 8: Bring application back online
    Write-Host "8. Disabling maintenance mode..." -ForegroundColor Yellow
    php artisan up

    Write-Host ""
    Write-Host "Deployment completed successfully!" -ForegroundColor Green
    Write-Host "Application is now online and queue workers are running." -ForegroundColor Green

} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    
    # Ensure application comes back online even if deployment fails
    Write-Host "Bringing application back online..." -ForegroundColor Yellow
    php artisan up
    
    exit 1
}

Write-Host ""
Write-Host "Post-deployment checklist:" -ForegroundColor Cyan
Write-Host "- Check application logs: storage/logs/laravel.log"
Write-Host "- Monitor queue status: php artisan queue:manage status"
Write-Host "- Verify email notifications are working"
Write-Host "- Test critical application features"
