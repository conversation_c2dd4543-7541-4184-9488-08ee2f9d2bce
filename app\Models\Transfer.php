<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\HasLocation;

class Transfer extends Model
{
    use HasFactory;
    //use HasLocation;

    protected $fillable = [
        'name',
        'description',
        'main_image',
        'transfer_type',
        'vehicle_type',
        'min_capacity',
        'max_capacity',
        'suitcases',
        'small_bag',
        'tax',
        'details_tax',
        'suppliers',
    ];
    
    public function transferContracts()
    {
        return $this->belongsToMany(TransferContract::class);
    }
    
}