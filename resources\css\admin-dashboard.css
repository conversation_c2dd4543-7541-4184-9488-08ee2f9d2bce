/* Beautiful Admin Dashboard Styles */

/* Widget Cards Enhancement */
.fi-wi-widget {
    @apply shadow-lg border-0 bg-gradient-to-br from-white to-gray-50;
    border-radius: 12px !important;
    transition: all 0.3s ease;
}

.fi-wi-widget:hover {
    @apply shadow-xl;
    transform: translateY(-2px);
}

/* Chart Container Styling */
.fi-wi-chart {
    @apply rounded-xl overflow-hidden;
}

/* Stats Cards Enhancement */
.fi-wi-stats-overview-stat {
    @apply bg-gradient-to-br from-white to-gray-50 border-0 shadow-md;
    border-radius: 12px !important;
    transition: all 0.3s ease;
}

.fi-wi-stats-overview-stat:hover {
    @apply shadow-lg;
    transform: translateY(-1px);
}

/* Beautiful Gradients for Different Widget Types */
.revenue-widget {
    @apply bg-gradient-to-br from-pink-50 to-rose-50 border-pink-200;
}

.booking-widget {
    @apply bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200;
}

.user-widget {
    @apply bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200;
}

.map-widget {
    @apply bg-gradient-to-br from-green-50 to-emerald-50 border-green-200;
}

.chatbot-widget {
    @apply bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200;
}

/* Dashboard Grid Enhancement */
.fi-da-page {
    @apply bg-gradient-to-br from-gray-50 to-white;
}

/* Widget Headers */
.fi-wi-header {
    @apply border-b border-gray-100;
}

.fi-wi-header-heading {
    @apply text-gray-800 font-semibold;
}

/* Chart Canvas Styling */
canvas {
    border-radius: 8px !important;
}

/* Filter Dropdowns */
.fi-select-input {
    @apply border-gray-200 rounded-lg shadow-sm;
}

/* Loading States */
.fi-wi-loading {
    @apply bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

/* Responsive Grid Improvements */
@media (min-width: 768px) {
    .fi-da-widgets {
        gap: 1.5rem !important;
    }
}

@media (min-width: 1024px) {
    .fi-da-widgets {
        gap: 2rem !important;
    }
}

/* Beautiful Scrollbars */
.fi-wi-widget::-webkit-scrollbar {
    width: 6px;
}

.fi-wi-widget::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
}

.fi-wi-widget::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
}

.fi-wi-widget::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
}

/* Map Widget Specific Styling */
.leaflet-container {
    border-radius: 8px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Chart Legend Styling */
.chartjs-legend {
    @apply text-sm text-gray-600;
}

/* Widget Action Buttons */
.fi-wi-header-actions button {
    @apply bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300;
    border-radius: 8px !important;
    transition: all 0.2s ease;
}

/* Stats Value Styling */
.fi-wi-stats-overview-stat-value {
    @apply text-2xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent;
}

/* Beautiful Focus States */
.fi-select-input:focus,
.fi-input:focus {
    @apply ring-2 ring-pink-500 ring-opacity-50 border-pink-500;
}

/* Dashboard Header */
.fi-header {
    @apply bg-white shadow-sm border-b border-gray-200;
}

/* Widget Content Padding */
.fi-wi-content {
    @apply p-6;
}

/* Table Styling in Widgets */
.fi-ta-table {
    @apply rounded-lg overflow-hidden;
}

.fi-ta-row:hover {
    @apply bg-gray-50;
}

/* Success/Error States */
.fi-color-success {
    @apply text-green-600;
}

.fi-color-danger {
    @apply text-red-600;
}

.fi-color-warning {
    @apply text-amber-600;
}

/* Animation for New Data */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fi-wi-widget[data-updated="true"] {
    animation: fadeInUp 0.5s ease-out;
}
