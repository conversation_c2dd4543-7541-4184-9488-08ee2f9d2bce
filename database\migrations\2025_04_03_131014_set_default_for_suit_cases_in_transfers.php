<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::table('transfers', function (Blueprint $table) {
        $table->integer('suit_cases')->default(0)->change();  // Set default to 0 or any appropriate value
    });
}

public function down()
{
    Schema::table('transfers', function (Blueprint $table) {
        $table->integer('suit_cases')->nullable()->change();  // Or change back to nullable if needed
    });
}

};
