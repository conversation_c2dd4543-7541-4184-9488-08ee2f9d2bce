<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum RoomStatus: string implements HasLabel, HasColor, HasIcon
{
    case active = 'active';
    case inactive = 'inactive';

    public function getLabel(): string
    {
        return match ($this) {
            self::active => 'active',
            self::inactive => 'inactive',
          
        };
    }
    public function getColor(): string|array|null
    {
        return match ($this) {

            self::active => 'success',
            self::inactive => 'danger',
            
        };
    }
    public function getIcon(): string
    {
        return match ($this) {

            self::active => 'heroicon-m-check',
            self::inactive => 'heroicon-m-x-mark',
            
        };
    }
}