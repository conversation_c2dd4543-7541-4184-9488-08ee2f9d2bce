<?php

use App\Livewire\Forms\LoginForm;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('layouts.guest')] class extends Component
{
    public LoginForm $form;

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->form->authenticate();

        Session::regenerate();

        $this->redirectIntended(default: '/', navigate: true);
    }
}; ?>

<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">
        <!-- Header -->
        <div class="flex flex-col items-center space-y-3">
            <img src="{{ asset('storage/avatar.jpg') }}" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Welcome back!</h2>
            <p class="text-center text-gray-500">Stay signed in with your account</p>
        </div>

        <!-- Livewire Form -->
        <form wire:submit="login">
            @csrf

            <!-- Email -->
            <div class="mb-4">
                <input wire:model="form.email" 
                       type="email" 
                       placeholder="Email" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                       required autofocus>
                @error('form.email') 
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Password -->
            <div class="relative mb-4">
                <input wire:model="form.password" 
                       id="password"
                       type="password" 
                       placeholder="Password" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                       required>
                <button type="button" 
                        class="absolute inset-y-0 right-3 flex items-center"
                        onclick="togglePassword()">
                    <i class="far fa-eye-slash text-gray-400"></i>
                </button>
                @error('form.password') 
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            <div class="mb-4 text-right">
                <a href="{{ route('password.request') }}" class="text-sm text-gray-600 hover:underline">
                    Forgot password?
                </a>
            </div>
            <!-- Remember Me -->
            <label class="flex items-center mb-4">
                <input wire:model="form.remember" 
                       type="checkbox" 
                       class="mr-2 rounded border-gray-300">
                <span class="text-sm">Remember me</span>
                
            </label>

            <!-- Submit Button -->
            <button type="submit" 
                    class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md hover:bg-gray-800">
                Log in
            </button>
        </form>

        <!-- Divider -->
        <div class="text-center text-gray-400">or</div>

        <!-- Google Login -->
        <a href="{{ route('login.google') }}" 
           class="flex items-center justify-center w-full px-4 py-2 space-x-2 border rounded-md hover:bg-gray-50">
            <img src="https://www.google.com/favicon.ico" class="w-5 h-5" alt="Google">
            <span>Continue with Google</span>
        </a>

        <!-- Registration Link -->
        <p class="text-sm text-center">
            Don't have an account?
            <a href="{{ route('register') }}" class="font-semibold text-blue-600 hover:underline">Sign Up</a>
        </p>
    </div>
</main>

@push('scripts')
<script>
    function togglePassword() {
        const input = document.getElementById('password');
        const icon = document.querySelector('.fa-eye-slash');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        } else {
            input.type = 'password';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        }
    }
</script>
@endpush
