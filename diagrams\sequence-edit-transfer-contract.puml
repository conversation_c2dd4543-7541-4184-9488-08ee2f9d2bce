@startuml Edit Transfer Contract Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor <PERSON><PERSON> as A
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over A, M : Edit Transfer Contract Operation - MVC Architecture

A -> V : Navigate to admin panel
V -> C : GET /admin/transfer-contracts
C -> M : Get all transfer contracts
M -> M : Query database for contracts
M -> C : Return contracts data
C -> V : Return contracts list view
V -> A : Display transfer contracts list

A -> V : Click "Edit" on specific contract
V -> C : GET /admin/transfer-contracts/{id}/edit
C -> M : Find transfer contract by ID
M -> M : Query database for contract

alt Contract found
    M -> C : Return contract object\nwith relationships
    C -> V : Return edit form\nwith pre-filled data
    V -> A : Display contract edit form\n(dates, pricing, markets, policies)
    
    A -> V : Modify contract fields\n(pricing, dates, markets, etc.)
    V -> C : PUT /admin/transfer-contracts/{id}\n(updated data)
    C -> C : Validate updated data
    
    alt Valid data
        C -> M : Check for date conflicts
        M -> M : Query for overlapping contracts
        
        alt No conflicts
            M -> C : Validation passed
            C -> M : Update transfer contract
            M -> M : Save changes to database
            
            alt Update successful
                M -> C : Return updated contract
                C -> C : Log contract modification
                C -> V : Redirect with success message
                V -> A : Display "Contract updated successfully"
            else Database error
                M -> C : Return update error
                C -> V : Return error view
                V -> A : Display "Failed to update contract"
            end
            
        else Date conflicts exist
            M -> C : Return conflict information
            C -> V : Return form with conflict error
            V -> A : Display "Date overlap with existing contract"
        end
        
    else Invalid data
        C -> V : Return form with validation errors
        V -> A : Display validation error messages
    end
    
else Contract not found
    M -> C : Contract not found
    C -> V : Return 404 error view
    V -> A : Display "Contract not found"
end

note over A, M : Contract modification with conflict checking and audit trail

@enduml
