<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\Booking;
use App\Mail\BookingConfirmation;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent for the booking
     */
    public function createPaymentIntent($amount, $currency = 'usd', $metadata = [])
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount * 100, // Stripe expects amount in cents
                'currency' => $currency,
                'metadata' => $metadata,
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent Creation Failed: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Retrieve a payment intent
     */
    public function retrievePaymentIntent($paymentIntentId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'status' => $paymentIntent->status,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent Retrieval Failed: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Confirm a payment intent
     */
    public function confirmPaymentIntent($paymentIntentId, $paymentMethodId)
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);

            $confirmedPaymentIntent = $paymentIntent->confirm([
                'payment_method' => $paymentMethodId,
            ]);

            return [
                'success' => true,
                'payment_intent' => $confirmedPaymentIntent,
                'status' => $confirmedPaymentIntent->status,
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent Confirmation Failed: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle Stripe webhook events
     */
    public function handleWebhook($payload, $signature)
    {
        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                config('services.stripe.webhook_secret')
            );

            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentSucceeded($event->data->object);
                    break;
                case 'payment_intent.payment_failed':
                    $this->handlePaymentFailed($event->data->object);
                    break;
                default:
                    Log::info('Unhandled Stripe webhook event: ' . $event->type);
            }

            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Stripe Webhook Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle successful payment
     */
    private function handlePaymentSucceeded($paymentIntent)
    {
        Log::info('Payment succeeded for PaymentIntent: ' . $paymentIntent->id);

        // Update booking status to confirmed
        if (isset($paymentIntent->metadata['booking_id'])) {
            $bookingId = $paymentIntent->metadata['booking_id'];

            // Update booking status in database
            $booking = Booking::where('id', $bookingId)->first();
            if ($booking) {
                $booking->update([
                    'status' => 'confirmed',
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => 'paid'
                ]);

                // Send confirmation email
                try {
                    Mail::to($booking->user->email)->send(new BookingConfirmation($booking));
                    Log::info('Booking confirmation email sent via webhook to: ' . $booking->user->email);
                } catch (\Exception $e) {
                    Log::error('Failed to send booking confirmation email via webhook: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * Handle failed payment
     */
    private function handlePaymentFailed($paymentIntent)
    {
        Log::error('Payment failed for PaymentIntent: ' . $paymentIntent->id);

        // Update booking status to failed
        if (isset($paymentIntent->metadata['booking_id'])) {
            $bookingId = $paymentIntent->metadata['booking_id'];

            // Update booking status in database
            Booking::where('id', $bookingId)
                ->update([
                    'status' => 'payment_failed',
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_status' => 'failed'
                ]);
        }
    }

    /**
     * Calculate application fee (if using Stripe Connect)
     */
    public function calculateApplicationFee($amount, $feePercentage = 2.9)
    {
        return round($amount * ($feePercentage / 100), 2);
    }

    /**
     * Format amount for Stripe (convert to cents)
     */
    public function formatAmountForStripe($amount)
    {
        return intval($amount * 100);
    }

    /**
     * Format amount from Stripe (convert from cents)
     */
    public function formatAmountFromStripe($amount)
    {
        return $amount / 100;
    }
}
