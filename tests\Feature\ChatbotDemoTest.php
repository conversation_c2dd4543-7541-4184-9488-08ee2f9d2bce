<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\ChatbotService;
use App\Services\IntentDetectionService;
use App\Services\DatabaseQueryService;

class ChatbotDemoTest extends TestCase
{
    public function test_hotel_search_demo()
    {
        $chatbotService = app(ChatbotService::class);

        // Test 1: Recherche d'hôtel simple
        $response = $chatbotService->processMessage(
            "Je cherche un hôtel à Paris",
            'demo_session_1',
            []
        );

        $this->assertNotEmpty($response);
        echo "\n🏨 Test hôtel Paris:\n";
        echo "Question: Je cherche un hôtel à Paris\n";
        echo "Réponse: " . substr($response, 0, 200) . "...\n";

        // Test 2: Recherche avec budget
        $response = $chatbotService->processMessage(
            "Montre-moi les hôtels à moins de 100€",
            'demo_session_2',
            []
        );

        echo "\n💰 Test hôtel avec budget:\n";
        echo "Question: Montre-moi les hôtels à moins de 100€\n";
        echo "Réponse: " . substr($response, 0, 200) . "...\n";
    }

    public function test_activity_search_demo()
    {
        $chatbotService = app(ChatbotService::class);

        // Test activité
        $response = $chatbotService->processMessage(
            "Quelles activités puis-je faire à Paris ?",
            'demo_session_3',
            []
        );

        echo "\n🎯 Test activités Paris:\n";
        echo "Question: Quelles activités puis-je faire à Paris ?\n";
        echo "Réponse: " . substr($response, 0, 200) . "...\n";
    }

    public function test_transfer_search_demo()
    {
        $chatbotService = app(ChatbotService::class);

        // Test transfert
        $response = $chatbotService->processMessage(
            "Je cherche un transfert en voiture",
            'demo_session_4',
            []
        );

        echo "\n🚗 Test transfert:\n";
        echo "Question: Je cherche un transfert en voiture\n";
        echo "Réponse: " . substr($response, 0, 200) . "...\n";
    }

    public function test_context_management_demo()
    {
        $chatbotService = app(ChatbotService::class);

        // Conversation avec contexte
        $sessionId = 'demo_context_session';

        // Premier message
        $response1 = $chatbotService->processMessage(
            "Je veux aller à Paris",
            $sessionId,
            []
        );

        echo "\n🔄 Test gestion du contexte:\n";
        echo "Message 1: Je veux aller à Paris\n";
        echo "Réponse 1: " . substr($response1, 0, 150) . "...\n";

        // Deuxième message utilisant le contexte
        $response2 = $chatbotService->processMessage(
            "Montre-moi les hôtels",
            $sessionId,
            [
                ['sender' => 'user', 'text' => 'Je veux aller à Paris'],
                ['sender' => 'bot', 'text' => $response1]
            ]
        );

        echo "Message 2: Montre-moi les hôtels\n";
        echo "Réponse 2: " . substr($response2, 0, 150) . "...\n";
    }

    public function test_intent_detection_accuracy()
    {
        $intentService = app(IntentDetectionService::class);

        $testCases = [
            "Je cherche un hôtel" => "voir_hotel",
            "Quelles activités ?" => "voir_activité",
            "Je veux un transfert" => "voir_transfert",
            "Bonjour" => "autre"
        ];

        echo "\n🎯 Test précision détection d'intentions:\n";

        foreach ($testCases as $message => $expectedIntent) {
            $result = $intentService->detectIntentAndEntities($message);
            $detected = $result['intent'];
            $confidence = $result['confidence'];

            $status = $detected === $expectedIntent ? "✅" : "❌";
            echo "{$status} '{$message}' → {$detected} (confiance: {$confidence})\n";
        }
    }

    public function test_database_queries()
    {
        $dbService = app(DatabaseQueryService::class);

        echo "\n🗄️ Test requêtes base de données:\n";

        // Test hôtels
        $hotels = $dbService->queryByIntent('voir_hotel', ['location' => 'Paris']);
        echo "Hôtels trouvés à Paris: " . count($hotels) . "\n";

        // Test activités
        $activities = $dbService->queryByIntent('voir_activité', ['location' => 'Paris']);
        echo "Activités trouvées à Paris: " . count($activities) . "\n";

        // Test transferts
        $transfers = $dbService->queryByIntent('voir_transfert', ['vehicule_type' => 'voiture']);
        echo "Transferts en voiture: " . count($transfers) . "\n";
    }

    public function test_spelling_correction()
    {
        $intentService = app(IntentDetectionService::class);

        echo "\n✏️ Test correction orthographique:\n";

        $testCases = [
            "Je cherche un otel" => "hôtel",
            "Quelles activiter ?" => "activité",
            "Je veux un transfere" => "transfert"
        ];

        foreach ($testCases as $input => $expected) {
            $result = $intentService->detectIntentAndEntities($input);
            echo "'{$input}' → Intention détectée: {$result['intent']}\n";
        }
    }
}
