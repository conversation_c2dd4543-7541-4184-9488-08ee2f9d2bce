<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            🗺️ Destinations Map
        </x-slot>

        <x-slot name="headerEnd">
            <x-filament::input.wrapper>
                <x-filament::input.select wire:model.live="filter">
                    <option value="revenue">Filter by Revenue</option>
                    <option value="bookings">Filter by Bookings</option>
                </x-filament::input.select>
            </x-filament::input.wrapper>
        </x-slot>

        <div class="space-y-4">
            <!-- Map Container with better styling -->
            <div class="relative">
                <div id="destinations-map" class="w-full h-96 rounded-lg border border-gray-200 bg-gray-50"></div>
                <div id="map-loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto mb-2"></div>
                        <p class="text-sm text-gray-600">Loading map...</p>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">High
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Medium
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Low
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Total Destinations: {{ count($destinations) }}
                </div>
            </div>

            <!-- Destinations Stats Table -->
            <div class="overflow-hidden bg-white border border-gray-200 rounded-lg">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900">Destination Performance</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Destination</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Revenue</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Bookings</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg. Value</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach (collect($destinations)->sortByDesc($filter === 'revenue' ? 'revenue' : 'bookings')->take(10) as $destination)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 w-8 h-8">
                                                @if ($destination['image'])
                                                    <img class="w-8 h-8 rounded-full object-cover"
                                                        src="{{ $destination['image'] }}"
                                                        alt="{{ $destination['name'] }}">
                                                @else
                                                    <div
                                                        class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                        <span
                                                            class="text-xs font-medium text-gray-600">{{ substr($destination['name'], 0, 2) }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $destination['name'] }}</div>
                                                <div class="text-sm text-gray-500">{{ $destination['country'] }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            €{{ number_format($destination['revenue'], 2) }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $destination['bookings'] }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            €{{ $destination['bookings'] > 0 ? number_format($destination['revenue'] / $destination['bookings'], 2) : '0.00' }}
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Include Leaflet CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <style>
        /* Fix Leaflet CSS issues in admin panels */
        #destinations-map {
            height: 384px !important;
            /* h-96 = 24rem = 384px */
            width: 100% !important;
            z-index: 1 !important;
        }

        .leaflet-container {
            height: 384px !important;
            width: 100% !important;
            background: #f9fafb !important;
        }

        .leaflet-tile {
            filter: none !important;
        }

        .leaflet-control-zoom {
            z-index: 1000 !important;
        }

        .leaflet-popup {
            z-index: 1001 !important;
        }

        #map-loading {
            z-index: 1002 !important;
        }
    </style>

    <script>
        let destinationsMap = null;
        let mapInitialized = false;

        function hideMapLoading() {
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }
        }

        function showMapLoading() {
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'flex';
            }
        }

        function initializeDestinationsMap() {
            console.log('Initializing destinations map...');

            // Wait for Leaflet to be fully loaded
            if (typeof L === 'undefined') {
                console.log('Leaflet not loaded yet, retrying...');
                setTimeout(initializeDestinationsMap, 500);
                return;
            }

            const mapContainer = document.getElementById('destinations-map');
            if (!mapContainer) {
                console.error('Map container not found');
                return;
            }

            // Remove existing map if it exists
            if (destinationsMap) {
                destinationsMap.remove();
                destinationsMap = null;
            }

            try {
                console.log('Creating map instance...');

                // Initialize map
                destinationsMap = L.map('destinations-map').setView([50.0, 10.0], 4);

                // Add OpenStreetMap tiles
                const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                });

                tileLayer.on('load', function() {
                    console.log('Tiles loaded successfully');
                    hideMapLoading();
                    mapInitialized = true;
                });

                tileLayer.on('tileerror', function(e) {
                    console.error('Tile loading error:', e);
                });

                tileLayer.addTo(destinationsMap);

                // Load destination markers
                loadDestinationMarkers();

            } catch (error) {
                console.error('Error creating map:', error);
                hideMapLoading();
                mapContainer.innerHTML =
                    '<div class="p-4 text-center text-red-600">Error loading map. Please refresh the page.</div>';
            }
        }

        function loadDestinationMarkers() {
            if (!destinationsMap) return;

            try {
                console.log('Loading destination markers...');

                // Get destinations data
                const destinations = @json($destinations);
                const filter = @json($filter);

                if (!destinations || destinations.length === 0) {
                    console.warn('No destinations data available');
                    hideMapLoading();
                    return;
                }

                // Calculate min/max values for color scaling
                const values = destinations.map(d => filter === 'revenue' ? d.revenue : d.bookings);
                const maxValue = Math.max(...values);
                const minValue = Math.min(...values);

                // Add markers for each destination
                const markers = [];
                destinations.forEach(destination => {
                    if (!destination.coordinates || !destination.coordinates.lat || !destination.coordinates.lng) {
                        console.warn('Invalid coordinates for destination:', destination.name);
                        return;
                    }

                    const value = filter === 'revenue' ? destination.revenue : destination.bookings;
                    const normalizedValue = maxValue > minValue ? (value - minValue) / (maxValue - minValue) : 0;

                    // Determine marker color based on value
                    let color;
                    if (normalizedValue > 0.66) {
                        color = '#10b981'; // Green for high
                    } else if (normalizedValue > 0.33) {
                        color = '#f59e0b'; // Yellow for medium
                    } else {
                        color = '#ef4444'; // Red for low
                    }

                    // Create custom marker
                    const marker = L.circleMarker([destination.coordinates.lat, destination.coordinates.lng], {
                        radius: Math.max(8, normalizedValue * 15 + 8),
                        fillColor: color,
                        color: '#ffffff',
                        weight: 2,
                        opacity: 1,
                        fillOpacity: 0.8
                    }).addTo(destinationsMap);

                    markers.push(marker);

                    // Create popup content
                    const popupContent = `
                        <div class="p-3">
                            <h3 class="font-bold text-base text-gray-900 mb-1">${destination.name}</h3>
                            <p class="text-sm text-gray-600 mb-2">${destination.country || 'Unknown'}</p>
                            <div class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Revenue:</span>
                                    <span class="font-medium">€${destination.revenue.toFixed(2)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Bookings:</span>
                                    <span class="font-medium">${destination.bookings}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Avg. Value:</span>
                                    <span class="font-medium">€${destination.bookings > 0 ? (destination.revenue / destination.bookings).toFixed(2) : '0.00'}</span>
                                </div>
                            </div>
                        </div>
                    `;

                    marker.bindPopup(popupContent);

                    // Add hover effects
                    marker.on('mouseover', function() {
                        this.setStyle({
                            weight: 3,
                            fillOpacity: 1
                        });
                    });

                    marker.on('mouseout', function() {
                        this.setStyle({
                            weight: 2,
                            fillOpacity: 0.8
                        });
                    });
                });

                console.log('Added', markers.length, 'markers to map');

                // Fit map to show all markers
                if (markers.length > 0) {
                    const group = new L.featureGroup(markers);
                    destinationsMap.fitBounds(group.getBounds().pad(0.1));
                }

                hideMapLoading();

            } catch (error) {
                console.error('Error loading destination markers:', error);
                hideMapLoading();
            }
        }

        // Initialize map when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeDestinationsMap, 1000);
        });

        // Re-initialize map when filter changes
        document.addEventListener('livewire:updated', function() {
            if (mapInitialized) {
                setTimeout(initializeDestinationsMap, 300);
            }
        });
    </script>
</x-filament-widgets::widget>
