<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            🗺️ Destinations Map
        </x-slot>

        <x-slot name="headerEnd">
            <x-filament::input.wrapper>
                <x-filament::input.select wire:model.live="filter">
                    <option value="revenue">Filter by Revenue</option>
                    <option value="bookings">Filter by Bookings</option>
                </x-filament::input.select>
            </x-filament::input.wrapper>
        </x-slot>

        <div class="space-y-4">
            <!-- Simplified Map Container -->
            <div class="bg-white rounded-lg border border-gray-200 p-4">
                <div class="mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Interactive Map</h3>
                    <p class="text-sm text-gray-600">Destinations with revenue and booking data</p>
                </div>

                <!-- Map will be inserted here by Alpine.js -->
                <div x-data="mapWidget()" x-init="initMap()" class="w-full">
                    <div x-ref="mapContainer" class="w-full h-96 rounded-lg border border-gray-200 bg-gray-100"></div>
                    <div x-show="loading"
                        class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto mb-2">
                            </div>
                            <p class="text-sm text-gray-600">Loading map...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">High
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Medium
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Low
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Total Destinations: {{ count($destinations) }}
                </div>
            </div>

            <!-- Destinations Stats Table -->
            <div class="overflow-hidden bg-white border border-gray-200 rounded-lg">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900">Destination Performance</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Destination</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Revenue</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Bookings</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg. Value</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach (collect($destinations)->sortByDesc($filter === 'revenue' ? 'revenue' : 'bookings')->take(10) as $destination)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 w-8 h-8">
                                                @if ($destination['image'])
                                                    <img class="w-8 h-8 rounded-full object-cover"
                                                        src="{{ asset('storage/' . $destination['image']) }}"
                                                        alt="{{ $destination['name'] }}">
                                                @else
                                                    <div
                                                        class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                        <span
                                                            class="text-xs font-medium text-gray-600">{{ substr($destination['name'], 0, 2) }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $destination['name'] }}</div>
                                                <div class="text-sm text-gray-500">{{ $destination['country'] }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            €{{ number_format($destination['revenue'], 2) }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $destination['bookings'] }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            €{{ $destination['bookings'] > 0 ? number_format($destination['revenue'] / $destination['bookings'], 2) : '0.00' }}
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Include Leaflet CSS and JS with integrity checks -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""
        onload="console.log('✅ Leaflet script loaded successfully')"
        onerror="console.error('❌ Failed to load Leaflet script')"></script>

    <style>
        /* Fix Leaflet CSS issues in admin panels */
        #destinations-map {
            height: 384px !important;
            /* h-96 = 24rem = 384px */
            width: 100% !important;
            z-index: 1 !important;
        }

        .leaflet-container {
            height: 384px !important;
            width: 100% !important;
            background: #f9fafb !important;
        }

        .leaflet-tile {
            filter: none !important;
        }

        .leaflet-control-zoom {
            z-index: 1000 !important;
        }

        .leaflet-popup {
            z-index: 1001 !important;
        }

        #map-loading {
            z-index: 1002 !important;
        }
    </style>

    <script>
        // Alpine.js Map Widget Component
        function mapWidget() {
            return {
                loading: true,
                map: null,

                initMap() {
                    console.log('🗺️ Alpine.js Map Widget Initializing...');

                    // Wait for Leaflet to be available
                    this.waitForLeaflet();
                },

                waitForLeaflet() {
                    if (typeof L !== 'undefined') {
                        console.log('✅ Leaflet available, creating map...');
                        this.createMap();
                    } else {
                        console.log('⏳ Waiting for Leaflet...');
                        setTimeout(() => this.waitForLeaflet(), 500);
                    }
                },

                createMap() {
                    try {
                        const container = this.$refs.mapContainer;

                        // Create map
                        this.map = L.map(container).setView([48.8566, 2.3522], 5);

                        // Add tiles
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '© OpenStreetMap contributors'
                        }).addTo(this.map);

                        // Add destination markers
                        this.addDestinationMarkers();

                        // Hide loading
                        this.loading = false;

                        console.log('✅ Map created successfully!');

                    } catch (error) {
                        console.error('❌ Map creation failed:', error);
                        this.loading = false;
                    }
                },

                addDestinationMarkers() {
                    const destinations = @json($destinations);
                    const filter = @json($filter);

                    console.log('📍 Adding markers for', destinations.length, 'destinations');

                    destinations.forEach((dest, index) => {
                        // Use coordinates or default position
                        let lat = dest.coordinates?.lat || (45 + (index % 10) * 2);
                        let lng = dest.coordinates?.lng || (0 + (index % 15) * 3);

                        const value = filter === 'revenue' ? dest.revenue : dest.bookings;
                        const color = value > 1000 ? '#10b981' : value > 100 ? '#f59e0b' : '#ef4444';

                        // Create marker
                        const marker = L.circleMarker([lat, lng], {
                            radius: Math.max(6, Math.min(20, value / 100)),
                            fillColor: color,
                            color: '#ffffff',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.8
                        }).addTo(this.map);

                        // Add popup
                        marker.bindPopup(`
                            <div class="p-2">
                                <h3 class="font-bold">${dest.name}</h3>
                                <p>Revenue: €${dest.revenue.toFixed(2)}</p>
                                <p>Bookings: ${dest.bookings}</p>
                            </div>
                        `);
                    });
                }
            }
        }

        // Livewire integration for filter changes
        document.addEventListener('livewire:updated', () => {
            console.log('🔄 Livewire updated - map will reinitialize automatically');
        });
    </script>
</x-filament-widgets::widget>
