<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            🗺️ Destinations Map
        </x-slot>

        <x-slot name="headerEnd">
            <x-filament::input.wrapper>
                <x-filament::input.select wire:model.live="filter">
                    <option value="revenue">Filter by Revenue</option>
                    <option value="bookings">Filter by Bookings</option>
                </x-filament::input.select>
            </x-filament::input.wrapper>
        </x-slot>

        <div class="space-y-4">
            <!-- Map Container with better styling -->
            <div class="relative">
                <div id="destinations-map" class="w-full h-96 rounded-lg border border-gray-200 bg-gray-50"></div>
                <div id="map-loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto mb-2"></div>
                        <p class="text-sm text-gray-600">Loading map...</p>
                        <button onclick="forceMapInit()"
                            class="mt-2 px-3 py-1 bg-pink-600 text-white text-xs rounded hover:bg-pink-700">
                            Force Load
                        </button>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">High
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Medium
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Low
                            {{ $filter === 'revenue' ? 'Revenue' : 'Bookings' }}</span>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Total Destinations: {{ count($destinations) }}
                </div>
            </div>

            <!-- Destinations Stats Table -->
            <div class="overflow-hidden bg-white border border-gray-200 rounded-lg">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900">Destination Performance</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Destination</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Revenue</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Bookings</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg. Value</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach (collect($destinations)->sortByDesc($filter === 'revenue' ? 'revenue' : 'bookings')->take(10) as $destination)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 w-8 h-8">
                                                @if ($destination['image'])
                                                    <img class="w-8 h-8 rounded-full object-cover"
                                                        src="{{ asset('storage/' . $destination['image']) }}"
                                                        alt="{{ $destination['name'] }}">
                                                @else
                                                    <div
                                                        class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                        <span
                                                            class="text-xs font-medium text-gray-600">{{ substr($destination['name'], 0, 2) }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $destination['name'] }}</div>
                                                <div class="text-sm text-gray-500">{{ $destination['country'] }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            €{{ number_format($destination['revenue'], 2) }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $destination['bookings'] }}</div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            €{{ $destination['bookings'] > 0 ? number_format($destination['revenue'] / $destination['bookings'], 2) : '0.00' }}
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </x-filament::section>

    <!-- Include Leaflet CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <style>
        /* Fix Leaflet CSS issues in admin panels */
        #destinations-map {
            height: 384px !important;
            /* h-96 = 24rem = 384px */
            width: 100% !important;
            z-index: 1 !important;
        }

        .leaflet-container {
            height: 384px !important;
            width: 100% !important;
            background: #f9fafb !important;
        }

        .leaflet-tile {
            filter: none !important;
        }

        .leaflet-control-zoom {
            z-index: 1000 !important;
        }

        .leaflet-popup {
            z-index: 1001 !important;
        }

        #map-loading {
            z-index: 1002 !important;
        }
    </style>

    <script>
        let destinationsMap = null;
        let mapInitialized = false;
        let initAttempts = 0;

        function hideMapLoading() {
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }
        }

        function showMapError(message) {
            const mapContainer = document.getElementById('destinations-map');
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) loadingEl.style.display = 'none';
            if (mapContainer) {
                mapContainer.innerHTML = `<div class="flex items-center justify-center h-full text-center p-4">
                    <div>
                        <div class="text-red-500 mb-2">⚠️</div>
                        <p class="text-sm text-gray-600">${message}</p>
                        <button onclick="forceMapInit()" class="mt-2 px-3 py-1 bg-pink-600 text-white text-xs rounded hover:bg-pink-700">
                            Try Again
                        </button>
                    </div>
                </div>`;
            }
        }

        function forceMapInit() {
            console.log('🔄 Force initializing map...');
            mapInitialized = false;
            initAttempts = 0;
            const mapContainer = document.getElementById('destinations-map');
            if (mapContainer) {
                mapContainer.innerHTML = '';
            }
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'flex';
            }
            setTimeout(initializeDestinationsMap, 100);
        }

        function initializeDestinationsMap() {
            initAttempts++;
            console.log(`🗺️ Map initialization attempt ${initAttempts}...`);

            // Prevent infinite loops
            if (initAttempts > 5) {
                console.error('❌ Too many initialization attempts');
                showMapError('Map failed to load after multiple attempts. Please refresh the page.');
                return;
            }

            // Check if already initialized
            if (mapInitialized) {
                console.log('✅ Map already initialized');
                return;
            }

            // Basic checks with detailed logging
            console.log('🔍 Checking Leaflet library...');
            if (typeof L === 'undefined') {
                console.error('❌ Leaflet not loaded, retrying in 1 second...');
                setTimeout(initializeDestinationsMap, 1000);
                return;
            }
            console.log('✅ Leaflet library loaded');

            console.log('🔍 Checking map container...');
            const mapContainer = document.getElementById('destinations-map');
            if (!mapContainer) {
                console.error('❌ Map container not found, retrying in 1 second...');
                setTimeout(initializeDestinationsMap, 1000);
                return;
            }
            console.log('✅ Map container found');

            // Check container dimensions
            const rect = mapContainer.getBoundingClientRect();
            console.log(`📏 Container dimensions: ${rect.width}x${rect.height}`);

            if (rect.width === 0 || rect.height === 0) {
                console.warn('⚠️ Container has no dimensions, retrying in 1 second...');
                setTimeout(initializeDestinationsMap, 1000);
                return;
            }

            try {
                console.log('🚀 Creating map instance...');

                // Remove existing map
                if (destinationsMap) {
                    console.log('🗑️ Removing existing map...');
                    destinationsMap.remove();
                    destinationsMap = null;
                }

                // Clear container
                mapContainer.innerHTML = '';

                // Create map with explicit options
                destinationsMap = L.map('destinations-map', {
                    center: [48.8566, 2.3522],
                    zoom: 5,
                    zoomControl: true,
                    attributionControl: true,
                    preferCanvas: false
                });

                console.log('✅ Map instance created successfully');

                // Add tiles with error handling
                const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18,
                    timeout: 10000
                });

                tileLayer.on('loading', () => console.log('🔄 Tiles loading...'));
                tileLayer.on('load', () => console.log('✅ Tiles loaded'));
                tileLayer.on('tileerror', (e) => console.warn('⚠️ Tile error:', e));

                tileLayer.addTo(destinationsMap);
                console.log('✅ Tile layer added');

                // Add markers
                console.log('📍 Adding markers...');
                addSimpleMarkers();

                // Force map to invalidate size
                setTimeout(() => {
                    if (destinationsMap) {
                        destinationsMap.invalidateSize();
                        console.log('🔄 Map size invalidated');
                    }
                }, 100);

                // Hide loading and mark as initialized
                hideMapLoading();
                mapInitialized = true;

                console.log('🎉 Map initialization complete!');

            } catch (error) {
                console.error('❌ Map creation failed:', error);
                console.error('Error details:', error.stack);
                showMapError(`Failed to create map: ${error.message}`);
            }
        }

        function addSimpleMarkers() {
            const destinations = @json($destinations);
            const filter = @json($filter);

            console.log('📍 Adding markers for', destinations.length, 'destinations');

            if (!destinations || destinations.length === 0) {
                console.warn('⚠️ No destinations data');
                return;
            }

            destinations.forEach((dest, index) => {
                // Use default coordinates if missing
                let lat = 48.8566; // Paris default
                let lng = 2.3522;

                if (dest.coordinates && dest.coordinates.lat && dest.coordinates.lng) {
                    lat = dest.coordinates.lat;
                    lng = dest.coordinates.lng;
                } else {
                    // Spread destinations around Europe if no coordinates
                    lat = 45 + (index % 10) * 2;
                    lng = 0 + (index % 15) * 3;
                }

                const value = filter === 'revenue' ? dest.revenue : dest.bookings;
                const color = value > 1000 ? '#10b981' : value > 100 ? '#f59e0b' : '#ef4444';

                const marker = L.circleMarker([lat, lng], {
                    radius: Math.max(6, Math.min(20, value / 100)),
                    fillColor: color,
                    color: '#ffffff',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(destinationsMap);

                marker.bindPopup(`
                    <div class="p-2">
                        <h3 class="font-bold">${dest.name}</h3>
                        <p>Revenue: €${dest.revenue.toFixed(2)}</p>
                        <p>Bookings: ${dest.bookings}</p>
                    </div>
                `);
            });

            console.log('✅ Markers added successfully');
        }

        // Aggressive initialization strategy
        console.log('🚀 Setting up map initialization...');

        function startMapInit() {
            console.log('⏰ Starting immediate map initialization...');

            // Try immediately
            initializeDestinationsMap();

            // Backup attempts
            setTimeout(() => {
                if (!mapInitialized) {
                    console.log('🔄 Backup attempt 1...');
                    initializeDestinationsMap();
                }
            }, 1000);

            setTimeout(() => {
                if (!mapInitialized) {
                    console.log('🔄 Backup attempt 2...');
                    initializeDestinationsMap();
                }
            }, 3000);
        }

        // Multiple initialization triggers
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startMapInit);
        } else {
            // DOM already ready, start immediately
            startMapInit();
        }

        // Window load backup
        window.addEventListener('load', () => {
            if (!mapInitialized) {
                console.log('🔄 Window load backup...');
                setTimeout(initializeDestinationsMap, 500);
            }
        });

        // Intersection Observer for when map becomes visible
        if (typeof IntersectionObserver !== 'undefined') {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !mapInitialized) {
                        console.log('👁️ Map container visible, initializing...');
                        setTimeout(initializeDestinationsMap, 100);
                    }
                });
            });

            // Start observing
            setTimeout(() => {
                const mapContainer = document.getElementById('destinations-map');
                if (mapContainer) {
                    observer.observe(mapContainer);
                    console.log('👁️ Started observing map container');
                }
            }, 100);
        }

        // Handle filter changes
        document.addEventListener('livewire:updated', () => {
            console.log('🔄 Livewire updated, reinitializing map...');
            mapInitialized = false;
            initAttempts = 0;
            setTimeout(initializeDestinationsMap, 300);
        });
    </script>
</x-filament-widgets::widget>
