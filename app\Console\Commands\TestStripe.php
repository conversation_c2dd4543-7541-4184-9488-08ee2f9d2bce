<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\StripeService;

class TestStripe extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:stripe';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Stripe integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Stripe integration...');

        try {
            $stripeService = new StripeService();

            // Test creating a payment intent
            $result = $stripeService->createPaymentIntent(
                50.00, // $50.00
                'usd',
                [
                    'test' => 'true',
                    'description' => 'Test payment intent from command'
                ]
            );

            if ($result['success']) {
                $this->info('✅ Stripe integration is working!');
                $this->line('Payment Intent ID: ' . $result['payment_intent_id']);
                $this->line('Client Secret: ' . substr($result['client_secret'], 0, 20) . '...');
                return 0;
            } else {
                $this->error('❌ Stripe integration failed');
                $this->error('Error: ' . $result['error']);
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            return 1;
        }
    }
}
