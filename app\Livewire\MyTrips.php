<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Booking;
use Illuminate\Support\Facades\Auth;

class MyTrips extends Component
{
    public function render()
    {
        $bookings = collect();

        if (Auth::check()) {
            $bookings = Booking::with(['activity', 'activity.destination'])
                ->where('user_id', Auth::id())
                ->orderBy('booking_date', 'asc')
                ->get();
        }

        // Separate bookings by status
        $confirmedBookings = $bookings->where('status', 'confirmed');
        $pendingBookings = $bookings->where('status', 'pending');
        $cancelledBookings = $bookings->where('status', 'cancelled');

        return view('livewire.my-trips', [
            'bookings' => $bookings,
            'confirmedBookings' => $confirmedBookings,
            'pendingBookings' => $pendingBookings,
            'cancelledBookings' => $cancelledBookings
        ]);
    }

    public function cancelBooking($bookingId)
    {
        $booking = Booking::where('id', $bookingId)
            ->where('user_id', Auth::id())
            ->first();

        if ($booking) {
            $booking->update(['status' => 'cancelled']);
            session()->flash('success', 'Booking cancelled successfully.');
        }
    }

    public function completePayment($bookingId)
    {
        $booking = Booking::where('id', $bookingId)
            ->where('user_id', Auth::id())
            ->where('status', 'pending')
            ->first();

        if ($booking) {
            // Redirect to payment page
            return redirect()->route('payment.page', ['bookingId' => $booking->id]);
        } else {
            session()->flash('error', 'Booking not found or already processed.');
        }
    }
}
