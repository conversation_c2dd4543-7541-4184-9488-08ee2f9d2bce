<?php

namespace App\Console\Commands;

use App\Mail\EventCreatedNotification;
use App\Models\Event;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEventEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:event-email 
                            {email : Email address to send test to}
                            {--event= : Specific event ID to use}
                            {--user= : Specific user ID to use}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test event notification email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        // Get event
        $eventId = $this->option('event');
        if ($eventId) {
            $event = Event::with('destination')->find($eventId);
            if (!$event) {
                $this->error("Event with ID {$eventId} not found.");
                return 1;
            }
        } else {
            $event = Event::with('destination')->latest()->first();
            if (!$event) {
                $this->error('No events found. Please create an event first.');
                return 1;
            }
        }

        // Get user
        $userId = $this->option('user');
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return 1;
            }
        } else {
            $user = User::first();
            if (!$user) {
                $this->error('No users found.');
                return 1;
            }
        }

        $this->info('Sending test event notification email...');
        $this->line('');
        $this->info("Event: {$event->name}");
        $this->info("Destination: {$event->destination->name}");
        $this->info("User: {$user->name}");
        $this->info("To: {$email}");
        $this->line('');

        try {
            // Create and send the email
            $mailable = new EventCreatedNotification($event, $user);
            Mail::to($email)->send($mailable);
            
            $this->info('✅ Test email sent successfully!');
            $this->line('');
            $this->info('Check your email inbox for the notification.');
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to send test email: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
