<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class QueueHeartbeat implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        // Set this job to run every 2 minutes
        $this->delay(now()->addMinutes(2));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Update heartbeat timestamp
        Cache::put('queue_worker_heartbeat', Carbon::now()->toISOString(), 600); // 10 minutes TTL
        
        // Log heartbeat
        \Log::info('Queue worker heartbeat: ' . Carbon::now()->toISOString());
        
        // Schedule next heartbeat
        self::dispatch();
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        \Log::error('Queue heartbeat failed: ' . $exception->getMessage());
        
        // Try to reschedule
        self::dispatch();
    }
}
