@startuml Travel Platform - Use Case Diagram (Manual Layout)
!theme plain

' Style exact comme l'image de référence
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
    FontSize 11
}
skinparam usecase {
    BackgroundColor #87CEEB
    BorderColor #4682B4
    FontColor #000000
    FontSize 9
}
skinparam arrow {
    Color #000000
}
skinparam note {
    BackgroundColor #FFFFFF
    BorderColor #FF0000
    FontColor #FF0000
    FontSize 10
}

' Acteurs
actor "Utilisateur\nInvité" as Guest
actor "Utilisateur\nEnregistré" as User  
actor "Administrateur" as Admin

' Layout vertical des acteurs
Guest -[hidden]down- User
User -[hidden]down- Admin

' Cas d'usage niveau 1 - Utilisateur Invité
usecase "Consulter page\nd'accueil" as HomePage
usecase "Explorer\ndestinations" as BrowseDest
usecase "Rechercher\nhôtels" as SearchHotels
usecase "Rechercher\nactivités" as SearchActivities
usecase "Rechercher\ntransferts" as SearchTransfers
usecase "Utiliser\nchatbot" as UseChatbot
usecase "Contacter\nsupport" as ContactSupport
usecase "S'inscrire" as Register
usecase "Se connecter" as Login

' Extensions d'authentification
usecase "Connexion\nGoogle OAuth" as GoogleAuth
usecase "Récupérer\nmot de passe" as ResetPassword

' Cas d'usage niveau 2 - Utilisateur Enregistré
usecase "Gérer\nprofil" as ManageProfile
usecase "Ajouter aux\nfavoris" as AddFavorites
usecase "Consulter\nfavoris" as ViewFavorites
usecase "Consulter mes\nvoyages" as ViewTrips

' Extensions du profil
usecase "Modifier\ninformations" as EditInfo
usecase "Changer\nmot de passe" as ChangePassword

' Extensions des favoris
usecase "Supprimer des\nfavoris" as RemoveFavorites
usecase "Organiser\nfavoris" as OrganizeFavorites

' Cas d'usage niveau 3 - Administrateur
usecase "Gérer\nhôtels" as ManageHotels
usecase "Gérer\nactivités" as ManageActivities
usecase "Gérer\ntransferts" as ManageTransfers
usecase "Gérer\ndestinations" as ManageDestinations
usecase "Gérer\nutilisateurs" as ManageUsers
usecase "Gérer\nsupport" as ManageSupport

' Extensions admin
usecase "Gérer\ncontrats" as ManageContracts
usecase "Gérer\npermissions" as ManagePermissions
usecase "Configurer\nchatbot" as ConfigureChatbot

' Relations principales - Utilisateur Invité
Guest --> HomePage
Guest --> BrowseDest
Guest --> SearchHotels
Guest --> SearchActivities
Guest --> SearchTransfers
Guest --> UseChatbot
Guest --> ContactSupport
Guest --> Register
Guest --> Login

' Héritage (généralisation)
Guest <|-- User

' Relations - Utilisateur Enregistré (hérite + spécifiques)
User --> ManageProfile
User --> AddFavorites
User --> ViewFavorites
User --> ViewTrips

' Relations - Administrateur
Admin --> ManageHotels
Admin --> ManageActivities
Admin --> ManageTransfers
Admin --> ManageDestinations
Admin --> ManageUsers
Admin --> ManageSupport

' Relations Include (lignes pointillées avec flèches)
Login ..> GoogleAuth : <<include>>
Login ..> ResetPassword : <<include>>
ManageProfile ..> EditInfo : <<include>>
ManageProfile ..> ChangePassword : <<include>>
ViewFavorites ..> RemoveFavorites : <<include>>
AddFavorites ..> Login : <<include>>

' Relations Extend (lignes pointillées avec flèches)
GoogleAuth ..> Login : <<extend>>
ResetPassword ..> Login : <<extend>>
EditInfo ..> ManageProfile : <<extend>>
ChangePassword ..> ManageProfile : <<extend>>
RemoveFavorites ..> ViewFavorites : <<extend>>
OrganizeFavorites ..> ViewFavorites : <<extend>>
ManageContracts ..> ManageHotels : <<extend>>
ManageContracts ..> ManageActivities : <<extend>>
ManageContracts ..> ManageTransfers : <<extend>>
ManagePermissions ..> ManageUsers : <<extend>>
ConfigureChatbot ..> ManageSupport : <<extend>>

' Notes explicatives en rouge
note top of Guest #FFE6E6 : Actor
note right of GoogleAuth #FFE6E6 : Extend
note right of EditInfo #FFE6E6 : Extension Point
note left of Guest #FFE6E6 : Association

@enduml
