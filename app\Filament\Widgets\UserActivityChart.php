<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\ChatSession;
use App\Models\Booking;
use App\Services\AnalyticsService;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class UserActivityChart extends ChartWidget
{
    protected static ?string $heading = '👥 User Activity & Engagement';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '350px';

    public ?string $filter = '30days';

    protected function getFilters(): ?array
    {
        return [
            '7days' => 'Last 7 days',
            '30days' => 'Last 30 days',
            '90days' => 'Last 3 months',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;
        
        $days = match ($activeFilter) {
            '7days' => 7,
            '30days' => 30,
            '90days' => 90,
            default => 30,
        };

        // Get trend data using custom analytics service
        $newUsersData = AnalyticsService::getTrendData(User::class, $days);
        $chatSessionsData = AnalyticsService::getTrendData(ChatSession::class, $days);
        $newBookingsData = AnalyticsService::getTrendData(Booking::class, $days);

        $labels = $newUsersData['labels'];

        return [
            'datasets' => [
                [
                    'label' => 'New Users',
                    'data' => $newUsersData['data'],
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Chat Sessions',
                    'data' => $chatSessionsData['data'],
                    'borderColor' => '#8b5cf6',
                    'backgroundColor' => 'rgba(139, 92, 246, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
                [
                    'label' => 'New Bookings',
                    'data' => $newBookingsData['data'],
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
            'elements' => [
                'point' => [
                    'radius' => 3,
                    'hoverRadius' => 5,
                ],
            ],
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }

    protected function getDescription(): ?string
    {
        $totalUsers = User::count();
        $activeUsers = User::whereHas('chatSessions', function($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        })->count();
        
        $engagementRate = $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 1) : 0;

        return "Total Users: {$totalUsers} | Active Users (30d): {$activeUsers} | Engagement Rate: {$engagementRate}%";
    }
}
