<?php

namespace App\Filament\Widgets;

use App\Models\Destination;
use App\Models\Booking;
use App\Models\Hotel;
use App\Models\Activity;
use Filament\Widgets\Widget;

class DestinationsMapWidget extends Widget
{
    protected static string $view = 'filament.widgets.destinations-map';
    protected static ?int $sort = 7;
    protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'revenue';

    protected function getViewData(): array
    {
        $destinations = $this->getDestinationsData();
        
        return [
            'destinations' => $destinations,
            'filter' => $this->filter,
            'filterOptions' => [
                'revenue' => 'Revenue',
                'bookings' => 'Total Bookings',
            ]
        ];
    }

    private function getDestinationsData(): array
    {
        $destinations = Destination::all();
        $destinationsData = [];

        foreach ($destinations as $destination) {
            // Get revenue for this destination
            $revenue = $this->getDestinationRevenue($destination);
            
            // Get total bookings for this destination
            $bookings = $this->getDestinationBookings($destination);

            // Get coordinates (you might need to add lat/lng fields to destinations table)
            $coordinates = $this->getDestinationCoordinates($destination);

            $destinationsData[] = [
                'id' => $destination->id,
                'name' => $destination->name,
                'country' => $destination->country,
                'revenue' => $revenue,
                'bookings' => $bookings,
                'coordinates' => $coordinates,
                'description' => $destination->description ?? '',
                'image' => $destination->image ?? null,
            ];
        }

        return $destinationsData;
    }

    private function getDestinationRevenue(Destination $destination): float
    {
        try {
            // Get hotel IDs for this destination
            $hotelIds = Hotel::where('destination_id', $destination->id)->pluck('id');

            // Get activity IDs for this destination
            $activityIds = Activity::where('destination_id', $destination->id)->pluck('id');

            // Get revenue from hotel bookings
            $hotelRevenue = Booking::where('bookable_type', Hotel::class)
                ->whereIn('bookable_id', $hotelIds)
                ->where('status', 'confirmed')
                ->sum('total_amount') ?? 0;

            // Get revenue from activity bookings
            $activityRevenue = Booking::where('bookable_type', Activity::class)
                ->whereIn('bookable_id', $activityIds)
                ->where('status', 'confirmed')
                ->sum('total_amount') ?? 0;

            return $hotelRevenue + $activityRevenue;
        } catch (\Exception) {
            return 0;
        }
    }

    private function getDestinationBookings(Destination $destination): int
    {
        try {
            // Get hotel IDs for this destination
            $hotelIds = Hotel::where('destination_id', $destination->id)->pluck('id');

            // Get activity IDs for this destination
            $activityIds = Activity::where('destination_id', $destination->id)->pluck('id');

            // Get bookings from hotels
            $hotelBookings = Booking::where('bookable_type', Hotel::class)
                ->whereIn('bookable_id', $hotelIds)
                ->where('status', 'confirmed')
                ->count();

            // Get bookings from activities
            $activityBookings = Booking::where('bookable_type', Activity::class)
                ->whereIn('bookable_id', $activityIds)
                ->where('status', 'confirmed')
                ->count();

            return $hotelBookings + $activityBookings;
        } catch (\Exception) {
            return 0;
        }
    }

    private function getDestinationCoordinates(Destination $destination): array
    {
        // Default coordinates for major cities (you should add lat/lng to destinations table)
        $defaultCoordinates = [
            'Paris' => ['lat' => 48.8566, 'lng' => 2.3522],
            'London' => ['lat' => 51.5074, 'lng' => -0.1278],
            'Rome' => ['lat' => 41.9028, 'lng' => 12.4964],
            'Barcelona' => ['lat' => 41.3851, 'lng' => 2.1734],
            'Amsterdam' => ['lat' => 52.3676, 'lng' => 4.9041],
            'Berlin' => ['lat' => 52.5200, 'lng' => 13.4050],
            'Madrid' => ['lat' => 40.4168, 'lng' => -3.7038],
            'Vienna' => ['lat' => 48.2082, 'lng' => 16.3738],
            'Prague' => ['lat' => 50.0755, 'lng' => 14.4378],
            'Lisbon' => ['lat' => 38.7223, 'lng' => -9.1393],
        ];

        // Check if destination has coordinates in database
        if (isset($destination->latitude) && isset($destination->longitude)) {
            return [
                'lat' => (float) $destination->latitude,
                'lng' => (float) $destination->longitude
            ];
        }

        // Try to match by name
        $destinationName = $destination->name;
        foreach ($defaultCoordinates as $city => $coords) {
            if (stripos($destinationName, $city) !== false) {
                return $coords;
            }
        }

        // Default to center of Europe
        return ['lat' => 50.0, 'lng' => 10.0];
    }

    public function getFilters(): array
    {
        return [
            'revenue' => 'Revenue',
            'bookings' => 'Total Bookings',
        ];
    }

    public function updatedFilter(): void
    {
        // This will trigger a re-render when filter changes
    }
}
