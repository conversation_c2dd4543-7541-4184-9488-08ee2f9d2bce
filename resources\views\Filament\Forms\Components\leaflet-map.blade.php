<head>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
</head>

<div wire:ignore x-data="{
    map: null,
    marker: null,
    initMap() {
        this.map = L.map($el).setView([48.8566, 2.3522], 10); // Paris par défaut

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map);

        this.marker = L.marker([48.8566, 2.3522], { draggable: true }).addTo(this.map);

        this.marker.on('dragend', (event) => {
            const position = event.target.getLatLng();
            @this.set('{{ $getStatePath() }}', JSON.stringify({ lat: position.lat, lng: position.lng }));
        });
    }
}" x-init="initMap()" style="height: 300px;"></div>
