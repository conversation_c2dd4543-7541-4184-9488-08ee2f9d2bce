<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px 20px;
        }
        .booking-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #ec4899;
        }
        .activity-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .activity-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 15px;
        }
        .activity-details h3 {
            margin: 0 0 5px 0;
            color: #1f2937;
            font-size: 20px;
        }
        .activity-details p {
            margin: 0;
            color: #6b7280;
        }
        .booking-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .detail-item {
            display: flex;
            align-items: center;
        }
        .detail-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #ec4899;
        }
        .detail-text {
            font-size: 14px;
            color: #4b5563;
        }
        .price-section {
            background: #ecfdf5;
            border: 1px solid #d1fae5;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
        }
        .price-amount {
            font-size: 24px;
            font-weight: bold;
            color: #059669;
            margin: 0;
        }
        .price-label {
            color: #6b7280;
            font-size: 14px;
            margin: 5px 0 0 0;
        }
        .status-badge {
            display: inline-block;
            background: #d1fae5;
            color: #065f46;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            margin: 5px 0;
            color: #6b7280;
            font-size: 14px;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .booking-details {
                grid-template-columns: 1fr;
            }
            .activity-info {
                flex-direction: column;
                text-align: center;
            }
            .activity-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎉 Booking Confirmed!</h1>
            <p>Your adventure awaits</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Dear {{ $booking->user->name }},</p>
            
            <p>Great news! Your booking has been confirmed and payment has been processed successfully. We're excited to have you join us for this amazing experience!</p>

            <!-- Booking Card -->
            <div class="booking-card">
                <!-- Activity Info -->
                <div class="activity-info">
                    <div class="activity-icon">🎯</div>
                    <div class="activity-details">
                        <h3>{{ $booking->activity->name }}</h3>
                        <p>{{ $booking->activity->destination?->name ?? 'Adventure Activity' }}</p>
                        <span class="status-badge">✅ Confirmed</span>
                    </div>
                </div>

                <!-- Booking Details -->
                <div class="booking-details">
                    <div class="detail-item">
                        <div class="detail-icon">📅</div>
                        <div class="detail-text">
                            <strong>Date:</strong><br>
                            {{ $booking->booking_date->format('l, F j, Y') }}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-icon">🕐</div>
                        <div class="detail-text">
                            <strong>Time:</strong><br>
                            {{ $booking->booking_time }}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-icon">👥</div>
                        <div class="detail-text">
                            <strong>Participants:</strong><br>
                            {{ $booking->adults }} adult{{ $booking->adults > 1 ? 's' : '' }}
                            @if($booking->children > 0)
                                + {{ $booking->children }} child{{ $booking->children > 1 ? 'ren' : '' }}
                            @endif
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-icon">🎫</div>
                        <div class="detail-text">
                            <strong>Booking ID:</strong><br>
                            #{{ str_pad($booking->id, 6, '0', STR_PAD_LEFT) }}
                        </div>
                    </div>
                </div>

                <!-- Price Section -->
                <div class="price-section">
                    <p class="price-amount">${{ number_format($booking->total_price, 2) }}</p>
                    <p class="price-label">Total Amount Paid</p>
                </div>
            </div>

            <!-- Important Information -->
            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #92400e;">📋 Important Information:</h4>
                <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                    <li>Please arrive 15 minutes before your scheduled time</li>
                    <li>Bring a valid ID for verification</li>
                    <li>Check weather conditions before your visit</li>
                    <li>Contact us if you need to make any changes</li>
                </ul>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ route('mytrips') }}" class="cta-button">View My Trips</a>
            </div>

            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
            
            <p>Thank you for choosing us for your adventure!</p>
            
            <p>Best regards,<br>
            <strong>Travel Platform Team</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Travel Platform</strong></p>
            <p>Your gateway to amazing adventures</p>
            <p style="font-size: 12px; margin-top: 15px;">
                This is an automated email. Please do not reply to this message.
            </p>
        </div>
    </div>
</body>
</html>
