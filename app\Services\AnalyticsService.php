<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class AnalyticsService
{
    /**
     * Get trend data for a model over a period
     */
    public static function getTrendData(string $model, int $days, string $aggregateColumn = null): array
    {
        $startDate = now()->subDays($days);
        $endDate = now();
        
        $period = CarbonPeriod::create($startDate, '1 day', $endDate);
        $dates = [];
        $data = [];
        
        // Create array of all dates in period
        foreach ($period as $date) {
            $dateKey = $date->format('Y-m-d');
            $dates[] = $dateKey;
            $data[$dateKey] = 0;
        }
        
        // Build query
        $query = $model::whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw('DATE(created_at) as date'));
            
        if ($aggregateColumn) {
            $query->addSelect(DB::raw("SUM({$aggregateColumn}) as aggregate"));
        } else {
            $query->addSelect(DB::raw('COUNT(*) as aggregate'));
        }
        
        $results = $query->groupBy('date')->get();
        
        // Fill in the data
        foreach ($results as $result) {
            $data[$result->date] = $result->aggregate;
        }
        
        return [
            'labels' => collect($dates)->map(fn($date) => Carbon::parse($date)->format('M j'))->toArray(),
            'data' => array_values($data),
        ];
    }
    
    /**
     * Get growth percentage between two periods
     */
    public static function getGrowthRate(string $model, string $period = 'month', string $aggregateColumn = null): float
    {
        $currentPeriodStart = match($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
        
        $previousPeriodStart = match($period) {
            'week' => now()->subWeek()->startOfWeek(),
            'month' => now()->subMonth()->startOfMonth(),
            'year' => now()->subYear()->startOfYear(),
            default => now()->subMonth()->startOfMonth(),
        };
        
        $previousPeriodEnd = match($period) {
            'week' => now()->subWeek()->endOfWeek(),
            'month' => now()->subMonth()->endOfMonth(),
            'year' => now()->subYear()->endOfYear(),
            default => now()->subMonth()->endOfMonth(),
        };
        
        // Current period value
        $currentQuery = $model::where('created_at', '>=', $currentPeriodStart);
        $currentValue = $aggregateColumn ? 
            $currentQuery->sum($aggregateColumn) : 
            $currentQuery->count();
            
        // Previous period value
        $previousQuery = $model::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd]);
        $previousValue = $aggregateColumn ? 
            $previousQuery->sum($aggregateColumn) : 
            $previousQuery->count();
        
        if ($previousValue == 0) {
            return $currentValue > 0 ? 100 : 0;
        }
        
        return round((($currentValue - $previousValue) / $previousValue) * 100, 1);
    }
    
    /**
     * Get top items by count or sum
     */
    public static function getTopItems(string $model, string $groupBy, int $limit = 10, string $aggregateColumn = null): array
    {
        $query = $model::select($groupBy);
        
        if ($aggregateColumn) {
            $query->addSelect(DB::raw("SUM({$aggregateColumn}) as aggregate"));
        } else {
            $query->addSelect(DB::raw('COUNT(*) as aggregate'));
        }
        
        return $query->groupBy($groupBy)
            ->orderByDesc('aggregate')
            ->limit($limit)
            ->get()
            ->toArray();
    }
}
