<?php

if (!function_exists('formatPrice')) {
    /**
     * Format price with current currency
     */
    function formatPrice(float $priceInEur): string
    {
        $currency = \App\Services\CurrencyService::getCurrentCurrency();
        return \App\Services\CurrencyService::convertAndFormat($priceInEur, $currency);
    }
}

if (!function_exists('convertPrice')) {
    /**
     * Convert price to current currency
     */
    function convertPrice(float $priceInEur): float
    {
        $currency = \App\Services\CurrencyService::getCurrentCurrency();
        return \App\Services\CurrencyService::convert($priceInEur, $currency);
    }
}

if (!function_exists('getCurrentCurrency')) {
    /**
     * Get current currency
     */
    function getCurrentCurrency(): string
    {
        return \App\Services\CurrencyService::getCurrentCurrency();
    }
}

if (!function_exists('getCurrencySymbol')) {
    /**
     * Get currency symbol
     */
    function getCurrencySymbol(string $currency = null): string
    {
        $currency = $currency ?? getCurrentCurrency();
        $currencyEnum = \App\Enums\Currency::tryFrom($currency);
        return $currencyEnum ? $currencyEnum->getIcon() : '€';
    }
}
