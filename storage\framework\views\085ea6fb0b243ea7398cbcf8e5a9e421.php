<!DOCTYPE html>
<html>
<head>
    <title>Leaflet Test</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin="" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>
    <style>
        #test-map { height: 400px; width: 100%; }
    </style>
</head>
<body>
    <h1>Leaflet Test Page</h1>
    <div id="test-map"></div>
    
    <script>
        console.log('🧪 Testing Leaflet...');
        console.log('Leaflet available:', typeof L !== 'undefined');
        
        if (typeof L !== 'undefined') {
            console.log('Leaflet version:', L.version);
            
            try {
                const map = L.map('test-map').setView([48.8566, 2.3522], 10);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);
                
                L.marker([48.8566, 2.3522]).addTo(map)
                    .bindPopup('Test marker in Paris')
                    .openPopup();
                
                console.log('✅ Leaflet test successful!');
            } catch (error) {
                console.error('❌ Leaflet test failed:', error);
            }
        } else {
            console.error('❌ Leaflet not loaded');
        }
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\travel-platform\resources\views/test-leaflet.blade.php ENDPATH**/ ?>