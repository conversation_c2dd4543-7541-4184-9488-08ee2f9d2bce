@startuml Sprint Payment & Admin Management Use Case Diagram
!theme plain
skinparam packageStyle rectangle
skinparam usecase {
    BackgroundColor White
    BorderColor Black
    BorderThickness 2
    ArrowColor Black
}
skinparam actor {
    BackgroundColor White
    BorderColor Black
    BorderThickness 2
}

title Sprint Use Case Diagram - Payment System & Admin Management

' Actors
actor "👤 User" as User
actor "👨‍💼 Manager" as Manager
actor "🔧 Administrator" as Admin

' System boundary
rectangle "Travel Platform System" {

    ' Stripe Payment System Package
    package "Stripe Payment System" {
        usecase "Make Payment" as UC18
        usecase "Initialize Stripe\nPayment Intent" as UC18a
        usecase "Process Stripe\nPayment" as UC18b
        usecase "Handle Stripe\nWebhook" as UC18c
        usecase "Validate Payment\nStatus" as UC18d
        usecase "Confirm Booking" as UC18e
        note right of UC18 : Priority: High\nStripe-powered secure\npayment processing
    }

    ' Invoice Management Package
    package "Invoice Management" {
        usecase "Receive Invoice" as UC19
        usecase "Generate PDF\nInvoice" as UC19a
        usecase "Send Invoice\nEmail" as UC19b
        usecase "Download Invoice" as UC19c
        note right of UC19 : Priority: Medium\nPDF invoice generation\nand delivery system
    }

    ' Notification System Package
    package "Notification System" {
        usecase "Receive\nNotifications" as UC20
        usecase "Subscribe to\nDestination Updates" as UC20a
        usecase "Manage Notification\nPreferences" as UC20b
        usecase "View Notification\nHistory" as UC20c
        note right of UC20 : Priority: Medium\nEvent-based notifications\nfor favorite destinations
    }

    ' Admin Reservation Management Package
    package "Reservation Management" {
        usecase "View Reservations" as UC21
        usecase "Filter Reservations\nby Date" as UC21a
        usecase "Search Reservations\nby User" as UC21b
        usecase "Export Reservation\nReports" as UC21c
        usecase "Update Reservation\nStatus" as UC21d
        note right of UC21 : Priority: High\nComprehensive reservation\nmanagement for admins
    }

    ' Admin Support Management Package
    package "Support Management" {
        usecase "View Support\nMessages" as UC22
        usecase "Respond to\nSupport Tickets" as UC22a
        usecase "Update Ticket\nStatus" as UC22b
        usecase "Assign Tickets\nto Staff" as UC22c
        usecase "Generate Support\nReports" as UC22d
        note right of UC22 : Priority: High\nComplete support ticket\nmanagement system
    }
}

' Actor inheritance relationships
Manager --|> Admin : <<extends>>

' Use case relationships - Stripe Payment System (User only)
User --> UC18
UC18 ..> UC18a : <<include>>
UC18 ..> UC18b : <<include>>
UC18 ..> UC18c : <<include>>
UC18 ..> UC18d : <<include>>
UC18 ..> UC18e : <<include>>

' Use case relationships - Invoice Management (User only)
User --> UC19
UC19 ..> UC19a : <<include>>
UC19 ..> UC19b : <<include>>
UC19 .> UC19c : <<extend>>

' Use case relationships - Notification System (User only)
User --> UC20
UC20 ..> UC20a : <<include>>
UC20 .> UC20b : <<extend>>
UC20 .> UC20c : <<extend>>

' Use case relationships - Reservation Management (Admin only)
Admin --> UC21
Manager --> UC21
UC21 ..> UC21a : <<include>>
UC21 ..> UC21b : <<include>>
UC21 .> UC21c : <<extend>>
UC21 .> UC21d : <<extend>>

' Use case relationships - Support Management (Manager & Admin)
Manager --> UC22
Admin --> UC22
UC22 ..> UC22a : <<include>>
UC22 ..> UC22b : <<include>>
UC22 .> UC22c : <<extend>>
UC22 .> UC22d : <<extend>>

' Cross-package relationships
UC18 ..> UC19 : <<extend>>\nafter payment
UC18 ..> UC21 : <<extend>>\nupdate reservations
UC20 ..> UC22 : <<extend>>\nnotification issues

@enduml
