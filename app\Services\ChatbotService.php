<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\Log;

class ChatbotService
{
    protected $geminiService;
    protected $intentDetectionService;
    protected $contextManager;
    protected $databaseQueryService;

    public function __construct(
        GeminiService $geminiService,
        IntentDetectionService $intentDetectionService,
        ContextManager $contextManager,
        DatabaseQueryService $databaseQueryService
    ) {
        $this->geminiService = $geminiService;
        $this->intentDetectionService = $intentDetectionService;
        $this->contextManager = $contextManager;
        $this->databaseQueryService = $databaseQueryService;
    }

    public function processMessage(string $message, string $sessionId, array $contextHistory = []): array
    {
        try {
            // 1. Obtenir la session de chat
            $session = $this->getOrCreateSession($sessionId);

            // 2. <PERSON>uvegarder le message utilisateur
            $userMessage = $this->saveMessage($session, 'user', $message);

            // 3. Détecter l'intention et les entités
            $intentData = $this->intentDetectionService->detectIntentAndEntities($message, $contextHistory);

            // 4. Mettre à jour le contexte
            $this->contextManager->updateContext($session, $intentData);

            // 5. Récupérer les données de la base selon l'intention
            $databaseResults = $this->databaseQueryService->queryByIntent(
                $intentData['intent'],
                $intentData['entities']
            );

            // 6. Générer la réponse avec Gemini
            $responseData = $this->geminiService->generateResponse(
                $message,
                $intentData,
                $databaseResults,
                $contextHistory
            );

            // 7. Sauvegarder la réponse du bot
            $this->saveMessage($session, 'bot', $responseData['text'], $intentData['intent'], $intentData['entities'], $databaseResults);

            return [
                'text' => $responseData['text'],
                'show_destination_cards' => $responseData['show_destination_cards'] ?? false,
                'destinations' => $responseData['destinations'] ?? []
            ];

        } catch (\Exception $e) {
            Log::error('ChatbotService error', [
                'message' => $message,
                'sessionId' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'text' => "Désolé, je rencontre un problème technique. Pouvez-vous reformuler votre question ?",
                'show_destination_cards' => false,
                'destinations' => []
            ];
        }
    }

    private function getOrCreateSession(string $sessionId): ChatSession
    {
        return ChatSession::firstOrCreate(
            ['session_id' => $sessionId],
            [
                'user_id' => auth()->id(),
                'started_at' => now(),
                'context_data' => []
            ]
        );
    }

    private function saveMessage(
        ChatSession $session,
        string $sender,
        string $message,
        ?string $intent = null,
        ?array $entities = null,
        ?array $responseData = null
    ): ChatMessage {
        return ChatMessage::create([
            'chat_session_id' => $session->id,
            'sender' => $sender,
            'message' => $message,
            'intent' => $intent,
            'entities' => $entities,
            'response_data' => $responseData,
        ]);
    }
}
