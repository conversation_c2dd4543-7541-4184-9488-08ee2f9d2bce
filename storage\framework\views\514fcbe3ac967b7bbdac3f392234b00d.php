<main class="px-8 py-10 bg-white">
    
    <div class="flex flex-col lg:flex-row justify-between items-start gap-8 mt-6">
        
        <div class="flex-1 animate-fade-in">
            <div class="flex items-center gap-4 animate-slide-up">
                <img src="storage/avatar.jpg"
                    class="w-24 h-24 rounded-full animate-bounce-gentle hover:scale-110 transition-transform duration-300"
                    alt="AI Assistant">
                <div class="animate-fade-in-delayed">
                    <p class="text-sm text-gray-500 animate-fade-in">Hey there!</p>
                    <p class="font-semibold text-lg animate-fade-in-delayed">I'm your <span
                            class="text-black animate-pulse">AI assistant.</span></p>
                </div>
            </div>

            
            <div
                class="mt-6 bg-gradient-to-r from-pink-500 to-red-500 text-white rounded-xl p-4 w-full max-w-md shadow-lg animate-slide-up hover:shadow-xl hover:scale-105 transition-all duration-500 group">
                <p class="text-lg mb-4 group-hover:translate-x-1 transition-transform duration-300">Do you know where
                    you want to go next?</p>
                <div class="flex gap-4">
                    <button
                        class="bg-white text-pink-600 px-4 py-2 rounded-full font-medium hover:bg-gray-100 hover:scale-105 transform transition-all duration-300">Yes,
                        I know</button>
                    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('chatbot')); ?>"
                            class="bg-white text-pink-600 px-4 py-2 rounded-full font-medium hover:bg-gray-100 hover:scale-105 transform transition-all duration-300 inline-block text-center">Inspire
                            me</a>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>"
                            class="bg-white text-pink-600 px-4 py-2 rounded-full font-medium hover:bg-gray-100 hover:scale-105 transform transition-all duration-300 inline-block text-center">Login
                            to get inspired</a>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>

        
        <div class="relative flex-shrink-0 animate-fade-in-delayed">
            <img src="<?php echo e(asset('storage/statutdeliberte.jpeg')); ?>"
                class="rounded-2xl shadow-lg w-[500px] md:w-[700px] hover:shadow-xl hover:scale-105 transition-all duration-700 group"
                alt="City Image">
            <button
                class="absolute top-4 right-4 bg-white px-4 py-2 rounded-full text-sm shadow-md hover:bg-gray-100 hover:shadow-lg hover:scale-105 transform transition-all duration-300 animate-fade-in-down">
                Contact support
            </button>

            <!-- Floating elements around the image -->
            <div class="absolute -top-4 -left-4 w-8 h-8 bg-pink-200 rounded-full animate-float opacity-70"></div>
            <div
                class="absolute -bottom-6 -right-6 w-6 h-6 bg-purple-200 rounded-full animate-float-delayed opacity-60">
            </div>
            <div class="absolute top-1/2 -left-8 w-4 h-4 bg-blue-200 rounded-full animate-float-slow opacity-50"></div>
        </div>
    </div>

    
    <div class="mt-6 animate-slide-up">
        <h2 class="text-3xl font-bold mb-6 animate-fade-in">Trip ideas powered by our <span
                class="text-pink-500 animate-pulse">local experts</span></h2>

        
        <?php
            $icons = [
                'Gastronomy' => '🍽️',
                'Culture' => '🏛️',
                'Bicycle' => '🚴',
                'Kite Surfing' => '🏄',
                'Beach' => '🏖️',
            ];
        ?>

        <div class="flex gap-6 overflow-x-auto mb-6 text-center">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label => $icon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php $index = $loop->index; ?>
                <div class="flex flex-col items-center text-sm text-gray-700 min-w-[70px] group cursor-pointer transform hover:scale-110 transition-all duration-300 animate-fade-in-up"
                    style="animation-delay: <?php echo e($index * 0.1); ?>s">
                    <div
                        class="text-2xl group-hover:animate-bounce-gentle transition-all duration-300 group-hover:scale-125">
                        <?php echo e($icon); ?></div>
                    <span
                        class="group-hover:text-pink-500 group-hover:font-medium transition-all duration-300"><?php echo e($label); ?></span>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    
    <h2 class="text-xl font-bold mt-12 mb-4 animate-fade-in">Hotels</h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $hotels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $hotel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-xl shadow-sm hover:shadow-xl hover:border-blue-200 border border-transparent transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 group animate-fade-in-up cursor-pointer"
                style="animation-delay: <?php echo e($index * 0.1); ?>s"
                onclick="window.location.href='<?php echo e(route('hotels.show', $hotel->id)); ?>'">
                <div class="relative overflow-hidden">
                    <img src="<?php echo e(asset('storage/' . $hotel->image)); ?>"
                        class="w-full h-48 object-cover rounded-t-xl transition-transform duration-700 group-hover:scale-110"
                        alt="Hotel Image">

                    
                    <div class="absolute top-3 left-3 right-3 flex justify-between z-10">
                        <button
                            class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 relative z-20">
                            <svg class="w-4 h-4 text-gray-600 hover:text-blue-500 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z">
                                </path>
                            </svg>
                        </button>
                        <button wire:click="toggleFavorite('hotel', <?php echo e($hotel->id); ?>)"
                            class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 group/heart relative z-20">
                            <!--[if BLOCK]><![endif]--><?php if($this->isFavorited('hotel', $hotel->id)): ?>
                                <svg class="w-4 h-4 text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
                                    fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                    </path>
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4 text-gray-600 hover:text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                    </path>
                                </svg>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </button>
                    </div>

                    
                    <div
                        class="absolute top-3 right-16 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium hover:bg-white hover:scale-110 transform transition-all duration-300">
                        <span class="animate-star-twinkle">⭐ 4.8</span>
                    </div>

                    <!-- Hover Overlay -->
                    <div
                        class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    </div>
                </div>

                <div class="p-4">
                    <div class="text-sm text-gray-500 mb-1 group-hover:text-gray-700 transition-colors duration-300">📍
                        <?php echo e($hotel->name); ?> - Porto - Funchal</div>
                    <h3
                        class="font-bold text-lg mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                        <?php echo e($hotel->description); ?></h3>
                    <div class="flex items-center gap-4 text-xs text-gray-500 mb-3">
                        <span class="flex items-center gap-1 hover:text-pink-500 transition-colors duration-300">🍷
                            Gastronomy</span>
                        <span class="flex items-center gap-1 hover:text-blue-500 transition-colors duration-300">🏄
                            Surf</span>
                        <span class="hover:text-purple-500 transition-colors duration-300">+1</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <span
                                class="text-xl font-bold group-hover:text-blue-600 transition-colors duration-300"><?php echo e(formatPrice(699)); ?></span>
                            <span class="text-sm text-gray-500">/week</span>
                        </div>
                        <button
                            class="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 hover:scale-105 transform transition-all duration-300">
                            Add to project
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <h2 class="text-xl font-bold mt-12 mb-4 animate-fade-in">Activities</h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-xl shadow-sm hover:shadow-xl hover:border-orange-200 border border-transparent transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 group animate-fade-in-up cursor-pointer"
                style="animation-delay: <?php echo e($index * 0.15); ?>s"
                onclick="window.location.href='<?php echo e(route('activities.show', $activity->id)); ?>'">
                <div class="relative overflow-hidden">
                    <img src="<?php echo e(asset('storage/' . $activity->image)); ?>"
                        class="w-full h-48 object-cover rounded-t-xl transition-transform duration-700 group-hover:scale-110"
                        alt="Activity Image">

                    
                    <div class="absolute top-3 left-3 right-3 flex justify-between z-10">
                        <button
                            class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 relative z-20"
                            onclick="event.stopPropagation()">
                            <svg class="w-4 h-4 text-gray-600 hover:text-orange-500 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z">
                                </path>
                            </svg>
                        </button>
                        <button wire:click="toggleFavorite('activity', <?php echo e($activity->id); ?>)"
                            class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 group/heart relative z-20"
                            onclick="event.stopPropagation()">
                            <!--[if BLOCK]><![endif]--><?php if($this->isFavorited('activity', $activity->id)): ?>
                                <svg class="w-4 h-4 text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
                                    fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                    </path>
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4 text-gray-600 hover:text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                    </path>
                                </svg>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </button>
                    </div>

                    
                    <div
                        class="absolute top-3 right-16 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium hover:bg-white hover:scale-110 transform transition-all duration-300">
                        <span class="animate-star-twinkle">⭐ 4.6</span>
                    </div>

                    <!-- Hover Overlay -->
                    <div
                        class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    </div>
                </div>

                <div class="p-4">
                    <div class="text-sm text-gray-500 mb-1 group-hover:text-gray-700 transition-colors duration-300">
                        <?php echo e($activity->destination?->name ?? 'Activity'); ?></div>
                    <h3
                        class="font-bold text-lg mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors duration-300">
                        <?php echo e($activity->name); ?></h3>
                    <div class="flex items-center gap-4 text-xs text-gray-500 mb-3">
                        <span class="flex items-center gap-1 hover:text-blue-500 transition-colors duration-300">🕒
                            <?php echo e($activity->duration); ?>h</span>
                        <span class="flex items-center gap-1 hover:text-orange-500 transition-colors duration-300">🎯
                            <?php echo e($activity->difficulty ?? 'Adventure'); ?></span>
                        <span class="hover:text-purple-500 transition-colors duration-300">+2</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-xl font-bold group-hover:text-orange-600 transition-colors duration-300">
                                <?php echo e(formatPrice($activity->activityContracts->min('adult_price') ?? 299)); ?>

                            </span>
                            <span class="text-sm text-gray-500">/person</span>
                        </div>
                        <button
                            class="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 hover:scale-105 transform transition-all duration-300"
                            onclick="event.stopPropagation()">
                            Add to project
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
    <h2 class="text-xl font-bold mt-12 mb-4">Transfers</h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $transfers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transfer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-xl shadow-sm hover:shadow-lg transition-shadow">
                <div class="relative">
                    <img src="<?php echo e(asset('storage/' . $transfer->main_image)); ?>"
                        class="w-full h-48 object-cover rounded-t-xl" alt="Transfer Image">

                    
                    <div class="absolute top-3 left-3 right-3 flex justify-between z-10">
                        <button
                            class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 relative z-20">
                            <svg class="w-4 h-4 text-gray-600 hover:text-green-500 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z">
                                </path>
                            </svg>
                        </button>
                        <button wire:click="toggleFavorite('transfer', <?php echo e($transfer->id); ?>)"
                            class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 group/heart relative z-20">
                            <!--[if BLOCK]><![endif]--><?php if($this->isFavorited('transfer', $transfer->id)): ?>
                                <svg class="w-4 h-4 text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
                                    fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                    </path>
                                </svg>
                            <?php else: ?>
                                <svg class="w-4 h-4 text-gray-600 hover:text-red-500 transition-colors duration-300 group-hover/heart:animate-bounce-gentle"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                    </path>
                                </svg>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </button>
                    </div>

                    
                    <div
                        class="absolute top-3 right-16 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ 4.9
                    </div>
                </div>

                <div class="p-4">
                    <div class="text-sm text-gray-500 mb-1">📍 <?php echo e($transfer->name); ?> - Porto - Funchal</div>
                    <h3 class="font-bold text-lg mb-2 line-clamp-2"><?php echo e($transfer->description); ?></h3>
                    <div class="flex items-center gap-4 text-xs text-gray-500 mb-3">
                        <span class="flex items-center gap-1">🚗 Transfer</span>
                        <span class="flex items-center gap-1">⚡ Fast</span>
                        <span>+1</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-xl font-bold"><?php echo e(formatPrice(149)); ?></span>
                            <span class="text-sm text-gray-500">/trip</span>
                        </div>
                        <button
                            class="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors">
                            Add to project
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
    <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-3 gap-6">

    </div>
    <!-- View All Trip Ideas Header -->
    <section class="px-6 md:px-20 py-12">
        <h2 class="text-3xl md:text-4xl font-bold">
            View All trip <span class="text-pink-500">ideas</span>
        </h2>
    </section>

    <!-- Create Account CTA Section -->
    <section class="px-6 md:px-20">
        <div
            class="bg-gradient-to-r from-[#dc4a67] to-[#e06e80] text-white rounded-2xl p-8 flex flex-col md:flex-row justify-between items-center gap-8">
            <div class="md:max-w-md">
                <h3 class="text-3xl font-bold mb-2">Let’s create your account now!</h3>
                <p class="text-sm">
                    On te conseille d'enregistrer un profil si tu veux entraîner <br />
                    l’IA avec tes préférences et en faire ta travel bestie!
                </p>
            </div>
            <div class="flex flex-col gap-4 w-full md:max-w-xs">
                <!-- Google button -->
                <button
                    class="flex items-center justify-center gap-3 bg-white text-gray-700 py-3 px-4 rounded-lg shadow">
                    <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" class="w-5 h-5" />
                    <span class="text-sm">continue with Google</span>
                </button>
                <p class="text-xs text-center text-white/80">or continue with</p>
                <!-- Create Account button -->
                <button class="bg-black text-white font-semibold py-3 px-4 rounded-lg hover:bg-gray-800">
                    Create account
                </button>
            </div>
        </div>
    </section>

    <!-- Why TravelShaper Section -->
    <section class="px-6 md:px-20 py-16">
        <div class="bg-white rounded-2xl shadow-sm p-8">
            <h2 class="text-2xl md:text-3xl font-bold mb-10">Why TravelShaper</h2>
            <div class="grid md:grid-cols-3 gap-8 text-sm text-gray-700">
                <!-- Feature 1 -->
                <div class="flex items-start gap-4">
                    <div
                        class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center text-pink-500 text-xl">
                        🎯
                    </div>
                    <div>
                        <p class="font-semibold mb-2">Gives you personalized itinerary options</p>
                        <p class="text-sm text-gray-500">Our AI analyzes your preferences to create custom travel plans
                            that match your style and budget perfectly.</p>
                    </div>
                </div>
                <!-- Feature 2 -->
                <div class="flex items-start gap-4">
                    <div
                        class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center text-pink-500 text-xl">
                        ⚡
                    </div>
                    <div>
                        <p class="font-semibold mb-2">Quick and easy travel planning</p>
                        <p class="text-sm text-gray-500">Plan your entire trip in minutes, not hours. Our streamlined
                            process makes travel planning effortless.</p>
                    </div>
                </div>
                <!-- Feature 3 -->
                <div class="flex items-start gap-4">
                    <div
                        class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center text-pink-500 text-xl">
                        🎧
                    </div>
                    <div>
                        <p class="font-semibold mb-2">Get your work personal travel assistant</p>
                        <p class="text-sm text-gray-500">24/7 AI-powered support to help you with bookings, changes,
                            and travel recommendations.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Join Users Section -->
    <section class="px-6 md:px-20 py-16 text-center animate-slide-up">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold mb-4 animate-fade-in">
                Join <span class="text-pink-500 animate-count-up">50,000+</span>
                <div class="inline-flex items-center gap-2 animate-fade-in-delayed">

                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face&auto=format"
                        alt="User"
                        class="w-12 h-12 rounded-full border-2 border-white shadow-sm -ml-2 hover:scale-110 hover:z-10 transition-all duration-300 animate-bounce-gentle">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face&auto=format"
                        alt="User"
                        class="w-12 h-12 rounded-full border-2 border-white shadow-sm -ml-2 hover:scale-110 hover:z-10 transition-all duration-300 animate-bounce-gentle"
                        style="animation-delay: 0.2s">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format"
                        alt="User"
                        class="w-12 h-12 rounded-full border-2 border-white shadow-sm -ml-2 hover:scale-110 hover:z-10 transition-all duration-300 animate-bounce-gentle"
                        style="animation-delay: 0.4s">
                </div>
            </h2>
            <h3 class="text-2xl md:text-3xl font-bold mb-8 animate-fade-in-delayed">
                users planning their trips<br>
                with <span class="text-pink-500 animate-pulse">TravelShaper</span>
            </h3>
        </div>
    </section>

    <!-- Venture off the beaten path Section -->
    <section class="px-6 md:px-20 py-16 animate-slide-up">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl md:text-3xl font-bold animate-fade-in">Check out our <span
                    class="text-pink-500 animate-pulse">destinations</span></h2>
            <a href="<?php echo e(route('destinations')); ?>"
                class="text-pink-500 hover:text-pink-600 font-medium transition-colors hover:scale-105 transform duration-300 animate-fade-in-delayed">View
                all</a>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <!--[if BLOCK]><![endif]--><?php if($destination && $destination->name): ?>
                    <a href="<?php echo e(route('destinations.show', $destination->id)); ?>"
                        class="relative group cursor-pointer block animate-fade-in-up hover:scale-105 transform transition-all duration-500 hover:shadow-xl"
                        style="animation-delay: <?php echo e($index * 0.1); ?>s">
                        <div class="aspect-[4/3] rounded-xl overflow-hidden">
                            <!--[if BLOCK]><![endif]--><?php if($destination->image): ?>
                                <img src="<?php echo e(asset('storage/' . $destination->image)); ?>"
                                    alt="<?php echo e($destination->name ?? 'Destination'); ?>"
                                    class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                            <?php else: ?>
                                <div
                                    class="w-full h-full bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center transition-all duration-500 group-hover:from-pink-500 group-hover:to-purple-600">
                                    <span class="text-white text-2xl animate-bounce-gentle">📍</span>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Enhanced Overlay -->
                            <div
                                class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-80 group-hover:opacity-90 transition-all duration-300">
                            </div>

                            <!-- Destination Name with Animation -->
                            <div
                                class="absolute bottom-3 left-3 right-3 transform group-hover:translate-y-[-2px] transition-transform duration-300">
                                <h3
                                    class="text-white font-semibold text-sm md:text-base group-hover:text-pink-200 transition-colors duration-300">
                                    <?php echo e($destination->name ?? 'Unknown Destination'); ?></h3>
                                <!--[if BLOCK]><![endif]--><?php if($destination->country): ?>
                                    <p
                                        class="text-white/80 text-xs group-hover:text-white transition-colors duration-300">
                                        <?php echo e($destination->country); ?></p>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            <!-- Heart Button -->
                            <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                                <button wire:click="toggleFavorite('destination', <?php echo e($destination->id); ?>)"
                                    class="absolute top-3 right-3 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center hover:bg-white/30 transition-all duration-300 z-10"
                                    onclick="event.stopPropagation()">
                                    <!--[if BLOCK]><![endif]--><?php if($this->isFavorited('destination', $destination->id)): ?>
                                        <svg class="w-5 h-5 text-red-500 fill-current" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                            </path>
                                        </svg>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </button>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Floating indicator -->
                            <div
                                class="absolute top-3 left-3 w-2 h-2 bg-pink-400 rounded-full animate-pulse opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            </div>
                        </div>
                    </a>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- Fallback destinations if none exist -->
                <div class="relative group cursor-pointer animate-fade-in-up hover:scale-105 transform transition-all duration-500"
                    style="animation-delay: 0.1s">
                    <div
                        class="aspect-[4/3] rounded-xl overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center transition-all duration-500 group-hover:from-blue-500 group-hover:to-blue-700">
                        <span class="text-white text-2xl animate-bounce-gentle">🏝️</span>
                    </div>
                    <div
                        class="absolute bottom-3 left-3 right-3 transform group-hover:translate-y-[-2px] transition-transform duration-300">
                        <h3
                            class="text-white font-semibold text-sm group-hover:text-blue-200 transition-colors duration-300">
                            Tropical Paradise</h3>
                        <p class="text-white/80 text-xs group-hover:text-white transition-colors duration-300">Maldives
                        </p>
                    </div>
                </div>

                <div class="relative group cursor-pointer animate-fade-in-up hover:scale-105 transform transition-all duration-500"
                    style="animation-delay: 0.2s">
                    <div
                        class="aspect-[4/3] rounded-xl overflow-hidden bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center transition-all duration-500 group-hover:from-green-500 group-hover:to-green-700">
                        <span class="text-white text-2xl animate-bounce-gentle">🏔️</span>
                    </div>
                    <div
                        class="absolute bottom-3 left-3 right-3 transform group-hover:translate-y-[-2px] transition-transform duration-300">
                        <h3
                            class="text-white font-semibold text-sm group-hover:text-green-200 transition-colors duration-300">
                            Mountain Adventure</h3>
                        <p class="text-white/80 text-xs group-hover:text-white transition-colors duration-300">
                            Switzerland</p>
                    </div>
                </div>

                <div class="relative group cursor-pointer animate-fade-in-up hover:scale-105 transform transition-all duration-500"
                    style="animation-delay: 0.3s">
                    <div
                        class="aspect-[4/3] rounded-xl overflow-hidden bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center transition-all duration-500 group-hover:from-orange-500 group-hover:to-red-600">
                        <span class="text-white text-2xl animate-bounce-gentle">🏜️</span>
                    </div>
                    <div
                        class="absolute bottom-3 left-3 right-3 transform group-hover:translate-y-[-2px] transition-transform duration-300">
                        <h3
                            class="text-white font-semibold text-sm group-hover:text-orange-200 transition-colors duration-300">
                            Desert Safari</h3>
                        <p class="text-white/80 text-xs group-hover:text-white transition-colors duration-300">Morocco
                        </p>
                    </div>
                </div>

                <div class="relative group cursor-pointer animate-fade-in-up hover:scale-105 transform transition-all duration-500"
                    style="animation-delay: 0.4s">
                    <div
                        class="aspect-[4/3] rounded-xl overflow-hidden bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center transition-all duration-500 group-hover:from-purple-500 group-hover:to-pink-600">
                        <span class="text-white text-2xl animate-bounce-gentle">🏛️</span>
                    </div>
                    <div
                        class="absolute bottom-3 left-3 right-3 transform group-hover:translate-y-[-2px] transition-transform duration-300">
                        <h3
                            class="text-white font-semibold text-sm group-hover:text-purple-200 transition-colors duration-300">
                            Ancient Wonders</h3>
                        <p class="text-white/80 text-xs group-hover:text-white transition-colors duration-300">Greece
                        </p>
                    </div>
                </div>

                <div class="relative group cursor-pointer animate-fade-in-up hover:scale-105 transform transition-all duration-500"
                    style="animation-delay: 0.5s">
                    <div
                        class="aspect-[4/3] rounded-xl overflow-hidden bg-gradient-to-br from-teal-400 to-blue-500 flex items-center justify-center transition-all duration-500 group-hover:from-teal-500 group-hover:to-blue-600">
                        <span class="text-white text-2xl animate-bounce-gentle">🌊</span>
                    </div>
                    <div
                        class="absolute bottom-3 left-3 right-3 transform group-hover:translate-y-[-2px] transition-transform duration-300">
                        <h3
                            class="text-white font-semibold text-sm group-hover:text-teal-200 transition-colors duration-300">
                            Coastal Escape</h3>
                        <p class="text-white/80 text-xs group-hover:text-white transition-colors duration-300">Portugal
                        </p>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </section>

    <!-- Font Awesome for icons -->
    <script src="https://kit.fontawesome.com/YOUR_KIT_ID.js" crossorigin="anonymous"></script>

</main>
<?php /**PATH C:\laragon\www\travel-platform\resources\views/livewire/list-product.blade.php ENDPATH**/ ?>