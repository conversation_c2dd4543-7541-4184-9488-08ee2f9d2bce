<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('hotels', function (Blueprint $table) {
            // Ajouter les nouvelles colonnes
        
           
            
            
      
            $table->string('street')->nullable();
            $table->string('state')->nullable();
            $table->string('zip')->nullable();

            // Supprimer les colonnes inutiles
            $table->dropColumn([
                'amenities', 
                
                'availability', 
                 
                'price', 
                
            ]);
        });
    }

    public function down()
    {
        Schema::table('hotels', function (Blueprint $table) {
            // Annuler les ajouts
            $table->dropColumn([
                'email', 'phone', 'public address', 'map address', 
                'street', 'city', 'state', 'zip'
            ]);

            // Réajouter les anciennes colonnes supprimées si nécessaire
            $table->text('amenities')->nullable();
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->boolean('availability')->default(true);
            $table->integer('rooms_count')->nullable();
            $table->decimal('price', 10, 2)->nullable();
            $table->string('country')->nullable();
        });
    }
};