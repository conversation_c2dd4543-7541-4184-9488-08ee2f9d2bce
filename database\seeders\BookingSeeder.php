<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Booking;
use App\Models\User;
use App\Models\Activity;
use App\Models\Hotel;
use App\Models\Transfer;
use Carbon\Carbon;

class BookingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users, activities, hotels, and transfers
        $users = User::limit(10)->get();
        $activities = Activity::limit(5)->get();
        $hotels = Hotel::limit(5)->get();
        $transfers = Transfer::limit(3)->get();

        if ($users->isEmpty()) {
            $this->command->warn('No users found. Please seed users first.');
            return;
        }

        $statuses = ['confirmed', 'pending', 'cancelled'];
        $paymentStatuses = ['paid', 'pending', 'failed'];

        // Create activity bookings
        if ($activities->isNotEmpty()) {
            foreach ($activities as $activity) {
                for ($i = 0; $i < rand(3, 8); $i++) {
                    Booking::create([
                        'user_id' => $users->random()->id,
                        'bookable_type' => Activity::class,
                        'bookable_id' => $activity->id,
                        'booking_date' => Carbon::now()->addDays(rand(1, 60)),
                        'booking_time' => sprintf('%02d:00', rand(8, 18)),
                        'adults' => rand(1, 4),
                        'children' => rand(0, 2),
                        'total_amount' => rand(50, 500),
                        'status' => $statuses[array_rand($statuses)],
                        'payment_status' => $paymentStatuses[array_rand($paymentStatuses)],
                        'created_at' => Carbon::now()->subDays(rand(0, 30)),
                    ]);
                }
            }
            $this->command->info('Created activity bookings');
        }

        // Create hotel bookings
        if ($hotels->isNotEmpty()) {
            foreach ($hotels as $hotel) {
                for ($i = 0; $i < rand(2, 6); $i++) {
                    Booking::create([
                        'user_id' => $users->random()->id,
                        'bookable_type' => Hotel::class,
                        'bookable_id' => $hotel->id,
                        'booking_date' => Carbon::now()->addDays(rand(1, 90)),
                        'booking_time' => '15:00', // Check-in time
                        'adults' => rand(1, 4),
                        'children' => rand(0, 3),
                        'total_amount' => rand(100, 1000),
                        'status' => $statuses[array_rand($statuses)],
                        'payment_status' => $paymentStatuses[array_rand($paymentStatuses)],
                        'created_at' => Carbon::now()->subDays(rand(0, 45)),
                    ]);
                }
            }
            $this->command->info('Created hotel bookings');
        }

        // Create transfer bookings
        if ($transfers->isNotEmpty()) {
            foreach ($transfers as $transfer) {
                for ($i = 0; $i < rand(1, 4); $i++) {
                    Booking::create([
                        'user_id' => $users->random()->id,
                        'bookable_type' => Transfer::class,
                        'bookable_id' => $transfer->id,
                        'booking_date' => Carbon::now()->addDays(rand(1, 30)),
                        'booking_time' => sprintf('%02d:%02d', rand(6, 22), [0, 15, 30, 45][rand(0, 3)]),
                        'adults' => rand(1, 6),
                        'children' => rand(0, 2),
                        'total_amount' => rand(25, 200),
                        'status' => $statuses[array_rand($statuses)],
                        'payment_status' => $paymentStatuses[array_rand($paymentStatuses)],
                        'created_at' => Carbon::now()->subDays(rand(0, 20)),
                    ]);
                }
            }
            $this->command->info('Created transfer bookings');
        }

        $totalBookings = Booking::count();
        $this->command->info("Total bookings created: {$totalBookings}");
    }
}
