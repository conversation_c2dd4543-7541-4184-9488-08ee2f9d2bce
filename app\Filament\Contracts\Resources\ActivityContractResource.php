<?php

namespace App\Filament\Contracts\Resources;

use App\Filament\Contracts\Resources\ActivityContractResource\Pages;
use App\Filament\Contracts\Resources\ActivityContractResource\RelationManagers;
use App\Models\ActivityContract;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Components\DatePicker;

use App\Enums\ContractStatus;
use App\Enums\Currency;
use App\Models\MeetingPoint;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Doctrine\DBAL\Schema\Schema;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Get;

use function Laravel\Prompts\select;

class ActivityContractResource extends Resource
{
    protected static ?string $model = ActivityContract::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Activity Details')
                    ->schema([
                        Tabs::make('Tabs')
                            ->columnSpan('full')
                            ->tabs([
                                Tabs\Tab::make('Information')
                                    ->columns(3)
                                    ->schema([
                                        TextInput::make('contract_number')->label('Contract Number')->required()->columnSpan(2),
                                        Forms\Components\Select::make('type')
                                            ->required()
                                            ->options([
                                                'FIT' => 'FIT',
                                                'Dynamic Rates' => 'Dynamic Rates',
                                                'Group Rate Plan' => 'Group Rate Plan',
                                                'Package Rate Plan' => 'Package Rate Plan',
                                                'Fly & Drive Rate Plan' => 'Fly & Drive Rate Plan',
                                            ])
                                            ->placeholder('Sélectionnez un type'),
                                            Forms\Components\Select::make('main_supplier')
                                            ->required()
                                            ->options([
                                                'test1' => 'test1',
                                                'test2' => 'test2',
                                            ])
                                            ->placeholder('Sélectionnez une option'),

                                        Forms\Components\Toggle::make('direct_supplier')
                                            ->live()
                                            ->default(true),
                                        Grid::make(2)->schema([
                                            
                                            Forms\Components\Select::make('intermediate_supplier')

                                                ->options([
                                                    'test1' => 'test1',
                                                    'test2' => 'test2',
                                                ])

                                                ->placeholder('Sélectionnez un point'),
                                        ])->hidden(function (Get $get) {
                                            return $get('direct_supplier') ? true : false;
                                        }),
                                        Select::make('activities')
                                            ->searchable()
                                            ->preload()
                                            ->live()
                                            ->native(false)
                                            ->multiple()
                                            ->relationship('activities', "title"),

                                        Forms\Components\Radio::make('type_of_service')->live()
                                            ->options([

                                                'Private' => 'Private',
                                                'Shared' => 'Shared',
                                            ])
                                            ->required(),
                                        Forms\Components\Textarea::make('description')
                                            ->required()
                                            ->columnSpanFull(),

                                        Forms\Components\Select::make('markets')
                                            ->label('Market(s)')
                                            ->multiple()
                                            ->relationship('markets', 'name')
                                            ->preload()
                                            ->columnSpanFull()
                                            ->searchable()
                                            ->required(),

                                        Section::make("what's included / what'is not included")
                                            ->schema([
                                                Repeater::make('whats_included')

                                                    ->simple(

                                                        Forms\Components\Select::make('whats_included')
                                                            ->options([
                                                                'Ambiance musicale' => 'Ambiance musicale',
                                                                'Tables avec vue panoramique' => 'Tables avec vue panoramique',
                                                                'Prise de photos autorisée' => 'Prise de photos autorisée',
                                                                'nouveau incluede services' => 'nouveau incluede services',
                                                                'Nourriture et boissons' => 'Nourriture et boissons',
                                                                'Commentaire audio depuis votre appareil mobile' => 'Commentaire audio depuis votre appareil mobile',
                                                                'Spectacles' => 'Spectacles',
                                                                'Zone de selfies avec les personnages' => 'Zone de selfies avec les personnages',
                                                                'Sièges extérieurs et intérieurs' => 'Sièges extérieurs et intérieurs',
                                                            ]),

                                                    )->columnSpan(2),


                                                Repeater::make('whats_not_included')
                                                ->label('Not Included')
                                                    ->simple(
                                                        Forms\Components\Select::make('whats_not_included')
                                                            ->options([
                                                                'Les animaux ne sont pas acceptés' => 'Les animaux ne sont pas acceptés',
                                                                'Deposit amount must be paid on site and is not linked to the use of the Application' => 'Deposit amount must be paid on site and is not linked to the use of the Application',
                                                                'Nourriture et boissons' => 'Nourriture et boissons',
                                                                'Prise en charge et retour à l’hôtel' => 'Prise en charge et retour à l’hôtel',
                                                            ])
                                                    )->columnSpan(2)

                                            ])
                                    ]),
                                    Tabs\Tab::make('Price')

                                    
                                    
                                    ->schema(
                                        function (Get $get) {
                                        $activities = $get('activities') ?? [];
                                
                                        if (!is_array($activities)) {
                                            return [];
                                        }
                                
                                        $repeaterActivities = [];
                                
                                        foreach ($activities as $activityId) {
                                            $activity = \App\Models\Activity::find($activityId);
                                            if (!$activity) continue;
                                
                                            $repeaterActivities[] = Section::make($activity->title)
                                                ->schema([
                                                    Placeholder::make("price_info_{$activityId}")
                                                        ->label('Selected Activity')
                                                        ->content($activity->title)
                                                        ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                                
                                                    TableRepeater::make("price_{$activityId}")
                                                        ->columns(2)
                                                        ->headers([
                                                            Header::make('Start Date'),
                                                            Header::make('End Date'),
                                                            Header::make('Meeting Point'),
                                                            Header::make('Rates'),
                                                            Header::make('Activity Details'),
                                                            Header::make('Actions'),
                                                        ])
                                                        ->schema([
                                                            DatePicker::make('start_date')->label('Start Date'),
                                                            DatePicker::make('end_date')->label('End Date'),
                                
                                                            Select::make('meeting_point')
                                                                ->label('Meeting Point')
                                                                ->options(
                                                                    \App\Models\Activity::whereIn('id', $activities)
                                                                        ->pluck('meeting_point', 'meeting_point')
                                                                        ->unique()
                                                                        ->toArray()
                                                                )
                                                                ->searchable()
                                                                ->preload()
                                                                ->reactive(),
                                
                                                            // Your Actions::make([]) code goes here...
                                                            Actions::make([
                                                                Action::make('activity_details')
                                                                    ->label('Activity Details')
                                                                    ->icon('heroicon-o-information-circle')
                                                                    ->modalHeading('Activity Details')
                                                                    ->modalCancelActionLabel('Close')
                                                                    ->form([
                                                                        Forms\Components\TextInput::make('price_type')->label('Price Type'),
                                                                        Forms\Components\Select::make('currency')->label('Currency')->options(Currency::class),
                                                                        Forms\Components\Select::make('language')->label('Language')->multiple()->options([
                                                                            'arabe' => 'arabe',
                                                                            'anglais' => 'anglais',
                                                                            'francais' => 'francais',
                                                                            'italien' => 'italien',
                                                                        ]),
                                                                        Forms\Components\Select::make('starting_points')
                                                                            ->label('Starting Points')
                                                                            ->options(MeetingPoint::pluck('name', 'id'))
                                                                            ->multiple()
                                                                            ->searchable()
                                                                            ->preload(),
                                                                        Forms\Components\Toggle::make('pickup_and_meet_at_start_point')->live()->columnSpan(3)->required(),
                                
                                                                        Grid::make(2)->schema([
                                                                            Repeater::make('meeting_points')
                                                                                ->label('Meeting Points')
                                                                                ->schema([
                                                                                    Select::make('meeting_point_id')
                                                                                        ->label('Meeting Point')
                                                                                        ->options(MeetingPoint::pluck('name', 'id'))
                                                                                        ->searchable()
                                                                                        ->preload()
                                                                                        ->required(),
                                                                                    TextInput::make('note')
                                                                                        ->label('Note')
                                                                                        ->placeholder('Optional comment or note'),
                                                                                ])
                                                                                ->defaultItems(1)
                                                                                ->minItems(1)
                                                                                ->addActionLabel('Add Meeting Point')
                                                                                ->columns(3),
                                                                        ])->hidden(fn (Get $get) => !$get('pickup_and_meet_at_start_point')),
                                
                                                                        Forms\Components\TextInput::make('destination_id')->required()->numeric(),
                                                                        Forms\Components\Radio::make('end_point_return_to')->live()->options([
                                                                            'Pick up point' => 'Pick up point',
                                                                            'Meeting point' => 'Meeting point',
                                                                            'Other' => 'Other',
                                                                        ]),
                                                                        Forms\Components\Select::make('ending_points')
                                                                            ->label('Ending Points')
                                                                            ->options(MeetingPoint::pluck('name', 'id'))
                                                                            ->multiple()
                                                                            ->searchable()
                                                                            ->preload()
                                                                            ->hidden(fn (Get $get) => $get('end_point_return_to') !== 'Other'),
                                
                                                                        // Rate details
                                                                        TextInput::make('adult_min')->numeric()->required(),
                                                                        TextInput::make('adult_max')->numeric()->required(),
                                                                        TextInput::make('child_min')->numeric()->required(),
                                                                        TextInput::make('child_max')->numeric()->required(),
                                                                        TextInput::make('infant_min')->numeric()->required(),
                                                                        TextInput::make('infant_max')->numeric()->required(),
                                
                                                                        Section::make("Rates")->schema([
                                                                            Section::make('Adults')->schema([
                                                                                TextInput::make('adult_rate')->numeric()->required(),
                                                                            ]),
                                                                            Section::make('Children')->schema([
                                                                                TextInput::make('child_rate')->numeric()->required(),
                                                                                TextInput::make('child_minimum_age')->numeric()->required(),
                                                                                TextInput::make('child_maximum_age')->numeric()->required(),
                                                                            ]),
                                                                            Section::make('Infants')->schema([
                                                                                TextInput::make('infant_rate')->numeric()->required(),
                                                                                TextInput::make('infant_minimum_age')->numeric()->required(),
                                                                                TextInput::make('infant_maximum_age')->numeric()->required(),
                                                                            ]),
                                                                        ])->columns(3)->columnSpan(3),
                                                                    ])
                                                                    ->action(function ($record, $data, Get $get) {
                                                                        $record->price = [
                                                                            'start_date' => $get('start_date'),
                                                                            'end_date' => $get('end_date'),
                                                                            'rates' => [
                                                                                'price_type' => $data['price_type'],
                                                                                'currency' => $data['currency'],
                                                                                'language' => $data['language'],
                                                                                'starting_points' => $data['starting_points'],
                                                                                'ending_points' => $data['end_point_return_to'] === 'Other' ? $data['ending_points'] : null,
                                                                                'meeting_point' => $data['pickup_and_meet_at_start_point'] ? $get('meeting_point') : null,
                                                                                'pickup_and_meet_at_start_point' => $data['pickup_and_meet_at_start_point'],
                                                                                'end_point_return_to' => $data['end_point_return_to'],
                                                                                'destination_id' => $data['destination_id'],
                                                                                'adult_min' => $data['adult_min'],
                                                                                'adult_max' => $data['adult_max'],
                                                                                'child_min' => $data['child_min'],
                                                                                'child_max' => $data['child_max'],
                                                                                'infant_min' => $data['infant_min'],
                                                                                'infant_max' => $data['infant_max'],
                                                                                'adult_rate' => $data['adult_rate'],
                                                                                'child_rate' => $data['child_rate'],
                                                                                'child_minimum_age' => $data['child_minimum_age'],
                                                                                'child_maximum_age' => $data['child_maximum_age'],
                                                                                'infant_rate' => $data['infant_rate'],
                                                                                'infant_minimum_age' => $data['infant_minimum_age'],
                                                                                'infant_maximum_age' => $data['infant_maximum_age'],
                                                                            ]
                                                                        ];
                                                                        $record->save();
                                                                    })
                                                            ]),
                                                        ])
                                                ]);
                                        }
                                
                                        return $repeaterActivities;
                                    }),
                                
                                Tabs\Tab::make('Inventory')
                                ->schema(function (Get $get) {
                                    $activities = $get('activities') ?? [];
                            
                                    if (!is_array($activities)) {
                                        return [];
                                    }
                            
                                    $repeaterActivities = [];
                            
                                    foreach ($activities as $activityId) {
                                        $activity = \App\Models\Activity::find($activityId);
                                        if (!$activity) continue;
                            
                                        $repeaterActivities[] = Section::make($activity->title)
                                            ->schema([
                                                Placeholder::make("inventory_info_{$activityId}")
                                                    ->label('Selected Activity')
                                                    ->content($activity->title)
                                                    ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                            
                                                TableRepeater::make("inventory_{$activityId}")
                                        


                                    ->columns(2)
                                    ->headers([
                                        Header::make('Start Date'),

                                        Header::make('End Date'),
                                        Header::make('Allocation type'),
                                        Header::make('Release'),
                                        Header::make('Allocation'),
                                        Header::make('Actions'),
                                    ])
                                    ->schema([
                                        DatePicker::make('start_date')->label('Start Date'),
                                        DatePicker::make('end_date')->label('End Date'),
                                        Select::make('allocation_type')
                                            ->label('Allocation type')
                                            ->options(function (Get $get) {
                                                $selectedActivityIds = $get('activities');

                                                if (!$selectedActivityIds || !is_array($selectedActivityIds)) {
                                                    return [];
                                                }

                                                return \App\Models\Activity::whereIn('id', $selectedActivityIds)
                                                    ->pluck('meeting_point', 'meeting_point') // use meeting_point as key and label
                                                    ->unique()
                                                    ->toArray();
                                            })
                                            ->searchable()
                                            ->preload()
                                            ->reactive(),
                                        TextInput::make('release')->numeric(),
                                        TextInput::make('allocation')->numeric(),
                                    ])
                                        
                                     

                                    
                                    
                                        ]);
                            }
                                return $repeaterActivities;
                                    }),
                                    
                                Tabs\Tab::make('Policies')
                                ->schema(function (Get $get) {
                                    $activities = $get('activities') ?? [];
                            
                                    if (!is_array($activities)) {
                                        return [];
                                    }
                            
                                    $repeaterActivities = []; 
                            
                                    foreach ($activities as $activityId) {
                                        $activity = \App\Models\Activity::find($activityId);
                                        if (!$activity) continue;
                            
                                        $repeaterActivities[] = Section::make($activity->title)
                                            ->schema([
                                                Placeholder::make("policy_info_{$activityId}")
                                                    ->label('Selected Activity')
                                                    ->content($activity->title)
                                                    ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                            
                                                TableRepeater::make("policy_{$activityId}")
    
    
                                                ->columns(2)
                                                ->headers([
                                                    Header::make('Period Start'),
    
                                                    Header::make('Period End'),
                                                    Header::make('Policies'),
                                                    Header::make('Show Policies Details'),
                                                    
                                                    Header::make('Actions'),
                                                ])
                                                ->schema([
                                                    DatePicker::make('period_start')->label('Period Start'),
                                                    DatePicker::make('period_end')->label('Period End'),
                                                    TextInput::make('pol'),    
                                                    Actions::make([
                                                    Action::make('show_policies_details')
                                                    ->label('Show Policies Details')
                                                    ->icon('heroicon-o-information-circle')
                                                    ->modalHeading('Policies Details')
                                                    
                                                    ->modalCancelActionLabel('Close')
                                                    
                                                    ->form([
                                                        section::make(null)
                                                        ->Schema([
                                                        section::make('Policy')
                                                        ->Schema([
                                                        
                                                        
                                                        Forms\Components\Select::make('types_policies')->label('Types Policies')
                                                        ->multiple()
                                                        ->required()
                                                        ->options([
                                                            'cancelation' => 'Cancelation',
                                                            'modification' => 'Modification',
                                                            'no_show' => 'No Show',
                                                        ]),
                                                        Forms\Components\TextInput::make('days_before_activity')->numeric()->required()->label('Days before Activity'),
                                                        Forms\Components\TextInput::make('hours_before_activity')->numeric()->required()->label('Hours before Activity'),
                                                        Forms\Components\Select::make('charge_type')->label('Charge Type')
                                                        ->live()
                                                        ->required()
                                                        ->options([
                                                            'percentage' => 'Percentage',
                                                            'flat' => 'Flat',
                                                            'none_refundable' => 'None Refundable',
                                                            'free' => 'Free',
                                                        ]),
                                                        Forms\Components\TextInput::make('percentage_rate')
                                                                ->numeric()
                                                                ->label('Percentage')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('charge_type') === 'percentage'),
                                                                
                                                            Forms\Components\TextInput::make('reduction')
                                                                ->numeric()
                                                                ->label('Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('charge_type') === 'flat'),
                                                                
                                                            Forms\Components\Select::make('currency')
                                                                ->label('Currency')
                                                                ->options(Currency::class)
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('charge_type') === 'flat'),

                                                    ])->columnSpan(1),
                                                        
                                                        section::make('Policy Conditions')
                                                        ->Schema([
                                                        Forms\Components\Select::make('types_organization')->label('Types organization of conditions (AND/OR)')                       
                                                            ->required()
                                                            ->options([
                                                                'and' => 'And',
                                                                'or' => 'OR',
                                                            
                                                            ]),


                                                            Forms\Components\Select::make('condition_type')->label('Condition Type')                       
                                                            ->required()
                                                            ->options([
                                                                'payment_type' => 'Payment Type',                                                            
                                                            ]),

                                                            Forms\Components\Select::make('comparison_type')->label('Comparison Type)')                       
                                                            ->required()
                                                            ->options([
                                                                'equal' => 'Equal',
                                                                'not_equal' => 'Not Equal',
                                                            
                                                            ]),

                                                            Forms\Components\Select::make('value')->label('Value')                       
                                                            ->required()
                                                            ->options([
                                                                'total_payment' => 'Total Payment',
                                                                'deposit' => 'Deposit',
                                                            
                                                            ]),
                                                        ])->columnSpan(1)
                                                        ])->columns(2),
                                                    
                                                    
                                                    // Add more fields if needed
                                                    ])
                                                    
                                                    ->action(function($record,$data,Get $get){
                                                        
                                                        $record->policies=[
                                                            
                                                            'period_start'=>$get('period_start'),
                                                            'period_end'=>$get('period_end'),
                                                            'show_policies_details'=>[
                                                                
                                                                
                                                                'types_policies'=>$data['types_policies'],
                                                                'days_before_activity'=>$data['days_before_activity'],
                                                                'hours_before_activity'=>$data['hours_before_activity'],
                                                                'charge_type' => $data['charge_type'],
                                                                'charge_type' => $data['charge_type'] === 'Percentage' ? $data['value_percentage'] : null,
                                                                'charge_type' => $data['charge_type'] === 'Flat'? [
                                                                    'flat_value' => $data['flat_value'],
                                                                    'currency' => $data['currency'],
                                                                ]: null,        
                                                                'types_organization'=>$data['types_organization'],
                                                                'condition_type'=>$data['condition_type'],
                                                                'comparison_type'=>$data['comparison_type'],
                                                                'value'=>$data['value'],
                                                                
                                                                
                                                                ]
                                                        ];
                                                        $record->save();
                                                    }),
    
                                                    ]),
                                                ])
    
    
                                                ]);
                                    }
                                    
                                        return $repeaterActivities;
                                    }),
                                    
                                Tabs\Tab::make('Supplement & special offers')
                                ->schema(function (Get $get) {
                                    $activities = $get('activities') ?? [];
                            
                                    if (!is_array($activities)) {
                                        return [];
                                    }
                            
                                    $repeaterActivities = [];
                            
                                    foreach ($activities as $activityId) {
                                        $activity = \App\Models\Activity::find($activityId);
                                        if (!$activity) continue;
                            
                                        $repeaterActivities[] = Section::make($activity->title)
                                            ->schema([
                                                Placeholder::make("supplemets_and_special_offers_info_{$activityId}")
                                                    ->label('Selected Activity')
                                                    ->content($activity->title)
                                                    ->extraAttributes(['class' => 'text-lg font-bold text-blue-700']),
                            
                                                TableRepeater::make("supplemets_and_special_offers_{$activityId}")


                                        ->columns(2)
                                        ->headers([
                                            Header::make('Special offer'),
                                            Header::make('Period Start'),
                                            Header::make('Period End'),
                                            Header::make('Show Periods'),
                                            
                                            Header::make('Actions'),
                                        ])
                                        ->schema([
                                            TextInput::make('special_offer'),
                                            DatePicker::make('period_start')->label('Period Start'),
                                            DatePicker::make('period_end')->label('Period End'),
                                                
                                            Actions::make([
                                            Action::make('show_offer_details')
                                            ->label('Show Offer Details')
                                            ->icon('heroicon-o-information-circle')
                                            ->modalHeading('Offer Details')
                                            
                                            ->modalCancelActionLabel('Close')
                                            
                                            ->form([
                                                section::make(null)
                                                ->Schema([
                                                
                                                
                                                    Repeater::make('offers')
                                                    ->schema([
                                                Forms\Components\Select::make('type_pax')->label('Type Pax')
                                                
                                                ->required()
                                                ->columnSpan(1)
                                                ->options([
                                                    'adult' => 'Adult',
                                                    'child' => 'Child',
                                                    'infant' => 'Infant',
                                                ]),
                                                Forms\Components\Select::make('level')->label('Level')
                                                ->required()
                                                ->columnSpan(1)
                                                ->options([
                                                    'all' => 'All',
                                                    'first' => 'First',
                                                    'second' => 'Second',
                                                    'third' => 'Third',
                                                    'fourth' => 'Fourth',
                                                ]),
                                                Forms\Components\Select::make('type_reduction')
                                                                ->label('Type Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->options([
                                                                    'flat' => 'Flat',
                                                                    'percentage' => '%',
                                                                ])
                                                                ->live(), // Add this to trigger reactivity
                                                                
                                                            Forms\Components\TextInput::make('percentage_rate')
                                                                ->numeric()
                                                                ->label('Percentage')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'percentage'),
                                                                
                                                            Forms\Components\TextInput::make('reduction')
                                                                ->numeric()
                                                                ->label('Reduction')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'flat'),
                                                                
                                                            Forms\Components\Select::make('currency')
                                                                ->label('Currency')
                                                                ->options(Currency::class)
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) => $get('type_reduction') === 'flat'),
                                                                
                                                                ])->columns(4),
                                                        Repeater::make('conditions')
                                                        ->schema([
                                                            Forms\Components\Select::make('condition_type')->label('Condition Type')                       
                                                            ->required()
                                                            ->columnSpan(3)
                                                            ->options([
                                                                'travel_date' => 'Travel Date',                                                            
                                                                'booking_date' => 'Booking Date',                                                            
                                                                'pick_up_time' => 'Pick up time',                                                            
                                                                'drop_off_time' => 'Drop off time ',                                                            
                                                            ]),

                                                            Forms\Components\Select::make('comparison_type')
                                                                ->label('Comparison Type')
                                                                ->required()
                                                                ->columnSpan(2)
                                                                ->options([
                                                                    'before' => 'Before',
                                                                    'after' => 'After',
                                                                    'equal' => 'Equal',
                                                                    'not_equal' => 'Not Equal',
                                                                    'between' => 'Between',
                                                                    'not_between' => 'Not Between',
                                                                ])
                                                                ->live(),
                                                                
                                                            TimePicker::make('value')
                                                                ->label('Value')
                                                                ->required()
                                                                ->columnSpan(1),
                                                                
                                                            TimePicker::make('second_value')
                                                                ->label('Second Value')
                                                                ->required()
                                                                ->columnSpan(1)
                                                                ->visible(fn (Get $get) =>
                                                                    in_array($get('comparison_type'), ['between', 'not_between'])
                                                                ),

                                                            
                                                            ])->columns(4)
                                                

                                            ]),
                                                
                                               
                                               
                                                
                                            
                                            
                                            // Add more fields if needed
                                            ])
                                            
                                            ->action(function($record, $data, Get $get) {
                                                $chargeType = $data['charge_type'] ?? null;
                                            
                                                $chargeDetails = match ($chargeType) {
                                                    'Percentage' => ['value_percentage' => $data['value_percentage']],
                                                    'Flat' => [
                                                        'flat_value' => $data['flat_value'],
                                                        'currency' => $data['currency'],
                                                    ],
                                                    default => $chargeType, // just the string if "None Refundable" or "Free"
                                                };
                                            
                                                $record->supplements_and_special_offers = [
                                                    'special_offer' => $get('special_offer'),
                                                    'period_start' => $get('period_start'),
                                                    'period_end' => $get('period_end'),
                                                    'offers' => [
                                                        'type_pax' => $data['type_pax'] ,
                                                        'level' => $data['level'] ,
                                                        'charge_type' => $chargeDetails,
                                                        'types_organization' => $data['types_organization'] ?? null,
                                                        'condition_type' => $data['condition_type'] ?? null,
                                                        'comparison_type' => $data['comparison_type'] ?? null,
                                                        'value' => $data['value'] ?? null,
                                                    ],
                                                ];
                                                
                                                $record->save();
                                            }),

                                            ]),
                                        ])


                                        ]);
                                    }
                                    
                                    return $repeaterActivities;
                                }),
                                Tabs\Tab::make('Channels')
                                    ->schema([
                                        Section::make("Channels contract")
                                            ->schema([
                                                Repeater::make('channels contract')
                                                    ->schema([
                                                        Forms\Components\Select::make('channels')
                                                            ->required()
                                                            ->options([
                                                                'test1' => 'test1',
                                                                'test2' => 'test2',
                                                            ])->columnSpan(1)
                                                            ->placeholder('Sélectionnez une option'),

                                                        Forms\Components\Select::make('commission')
                                                            ->required()
                                                            ->options([
                                                                'test1' => 'test1',
                                                                'test2' => 'test2',
                                                            ])->columnSpan(1)
                                                            ->placeholder('Sélectionnez une option'),
                                                    ])->columns(2),
                                            ])->columnSpanFull()

                                    ]),
                            ])
                    ])->columns(3)
                    ->columnSpan(3),


                // Second section: Radio Buttons and Date Picker (Top-Right Position)
                Section::make('Settings')
                    ->schema([
                        // Radio Button for contract status
                        ToggleButtons::make('contract_status')
                            ->label('Contract Status')
                            ->options(ContractStatus::class)
                            ->default('signed')

                            ->required(),

                        // Date Picker for contract date
                        DatePicker::make('signed_date')
                            ->label('Signed Date')

                            ->default(now())
                            ->displayFormat('Y-m-d'),

                        DatePicker::make('start_date')
                            ->label('Start Date')
                            ->required()
                            ->default(now())
                            ->displayFormat('Y-m-d')
                            ->minDate(fn (callable $get) => $get('signed_date')),


                        DatePicker::make('end_date')
                            ->label('End Date')
                            ->required()
                            ->default(now())
                            ->displayFormat('Y-m-d')
                            ->minDate(fn (callable $get) => $get('start_date')),

                    ])
                    ->grow(false) // Prevents it from growing, keeps it compact
                    ->columnSpan(1), // This section takes up 1 column
            ])->columns(4);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('contract_number'),

                Tables\Columns\TextColumn::make('description')->searchable(),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('signed_date'),
                Tables\Columns\TextColumn::make('start_date'),
                Tables\Columns\TextColumn::make('end_date'),
                Tables\Columns\TextColumn::make('contract_status')->badge(),


                Tables\Columns\TextColumn::make('markets.name')->badge(),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivityContracts::route('/'),
            'create' => Pages\CreateActivityContract::route('/create'),
            'edit' => Pages\EditActivityContract::route('/{record}/edit'),
        ];
    }
}