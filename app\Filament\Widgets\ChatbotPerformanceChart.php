<?php

namespace App\Filament\Widgets;

use App\Models\ChatMessage;
use App\Models\ChatSession;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class ChatbotPerformanceChart extends ChartWidget
{
    protected static ?string $heading = '🤖 Chatbot Performance & Intent Analysis';
    protected static ?int $sort = 8;
    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];
    protected static ?string $maxHeight = '400px';

    public ?string $filter = 'intents';

    protected function getFilters(): ?array
    {
        return [
            'intents' => 'Intent Distribution',
            'daily_usage' => 'Daily Usage',
            'success_rate' => 'Success Rate',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        switch ($activeFilter) {
            case 'daily_usage':
                return $this->getDailyUsageData();
            case 'success_rate':
                return $this->getSuccessRateData();
            default:
                return $this->getIntentData();
        }
    }

    private function getIntentData(): array
    {
        // Get intent distribution from chat messages
        $intentData = ChatMessage::where('sender', 'bot')
            ->whereNotNull('intent')
            ->select('intent', DB::raw('count(*) as count'))
            ->groupBy('intent')
            ->orderByDesc('count')
            ->get();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($intentData as $intent) {
            $labels[] = match($intent->intent) {
                'voir_hotel' => '🏨 Hotels',
                'voir_activité' => '🎯 Activities',
                'voir_transfert' => '🚗 Transfers',
                'voir_destination' => '🗺️ Destinations',
                'autre' => '❓ Other',
                default => ucfirst($intent->intent),
            };
            $data[] = $intent->count;
            $colors[] = match($intent->intent) {
                'voir_hotel' => '#3b82f6',
                'voir_activité' => '#10b981',
                'voir_transfert' => '#f59e0b',
                'voir_destination' => '#8b5cf6',
                'autre' => '#6b7280',
                default => '#ec4899',
            };
        }

        return [
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    private function getDailyUsageData(): array
    {
        $dailyMessages = ChatMessage::where('created_at', '>=', now()->subDays(30))
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('count(*) as total_messages'),
                DB::raw('sum(case when sender = "user" then 1 else 0 end) as user_messages'),
                DB::raw('sum(case when sender = "bot" then 1 else 0 end) as bot_messages')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $labels = $dailyMessages->pluck('date')->map(function($date) {
            return \Carbon\Carbon::parse($date)->format('M j');
        })->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'User Messages',
                    'data' => $dailyMessages->pluck('user_messages')->toArray(),
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Bot Responses',
                    'data' => $dailyMessages->pluck('bot_messages')->toArray(),
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    private function getSuccessRateData(): array
    {
        // Calculate success rate based on sessions with successful interactions
        $sessionStats = ChatSession::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('count(*) as total_sessions'),
            DB::raw('sum(case when (
                select count(*) from chat_messages
                where chat_session_id = chat_sessions.id
                and intent in ("voir_hotel", "voir_activité", "voir_transfert", "voir_destination")
            ) > 0 then 1 else 0 end) as successful_sessions')
        )
        ->where('created_at', '>=', now()->subDays(30))
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        $labels = $sessionStats->pluck('date')->map(function($date) {
            return \Carbon\Carbon::parse($date)->format('M j');
        })->toArray();

        $successRates = $sessionStats->map(function($stat) {
            return $stat->total_sessions > 0 ? 
                round(($stat->successful_sessions / $stat->total_sessions) * 100, 1) : 0;
        })->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Success Rate (%)',
                    'data' => $successRates,
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.2)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return match($this->filter) {
            'intents' => 'pie',
            'success_rate' => 'line',
            default => 'line',
        };
    }

    protected function getOptions(): array
    {
        if ($this->filter === 'intents') {
            return [
                'plugins' => [
                    'legend' => [
                        'display' => true,
                        'position' => 'right',
                    ],
                    'tooltip' => [
                        'callbacks' => [
                            'label' => 'function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ": " + context.parsed + " (" + percentage + "%)";
                            }'
                        ]
                    ],
                ],
                'maintainAspectRatio' => false,
                'responsive' => true,
            ];
        }

        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'max' => $this->filter === 'success_rate' ? 100 : null,
                    'ticks' => [
                        'callback' => $this->filter === 'success_rate' ? 
                            'function(value) { return value + "%"; }' : null,
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }

    public function getDescription(): ?string
    {
        $totalSessions = ChatSession::count();
        $totalMessages = ChatMessage::count();
        $avgMessagesPerSession = $totalSessions > 0 ? round($totalMessages / $totalSessions, 1) : 0;

        return "Total Sessions: {$totalSessions} | Total Messages: {$totalMessages} | Avg Messages/Session: {$avgMessagesPerSession}";
    }
}
