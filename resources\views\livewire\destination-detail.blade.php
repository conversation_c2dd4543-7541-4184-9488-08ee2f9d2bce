<div class="min-h-screen bg-gray-50">
    <!-- Hero Section with Destination Image -->
    <div class="relative h-96 overflow-hidden group">
        @if ($destination->image)
            <img src="{{ asset('storage/' . $destination->image) }}" alt="{{ $destination->name }}"
                class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105">
        @else
            <div
                class="w-full h-full bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center transition-all duration-500 group-hover:from-pink-500 group-hover:to-purple-600">
                <span class="text-white text-6xl animate-bounce">📍</span>
            </div>
        @endif

        <!-- Animated Overlay -->
        <div
            class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent transition-opacity duration-500 group-hover:from-black/40">
        </div>

        <!-- Back Button with Animation -->
        <div class="absolute top-6 left-6 animate-fade-in-down">
            <a href="{{ route('destinations') }}"
                class="inline-flex items-center px-4 py-2 bg-white/90 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-white hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300 group/btn">
                <svg class="w-4 h-4 mr-2 transition-transform duration-300 group-hover/btn:-translate-x-1"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back
            </a>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 right-10 w-4 h-4 bg-white/20 rounded-full animate-float"></div>
        <div class="absolute top-32 right-32 w-2 h-2 bg-white/30 rounded-full animate-float-delayed"></div>
        <div class="absolute bottom-20 left-20 w-3 h-3 bg-white/25 rounded-full animate-float-slow"></div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 -mt-20 relative z-10">
        <!-- Destination Info Card -->
        <div
            class="bg-white rounded-2xl shadow-lg p-6 mb-8 animate-slide-up hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2">
            <!-- Header -->
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-2 animate-fade-in">
                    Interested in visiting<br>
                    the <span class="text-pink-500 animate-pulse">{{ $destination->name }}</span>?
                </h1>

                <!-- AI Assistant Button -->
                <div class="flex items-center mt-4 animate-fade-in-delayed">
                    <img src="{{ asset('storage/avatar.jpg') }}" alt="AI Assistant"
                        class="w-10 h-10 rounded-full mr-3 animate-bounce-gentle">
                    <button
                        class="flex-1 bg-pink-50 border border-pink-200 rounded-full px-4 py-2 text-left text-gray-600 hover:bg-pink-100 hover:border-pink-300 hover:shadow-md transform hover:scale-105 transition-all duration-300 group">
                        <span class="group-hover:translate-x-1 transition-transform duration-300 inline-block">Let's
                            find your next trip</span>
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-3 gap-6 mb-6">
                <div class="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                    <div
                        class="text-2xl font-bold text-gray-900 group-hover:text-pink-500 transition-colors duration-300 animate-count-up">
                        {{ $hotels->count() }}</div>
                    <div class="text-sm text-gray-500 group-hover:text-gray-700 transition-colors duration-300">Hotels
                    </div>
                </div>
                <div class="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                    <div
                        class="text-2xl font-bold text-gray-900 group-hover:text-pink-500 transition-colors duration-300 animate-count-up-delayed">
                        {{ $activities->count() }}</div>
                    <div class="text-sm text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                        Activities</div>
                </div>
                <div class="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                    <div
                        class="text-2xl font-bold text-gray-900 group-hover:text-pink-500 transition-colors duration-300 animate-count-up-slow">
                        {{ rand(50, 200) }}</div>
                    <div class="text-sm text-gray-500 group-hover:text-gray-700 transition-colors duration-300">
                        Itineraries</div>
                </div>
            </div>
        </div>

        <!-- City Information -->
        <div
            class="bg-white rounded-2xl shadow-lg p-6 mb-8 animate-slide-up hover:shadow-xl transition-all duration-500 transform hover:-translate-y-1">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 animate-fade-in">City information</h2>

            <div class="grid grid-cols-3 gap-6 mb-6">
                <div class="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                    <div
                        class="text-2xl font-bold text-gray-900 group-hover:text-purple-500 transition-colors duration-300 animate-count-up">
                        {{ number_format(rand(100000, 5000000)) }}</div>
                    <div class="text-sm text-gray-500 group-hover:text-gray-700 transition-colors duration-300">Babies
                        born last year</div>
                </div>
                <div class="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                    <div
                        class="text-2xl font-bold text-gray-900 group-hover:text-blue-500 transition-colors duration-300 animate-count-up-delayed">
                        {{ rand(20, 150) }}</div>
                    <div class="text-sm text-gray-500 group-hover:text-gray-700 transition-colors duration-300">Beaches
                    </div>
                </div>
                <div class="text-center group cursor-pointer transform hover:scale-105 transition-all duration-300">
                    <div
                        class="text-2xl font-bold text-gray-900 group-hover:text-yellow-500 transition-colors duration-300 animate-count-up-slow">
                        {{ rand(10, 50) }}</div>
                    <div class="text-sm text-gray-500 group-hover:text-gray-700 transition-colors duration-300">Michelin
                        star</div>
                </div>
            </div>

            <div class="mb-6 animate-fade-in-delayed">
                <p class="text-gray-600 leading-relaxed">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit,
                    sed do eiusmod tempor incididunt ut labore et dolore
                    magna aliqua. Ut enim ad minim...
                    <button
                        class="text-pink-500 hover:text-pink-600 font-medium transition-colors duration-300 hover:underline">Read
                        more</button>
                </p>
            </div>

            <!-- Destination Image Gallery -->
            <div class="relative rounded-2xl overflow-hidden h-64 group cursor-pointer">
                @if ($destination->image)
                    <img src="{{ asset('storage/' . $destination->image) }}" alt="{{ $destination->name }}"
                        class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                @else
                    <div
                        class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center transition-all duration-500 group-hover:from-blue-500 group-hover:to-purple-600">
                        <span class="text-white text-4xl animate-bounce-gentle">🏖️</span>
                    </div>
                @endif

                <!-- Hover Overlay -->
                <div
                    class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>

                <!-- Image indicators -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <div
                        class="w-2 h-2 bg-white/50 rounded-full hover:bg-white transition-colors duration-300 cursor-pointer">
                    </div>
                    <div
                        class="w-2 h-2 bg-white/50 rounded-full hover:bg-white transition-colors duration-300 cursor-pointer">
                    </div>
                </div>
            </div>
        </div>

        <!-- Activities Section -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">Activities in {{ $destination->name }}</h2>
                <button class="text-pink-500 hover:text-pink-600 font-medium">Show all</button>
            </div>

            <!-- Activity Categories -->
            <div class="flex gap-4 mb-6 overflow-x-auto">
                <button
                    class="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-full text-sm font-medium whitespace-nowrap">
                    🎭 Culture
                </button>
                <button
                    class="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-full text-sm font-medium whitespace-nowrap">
                    🍽️ Gastronomy
                </button>
                <button
                    class="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-full text-sm font-medium whitespace-nowrap">
                    🪁 Kite
                </button>
            </div>

            <!-- Activities Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($activities as $index => $activity)
                    <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-xl hover:border-pink-200 transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 group animate-fade-in-up"
                        style="animation-delay: {{ $index * 0.1 }}s">
                        <div class="relative overflow-hidden">
                            @if ($activity->image)
                                <img src="{{ asset('storage/' . $activity->image) }}" alt="{{ $activity->title }}"
                                    class="w-full h-48 object-cover transition-transform duration-700 group-hover:scale-110">
                            @else
                                <div
                                    class="w-full h-48 bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center transition-all duration-500 group-hover:from-orange-500 group-hover:to-red-600">
                                    <span class="text-white text-3xl animate-bounce-gentle">🎯</span>
                                </div>
                            @endif

                            <!-- Rating Badge -->
                            <div
                                class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium transform transition-all duration-300 group-hover:scale-110 group-hover:bg-white">
                                ⭐ {{ number_format(rand(40, 50) / 10, 1) }}
                            </div>

                            <!-- Hover Overlay -->
                            <div
                                class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            </div>
                        </div>

                        <div class="p-4">
                            <h3
                                class="font-semibold text-gray-900 mb-2 group-hover:text-pink-600 transition-colors duration-300">
                                {{ $activity->title ?? 'Activity' }}</h3>
                            <div class="flex items-center text-sm text-gray-500">
                                <span
                                    class="flex items-center group-hover:text-gray-700 transition-colors duration-300">
                                    <span class="animate-star-twinkle">⭐⭐⭐⭐⭐</span>
                                    <span class="ml-1">{{ rand(100, 500) }}</span>
                                </span>
                            </div>
                        </div>
                    </div>
                @empty
                    <!-- Fallback activities -->
                    <div class="bg-white rounded-xl border border-gray-200 overflow-hidden">
                        <div
                            class="w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                            <span class="text-white text-3xl">🏛️</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">Historic Tour</h3>
                            <div class="flex items-center text-sm text-gray-500">
                                <span class="flex items-center">⭐⭐⭐⭐⭐ <span class="ml-1">245</span></span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl border border-gray-200 overflow-hidden">
                        <div
                            class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                            <span class="text-white text-3xl">🍽️</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">Food Experience</h3>
                            <div class="flex items-center text-sm text-gray-500">
                                <span class="flex items-center">⭐⭐⭐⭐⭐ <span class="ml-1">189</span></span>
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Hotels Section -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">Hotels in {{ $destination->name }}</h2>
                <button class="text-pink-500 hover:text-pink-600 font-medium">Show all</button>
            </div>

            <!-- Hotels Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($hotels as $index => $hotel)
                    <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-xl hover:border-blue-200 transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 group animate-fade-in-up cursor-pointer"
                        style="animation-delay: {{ $index * 0.15 }}s"
                        onclick="window.location.href='{{ route('hotels.show', $hotel->id) }}'">
                        <div class="relative overflow-hidden">
                            @if ($hotel->image)
                                <img src="{{ asset('storage/' . $hotel->image) }}" alt="{{ $hotel->name }}"
                                    class="w-full h-48 object-cover transition-transform duration-700 group-hover:scale-110">
                            @else
                                <div
                                    class="w-full h-48 bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center transition-all duration-500 group-hover:from-blue-500 group-hover:to-indigo-600">
                                    <span class="text-white text-3xl animate-bounce-gentle">🏨</span>
                                </div>
                            @endif

                            <!-- Rating Badge -->
                            <div
                                class="absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium transform transition-all duration-300 group-hover:scale-110 group-hover:bg-white">
                                ⭐ {{ number_format(rand(40, 50) / 10, 1) }}
                            </div>

                            <!-- Hover Overlay -->
                            <div
                                class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            </div>
                        </div>

                        <div class="p-4">
                            <h3
                                class="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                                {{ $hotel->name ?? 'Hotel' }}</h3>
                            <p
                                class="text-sm text-gray-600 mb-2 group-hover:text-gray-700 transition-colors duration-300">
                                {{ Str::limit($hotel->description ?? 'Luxury accommodation', 60) }}</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span
                                        class="flex items-center group-hover:text-gray-700 transition-colors duration-300">
                                        <span class="animate-star-twinkle">⭐⭐⭐⭐⭐</span>
                                        <span class="ml-1">{{ rand(100, 500) }}</span>
                                    </span>
                                </div>
                                <div class="text-right">
                                    <div
                                        class="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                                        ${{ rand(150, 500) }}</div>
                                    <div
                                        class="text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
                                        per night</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <!-- Fallback hotels -->
                    <div class="bg-white rounded-xl border border-gray-200 overflow-hidden">
                        <div
                            class="w-full h-48 bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center">
                            <span class="text-white text-3xl">🏨</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">Luxury Resort</h3>
                            <p class="text-sm text-gray-600 mb-2">Beautiful oceanfront resort with amazing amenities
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="flex items-center">⭐⭐⭐⭐⭐ <span class="ml-1">324</span></span>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-900">$299</div>
                                    <div class="text-xs text-gray-500">per night</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl border border-gray-200 overflow-hidden">
                        <div
                            class="w-full h-48 bg-gradient-to-br from-green-400 to-teal-500 flex items-center justify-center">
                            <span class="text-white text-3xl">🏨</span>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2">Boutique Hotel</h3>
                            <p class="text-sm text-gray-600 mb-2">Charming boutique hotel in the city center</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="flex items-center">⭐⭐⭐⭐⭐ <span class="ml-1">198</span></span>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-900">$189</div>
                                    <div class="text-xs text-gray-500">per night</div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
