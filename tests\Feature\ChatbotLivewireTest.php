<?php

namespace Tests\Feature;

use Livewire\Livewire;
use Tests\TestCase;
use App\Livewire\Chatbot;

class ChatbotLivewireTest extends TestCase
{
    public function test_chatbot_component_renders(): void
    {
        Livewire::test(Chatbot::class)
            ->assertStatus(200)
            ->assertSee('Bonjour ! Je suis votre assistant voyage intelligent');
    }

    public function test_chatbot_can_send_message(): void
    {
        Livewire::test(Chatbot::class)
            ->set('message', 'Bonjour')
            ->call('send')
            ->assertSet('message', '')
            ->assertSet('isTyping', false)
            ->assertCount('chat', 2); // Message utilisateur + réponse bot
    }

    public function test_chatbot_handles_hotel_query(): void
    {
        Livewire::test(Chatbot::class)
            ->set('message', 'Je cherche un hôtel à Paris')
            ->call('send')
            ->assertSet('message', '')
            ->assertSet('isTyping', false)
            ->assertCount('chat', 2);
    }

    public function test_typing_indicator_is_initially_false(): void
    {
        Livewire::test(Chatbot::class)
            ->assertSet('isTyping', false)
            ->assertDontSee('L\'assistant écrit');
    }
}
