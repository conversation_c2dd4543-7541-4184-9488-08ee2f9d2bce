<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
{
    Schema::table('hotel__contracts', function (Blueprint $table) {
        $table->json('supplements')->nullable();
        $table->json('special_offers')->nullable();
        $table->dropColumn('supplements_and_special_offers');
    });
}

public function down(): void
{
    Schema::table('hotel__contracts', function (Blueprint $table) {
        $table->dropColumn(['supplements', 'special_offers']);
        $table->json('supplements_and_special_offers')->nullable();
    });
}

};