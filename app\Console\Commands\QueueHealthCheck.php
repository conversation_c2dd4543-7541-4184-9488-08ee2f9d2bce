<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class QueueHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:health-check 
                            {--alert : Send alerts if issues detected}
                            {--fix : Attempt to fix detected issues}
                            {--threshold=100 : Alert threshold for pending jobs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check queue health and detect issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Travel Platform Queue Health Check');
        $this->info('=====================================');
        $this->line('');

        $issues = [];
        $warnings = [];

        // Check 1: Pending jobs count
        $pendingJobs = DB::table('jobs')->count();
        $threshold = $this->option('threshold');
        
        if ($pendingJobs > $threshold) {
            $issues[] = "High number of pending jobs: {$pendingJobs} (threshold: {$threshold})";
        } elseif ($pendingJobs > ($threshold / 2)) {
            $warnings[] = "Moderate number of pending jobs: {$pendingJobs}";
        }

        // Check 2: Failed jobs
        $failedJobs = DB::table('failed_jobs')->count();
        if ($failedJobs > 0) {
            $issues[] = "Failed jobs detected: {$failedJobs}";
        }

        // Check 3: Old pending jobs (stuck jobs)
        $oldJobs = DB::table('jobs')
            ->where('created_at', '<', Carbon::now()->subHours(1))
            ->count();
        
        if ($oldJobs > 0) {
            $issues[] = "Old pending jobs detected: {$oldJobs} (older than 1 hour)";
        }

        // Check 4: Queue worker heartbeat
        $lastHeartbeat = Cache::get('queue_worker_heartbeat');
        if (!$lastHeartbeat || Carbon::parse($lastHeartbeat)->diffInMinutes() > 5) {
            $issues[] = "Queue worker heartbeat missing or old";
        }

        // Check 5: Database connectivity
        try {
            DB::connection()->getPdo();
            $this->info('✓ Database connection: OK');
        } catch (\Exception $e) {
            $issues[] = "Database connection failed: " . $e->getMessage();
        }

        // Display results
        $this->displayResults($issues, $warnings, $pendingJobs, $failedJobs);

        // Handle fixes if requested
        if ($this->option('fix') && !empty($issues)) {
            $this->attemptFixes($issues);
        }

        // Handle alerts if requested
        if ($this->option('alert') && !empty($issues)) {
            $this->sendAlerts($issues);
        }

        return empty($issues) ? 0 : 1;
    }

    /**
     * Display health check results
     */
    private function displayResults($issues, $warnings, $pendingJobs, $failedJobs)
    {
        // Summary
        $this->info("Queue Statistics:");
        $this->line("- Pending jobs: {$pendingJobs}");
        $this->line("- Failed jobs: {$failedJobs}");
        $this->line('');

        // Issues
        if (!empty($issues)) {
            $this->error('Issues Detected:');
            foreach ($issues as $issue) {
                $this->line("  ✗ {$issue}");
            }
            $this->line('');
        }

        // Warnings
        if (!empty($warnings)) {
            $this->warn('Warnings:');
            foreach ($warnings as $warning) {
                $this->line("  ⚠ {$warning}");
            }
            $this->line('');
        }

        // Overall status
        if (empty($issues) && empty($warnings)) {
            $this->info('✓ Queue health: GOOD');
        } elseif (empty($issues)) {
            $this->warn('⚠ Queue health: WARNING');
        } else {
            $this->error('✗ Queue health: CRITICAL');
        }
    }

    /**
     * Attempt to fix detected issues
     */
    private function attemptFixes($issues)
    {
        $this->info('Attempting to fix issues...');

        foreach ($issues as $issue) {
            if (str_contains($issue, 'Failed jobs detected')) {
                $this->info('Retrying failed jobs...');
                $this->call('queue:retry', ['id' => 'all']);
            }

            if (str_contains($issue, 'Old pending jobs')) {
                $this->info('Restarting queue workers...');
                $this->call('queue:restart');
            }

            if (str_contains($issue, 'Queue worker heartbeat')) {
                $this->info('Queue worker may need restart. Consider running: php artisan queue:manage restart');
            }
        }
    }

    /**
     * Send alerts for critical issues
     */
    private function sendAlerts($issues)
    {
        $this->info('Sending alerts...');
        
        // Log critical issues
        foreach ($issues as $issue) {
            \Log::critical("Queue Health Check Alert: {$issue}");
        }

        // Here you could integrate with:
        // - Email notifications
        // - Slack webhooks
        // - SMS alerts
        // - Monitoring services

        $this->info('Alerts sent to logs. Configure additional alert channels as needed.');
    }
}
