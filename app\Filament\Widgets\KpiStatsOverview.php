<?php

namespace App\Filament\Widgets;

use App\Models\User;
use App\Models\Booking;
use App\Models\Destination;
use App\Models\Hotel;
use App\Models\Activity;
use App\Models\Transfer;
use App\Models\ChatSession;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class KpiStatsOverview extends BaseWidget
{
    protected static ?int $sort = 0;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        // Calculate key metrics
        $totalRevenue = Booking::where('status', 'confirmed')->sum('total_amount');
        $totalBookings = Booking::count();
        $totalUsers = User::count();
        $totalDestinations = Destination::count();
        
        // Calculate growth rates (compared to previous month)
        $currentMonthRevenue = Booking::where('status', 'confirmed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');
            
        $previousMonthRevenue = Booking::where('status', 'confirmed')
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('total_amount');
            
        $revenueGrowth = $previousMonthRevenue > 0 ? 
            round((($currentMonthRevenue - $previousMonthRevenue) / $previousMonthRevenue) * 100, 1) : 0;

        // User growth
        $currentMonthUsers = User::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
            
        $previousMonthUsers = User::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();
            
        $userGrowth = $previousMonthUsers > 0 ? 
            round((($currentMonthUsers - $previousMonthUsers) / $previousMonthUsers) * 100, 1) : 0;

        // Booking conversion rate
        $totalSessions = ChatSession::count();
        $conversionRate = $totalSessions > 0 ? round(($totalBookings / $totalSessions) * 100, 1) : 0;

        // Average booking value
        $avgBookingValue = $totalBookings > 0 ? round($totalRevenue / $totalBookings, 2) : 0;

        // Active users (users with activity in last 30 days)
        $activeUsers = User::where(function($query) {
            $query->whereHas('bookings', function($q) {
                $q->where('created_at', '>=', now()->subDays(30));
            })->orWhereHas('chatSessions', function($q) {
                $q->where('created_at', '>=', now()->subDays(30));
            });
        })->count();

        // Content metrics
        $totalHotels = Hotel::count();
        $totalActivities = Activity::count();
        $totalTransfers = Transfer::count();

        return [
            Stat::make('💰 Total Revenue', '€' . number_format($totalRevenue, 2))
                ->description($revenueGrowth >= 0 ? "+{$revenueGrowth}% from last month" : "{$revenueGrowth}% from last month")
                ->descriptionIcon($revenueGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($revenueGrowth >= 0 ? 'success' : 'danger')
                ->chart([
                    $previousMonthRevenue,
                    $currentMonthRevenue,
                ]),

            Stat::make('📊 Total Bookings', number_format($totalBookings))
                ->description("Conversion rate: {$conversionRate}%")
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('info'),

            Stat::make('👥 Total Users', number_format($totalUsers))
                ->description($userGrowth >= 0 ? "+{$userGrowth}% new users this month" : "{$userGrowth}% this month")
                ->descriptionIcon($userGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($userGrowth >= 0 ? 'success' : 'warning'),

            Stat::make('🎯 Active Users (30d)', number_format($activeUsers))
                ->description('Users with recent activity')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('💳 Avg Booking Value', '€' . number_format($avgBookingValue, 2))
                ->description('Average revenue per booking')
                ->descriptionIcon('heroicon-m-currency-euro')
                ->color('warning'),

            Stat::make('🏨 Hotels', number_format($totalHotels))
                ->description('Total hotels available')
                ->descriptionIcon('heroicon-m-building-office-2')
                ->color('info'),

            Stat::make('🎯 Activities', number_format($totalActivities))
                ->description('Total activities available')
                ->descriptionIcon('heroicon-m-puzzle-piece')
                ->color('success'),

            Stat::make('🚗 Transfers', number_format($totalTransfers))
                ->description('Total transfer options')
                ->descriptionIcon('heroicon-m-truck')
                ->color('warning'),

            Stat::make('🗺️ Destinations', number_format($totalDestinations))
                ->description('Available destinations')
                ->descriptionIcon('heroicon-m-map-pin')
                ->color('info'),

            Stat::make('🤖 Chat Sessions', number_format($totalSessions))
                ->description('AI chatbot interactions')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('info'),
        ];
    }

    protected function getColumns(): int
    {
        return 4; // Display 4 stats per row
    }
}
