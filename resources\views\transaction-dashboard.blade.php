<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8" x-data="transactionDashboard()" x-init="loadTransactions()">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">💳 Transaction Dashboard</h1>
                <p class="text-gray-600">Monitor your Stripe payment transactions</p>
                
                <div class="flex gap-4 mt-4">
                    <button @click="loadTransactions()" 
                            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                        🔄 Refresh
                    </button>
                    <a href="/test-payment" 
                       class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
                        🧪 Test Payment
                    </a>
                    <a href="https://dashboard.stripe.com/test/payments" target="_blank"
                       class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors">
                        🚀 Stripe Dashboard
                    </a>
                </div>
            </div>

            <!-- Loading State -->
            <div x-show="loading" class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p class="mt-2 text-gray-600">Loading transactions...</p>
            </div>

            <!-- Statistics -->
            <div x-show="!loading && data" class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600" x-text="data?.total_bookings || 0"></div>
                    <div class="text-sm text-gray-600">Total Bookings</div>
                </div>
                <div class="bg-white rounded-lg shadow p-4 text-center">
                    <div class="text-2xl font-bold text-green-600" x-text="data?.confirmed || 0"></div>
                    <div class="text-sm text-gray-600">✅ Confirmed</div>
                </div>
                <div class="bg-white rounded-lg shadow p-4 text-center">
                    <div class="text-2xl font-bold text-yellow-600" x-text="data?.pending || 0"></div>
                    <div class="text-sm text-gray-600">⏳ Pending</div>
                </div>
                <div class="bg-white rounded-lg shadow p-4 text-center">
                    <div class="text-2xl font-bold text-green-600" x-text="data?.paid || 0"></div>
                    <div class="text-sm text-gray-600">💰 Paid</div>
                </div>
                <div class="bg-white rounded-lg shadow p-4 text-center">
                    <div class="text-2xl font-bold text-red-600" x-text="data?.failed || 0"></div>
                    <div class="text-sm text-gray-600">❌ Failed</div>
                </div>
            </div>

            <!-- Transactions Table -->
            <div x-show="!loading && data" class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800">Recent Transactions</h2>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="booking in data?.recent_bookings || []" :key="booking.id">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="booking.id"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="booking.user"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="booking.activity"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="booking.booking_date"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                              :class="getStatusClass(booking.status)" x-text="booking.status"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                              :class="getPaymentStatusClass(booking.payment_status)" x-text="booking.payment_status || 'N/A'"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="booking.total_price"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="booking.created_at"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Error State -->
            <div x-show="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
                <div class="flex">
                    <div class="text-red-400">❌</div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Error loading transactions</h3>
                        <p class="mt-1 text-sm text-red-700" x-text="error"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function transactionDashboard() {
            return {
                data: null,
                loading: false,
                error: null,

                async loadTransactions() {
                    this.loading = true;
                    this.error = null;
                    
                    try {
                        const response = await fetch('/check-transactions');
                        if (!response.ok) {
                            throw new Error('Failed to load transactions');
                        }
                        this.data = await response.json();
                    } catch (err) {
                        this.error = err.message;
                    } finally {
                        this.loading = false;
                    }
                },

                getStatusClass(status) {
                    switch(status) {
                        case 'confirmed': return 'bg-green-100 text-green-800';
                        case 'pending': return 'bg-yellow-100 text-yellow-800';
                        case 'cancelled': return 'bg-red-100 text-red-800';
                        case 'payment_failed': return 'bg-red-100 text-red-800';
                        default: return 'bg-gray-100 text-gray-800';
                    }
                },

                getPaymentStatusClass(status) {
                    switch(status) {
                        case 'paid': return 'bg-green-100 text-green-800';
                        case 'pending': return 'bg-yellow-100 text-yellow-800';
                        case 'failed': return 'bg-red-100 text-red-800';
                        case 'refunded': return 'bg-blue-100 text-blue-800';
                        default: return 'bg-gray-100 text-gray-800';
                    }
                }
            }
        }
    </script>
</body>
</html>
