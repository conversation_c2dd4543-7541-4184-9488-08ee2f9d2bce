<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            🗺️ Destinations Map
         <?php $__env->endSlot(); ?>

         <?php $__env->slot('headerEnd', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal505efd9768415fdb4543e8c564dad437 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal505efd9768415fdb4543e8c564dad437 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.input.wrapper','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::input.wrapper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <?php if (isset($component)) { $__componentOriginal97dc683fe4ff7acce9e296503563dd85 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal97dc683fe4ff7acce9e296503563dd85 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.input.select','data' => ['wire:model.live' => 'filter']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::input.select'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model.live' => 'filter']); ?>
                    <option value="revenue">Filter by Revenue</option>
                    <option value="bookings">Filter by Bookings</option>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal97dc683fe4ff7acce9e296503563dd85)): ?>
<?php $attributes = $__attributesOriginal97dc683fe4ff7acce9e296503563dd85; ?>
<?php unset($__attributesOriginal97dc683fe4ff7acce9e296503563dd85); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal97dc683fe4ff7acce9e296503563dd85)): ?>
<?php $component = $__componentOriginal97dc683fe4ff7acce9e296503563dd85; ?>
<?php unset($__componentOriginal97dc683fe4ff7acce9e296503563dd85); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal505efd9768415fdb4543e8c564dad437)): ?>
<?php $attributes = $__attributesOriginal505efd9768415fdb4543e8c564dad437; ?>
<?php unset($__attributesOriginal505efd9768415fdb4543e8c564dad437); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal505efd9768415fdb4543e8c564dad437)): ?>
<?php $component = $__componentOriginal505efd9768415fdb4543e8c564dad437; ?>
<?php unset($__componentOriginal505efd9768415fdb4543e8c564dad437); ?>
<?php endif; ?>
         <?php $__env->endSlot(); ?>

        <div class="space-y-4">
            <!-- Map Container with better styling -->
            <div class="relative">
                <div id="destinations-map" class="w-full h-96 rounded-lg border border-gray-200 bg-gray-50"></div>
                <div id="map-loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto mb-2"></div>
                        <p class="text-sm text-gray-600">Loading map...</p>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">High
                            <?php echo e($filter === 'revenue' ? 'Revenue' : 'Bookings'); ?></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Medium
                            <?php echo e($filter === 'revenue' ? 'Revenue' : 'Bookings'); ?></span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 bg-red-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Low
                            <?php echo e($filter === 'revenue' ? 'Revenue' : 'Bookings'); ?></span>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Total Destinations: <?php echo e(count($destinations)); ?>

                </div>
            </div>

            <!-- Destinations Stats Table -->
            <div class="overflow-hidden bg-white border border-gray-200 rounded-lg">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900">Destination Performance</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Destination</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Revenue</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Bookings</th>
                                <th
                                    class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Avg. Value</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = collect($destinations)->sortByDesc($filter === 'revenue' ? 'revenue' : 'bookings')->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 w-8 h-8">
                                                <!--[if BLOCK]><![endif]--><?php if($destination['image']): ?>
                                                    <img class="w-8 h-8 rounded-full object-cover"
                                                        src="<?php echo e(asset('storage/' . $destination['image'])); ?>"
                                                        alt="<?php echo e($destination['name']); ?>">
                                                <?php else: ?>
                                                    <div
                                                        class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                        <span
                                                            class="text-xs font-medium text-gray-600"><?php echo e(substr($destination['name'], 0, 2)); ?></span>
                                                    </div>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo e($destination['name']); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo e($destination['country']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            €<?php echo e(number_format($destination['revenue'], 2)); ?></div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo e($destination['bookings']); ?></div>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            €<?php echo e($destination['bookings'] > 0 ? number_format($destination['revenue'] / $destination['bookings'], 2) : '0.00'); ?>

                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>

    <!-- Include Leaflet CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <style>
        /* Fix Leaflet CSS issues in admin panels */
        #destinations-map {
            height: 384px !important;
            /* h-96 = 24rem = 384px */
            width: 100% !important;
            z-index: 1 !important;
        }

        .leaflet-container {
            height: 384px !important;
            width: 100% !important;
            background: #f9fafb !important;
        }

        .leaflet-tile {
            filter: none !important;
        }

        .leaflet-control-zoom {
            z-index: 1000 !important;
        }

        .leaflet-popup {
            z-index: 1001 !important;
        }

        #map-loading {
            z-index: 1002 !important;
        }
    </style>

    <script>
        let destinationsMap = null;
        let mapInitialized = false;

        function hideMapLoading() {
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) {
                loadingEl.style.display = 'none';
            }
        }

        function showMapError(message) {
            const mapContainer = document.getElementById('destinations-map');
            const loadingEl = document.getElementById('map-loading');
            if (loadingEl) loadingEl.style.display = 'none';
            if (mapContainer) {
                mapContainer.innerHTML = `<div class="flex items-center justify-center h-full text-center p-4">
                    <div>
                        <div class="text-red-500 mb-2">⚠️</div>
                        <p class="text-sm text-gray-600">${message}</p>
                        <button onclick="location.reload()" class="mt-2 px-3 py-1 bg-pink-600 text-white text-xs rounded hover:bg-pink-700">
                            Refresh Page
                        </button>
                    </div>
                </div>`;
            }
        }

        function initializeDestinationsMap() {
            console.log('🗺️ Starting simple map initialization...');

            // Basic checks
            if (typeof L === 'undefined') {
                console.error('❌ Leaflet not loaded');
                showMapError('Map library not loaded. Please refresh the page.');
                return;
            }

            const mapContainer = document.getElementById('destinations-map');
            if (!mapContainer) {
                console.error('❌ Map container not found');
                showMapError('Map container not found. Please refresh the page.');
                return;
            }

            console.log('✅ Basic checks passed, creating map...');

            try {
                // Remove existing map
                if (destinationsMap) {
                    destinationsMap.remove();
                    destinationsMap = null;
                }

                // Create map immediately
                destinationsMap = L.map('destinations-map').setView([48.8566, 2.3522], 5);

                console.log('✅ Map instance created');

                // Add tiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(destinationsMap);

                console.log('✅ Tiles added');

                // Add markers immediately
                addSimpleMarkers();

                // Hide loading
                hideMapLoading();
                mapInitialized = true;

                console.log('🎉 Map initialization complete!');

            } catch (error) {
                console.error('❌ Map creation failed:', error);
                showMapError('Failed to create map: ' + error.message);
            }
        }

        function addSimpleMarkers() {
            const destinations = <?php echo json_encode($destinations, 15, 512) ?>;
            const filter = <?php echo json_encode($filter, 15, 512) ?>;

            console.log('📍 Adding markers for', destinations.length, 'destinations');

            if (!destinations || destinations.length === 0) {
                console.warn('⚠️ No destinations data');
                return;
            }

            destinations.forEach((dest, index) => {
                // Use default coordinates if missing
                let lat = 48.8566; // Paris default
                let lng = 2.3522;

                if (dest.coordinates && dest.coordinates.lat && dest.coordinates.lng) {
                    lat = dest.coordinates.lat;
                    lng = dest.coordinates.lng;
                } else {
                    // Spread destinations around Europe if no coordinates
                    lat = 45 + (index % 10) * 2;
                    lng = 0 + (index % 15) * 3;
                }

                const value = filter === 'revenue' ? dest.revenue : dest.bookings;
                const color = value > 1000 ? '#10b981' : value > 100 ? '#f59e0b' : '#ef4444';

                const marker = L.circleMarker([lat, lng], {
                    radius: Math.max(6, Math.min(20, value / 100)),
                    fillColor: color,
                    color: '#ffffff',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(destinationsMap);

                marker.bindPopup(`
                    <div class="p-2">
                        <h3 class="font-bold">${dest.name}</h3>
                        <p>Revenue: €${dest.revenue.toFixed(2)}</p>
                        <p>Bookings: ${dest.bookings}</p>
                    </div>
                `);
            });

            console.log('✅ Markers added successfully');
        }

        // Simple initialization
        console.log('🚀 Setting up map initialization...');

        function startMapInit() {
            console.log('⏰ Starting map initialization timer...');
            setTimeout(() => {
                if (!mapInitialized) {
                    initializeDestinationsMap();
                }
            }, 2000);
        }

        // Try multiple ways to initialize
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startMapInit);
        } else {
            startMapInit();
        }

        // Also try when window loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (!mapInitialized) {
                    console.log('🔄 Retry from window load...');
                    initializeDestinationsMap();
                }
            }, 1000);
        });

        // Handle filter changes
        document.addEventListener('livewire:updated', () => {
            console.log('🔄 Livewire updated, reinitializing map...');
            setTimeout(initializeDestinationsMap, 500);
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\travel-platform\resources\views/filament/widgets/destinations-map.blade.php ENDPATH**/ ?>