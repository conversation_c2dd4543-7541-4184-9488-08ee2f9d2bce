<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Enums\ContractStatus;


class TransferContract extends Model
{
    use HasFactory;

    protected $fillable = [
        'contract_number',
        'type',
        'direct_supplier',
        'main_supplier',
        'intermediate_supplier',

        'type_of_service',
        'markets',
        'description',
        'whats_included',
        'whats_not_included',
        'contract_status',
        'signed_date',
        'start_date',
        'end_date',
        'price',
    ];

    protected $casts = [
        'direct_supplier' => 'boolean',
        'whats_included' => 'array',
        'whats_not_included' => 'array',
        'price' => 'array',
        'contract_status' => ContractStatus::class

    ];


    public function transfers()
    {
        return $this->belongsToMany(Transfer::class);
    }

    public function destinations()
    {
        return $this->belongsToMany(Destination::class, 'destination_transfer_contracts');
    }

    public function meetingPoints()
    {
        return $this->belongsToMany(MeetingPoint::class, 'meeting_point_transfer_contracts');
    }

    public function markets()
    {
        return $this->belongsToMany(Markets::class, 'transfer_contract_market');
    }
}