<div class="min-h-screen bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">My Trips</h1>
            <p class="text-gray-600">Manage your booked activities and upcoming adventures</p>
        </div>

        <!-- Success/Error Messages -->
        @if (session()->has('success'))
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {{ session('success') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {{ session('error') }}
            </div>
        @endif

        @if ($bookings->count() > 0)
            <!-- Confirmed Bookings Section -->
            @if ($confirmedBookings->count() > 0)
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <h2 class="text-xl font-semibold text-gray-800">Confirmed Bookings</h2>
                        </div>
                        <span class="ml-3 bg-green-100 text-green-800 text-sm font-medium px-2 py-1 rounded-full">
                            {{ $confirmedBookings->count() }}
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach ($confirmedBookings as $booking)
                            @include('livewire.partials.booking-card', ['booking' => $booking, 'type' => 'confirmed'])
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Pending Bookings Section -->
            @if ($pendingBookings->count() > 0)
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                            <h2 class="text-xl font-semibold text-gray-800">Pending Bookings</h2>
                        </div>
                        <span class="ml-3 bg-yellow-100 text-yellow-800 text-sm font-medium px-2 py-1 rounded-full">
                            {{ $pendingBookings->count() }}
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach ($pendingBookings as $booking)
                            @include('livewire.partials.booking-card', ['booking' => $booking, 'type' => 'pending'])
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Cancelled Bookings Section -->
            @if ($cancelledBookings->count() > 0)
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                            <h2 class="text-xl font-semibold text-gray-800">Cancelled Bookings</h2>
                        </div>
                        <span class="ml-3 bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded-full">
                            {{ $cancelledBookings->count() }}
                        </span>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach ($cancelledBookings as $booking)
                            @include('livewire.partials.booking-card', ['booking' => $booking, 'type' => 'cancelled'])
                        @endforeach
                    </div>
                </div>
            @endif
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                        </path>
                    </svg>
                </div>
                <h3 class="text-xl font-medium text-gray-900 mb-2">No trips yet</h3>
                <p class="text-gray-500 mb-6">Start planning your next adventure by browsing our activities.</p>
                <a href="{{ route('home') }}"
                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-medium rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200">
                    Browse Activities
                </a>
            </div>
        @endif
    </div>
</div>
