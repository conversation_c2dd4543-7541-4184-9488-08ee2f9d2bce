<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotels', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('Type')->nullable();
            $table->string('chaine')->nullable();
            $table->string('gimmonix')->nullable();
            $table->string('giata')->nullable();
            $table->text('short_description')->nullable();
            $table->string('image')->nullable();
            $table->unsignedTinyInteger('rating')->default(0); // Rating column with a max of 5
            $table->unsignedTinyInteger('sustainability_score');
            $table->boolean('sustainability_certification')->default(false);  
            $table->text('description')->nullable();
            $table->enum('status', ['draft', 'published', 'archived']);
            $table->timestamps();
        });
    }
    


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotels');
    }
};