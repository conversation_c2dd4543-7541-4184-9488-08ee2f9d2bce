<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Booking extends Model
{
    protected $fillable = [
        'user_id',
        'activity_id',
        'booking_date',
        'booking_time',
        'adults',
        'children',
        'total_price',
        'status',
        'payment_intent_id',
        'payment_status',
        'stripe_payment_method_id'
    ];

    protected $casts = [
        'booking_date' => 'date',
        'total_price' => 'decimal:2'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }
}
