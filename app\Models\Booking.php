<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Booking extends Model
{
    protected $fillable = [
        'user_id',
        'bookable_type',
        'bookable_id',
        'booking_date',
        'booking_time',
        'adults',
        'children',
        'total_amount',
        'status',
        'payment_intent_id',
        'payment_status',
        'stripe_payment_method_id'
    ];

    protected $casts = [
        'booking_date' => 'date',
        'total_amount' => 'decimal:2'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bookable model (Activity, Hotel, or Transfer)
     */
    public function bookable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Legacy relationship for backward compatibility
     * @deprecated Use bookable() instead
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class, 'bookable_id')
            ->where('bookable_type', Activity::class);
    }

    /**
     * Get hotel relationship when bookable is a hotel
     */
    public function hotel(): BelongsTo
    {
        return $this->belongsTo(Hotel::class, 'bookable_id')
            ->where('bookable_type', Hotel::class);
    }

    /**
     * Get transfer relationship when bookable is a transfer
     */
    public function transfer(): BelongsTo
    {
        return $this->belongsTo(Transfer::class, 'bookable_id')
            ->where('bookable_type', Transfer::class);
    }

    /**
     * Scope to filter bookings by bookable type
     */
    public function scopeForType($query, string $type)
    {
        return $query->where('bookable_type', $type);
    }

    /**
     * Get the total price (alias for total_amount for backward compatibility)
     */
    public function getTotalPriceAttribute()
    {
        return $this->total_amount;
    }
}
