<?php

use App\Models\Activity;
use App\Models\ActivityContract;
use App\Models\MeetingPoint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_points', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('pays')->nullable();
            $table->timestamps();
        });
        Schema::create('activity_meeting_point', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Activity::class);
            $table->foreignIdFor(MeetingPoint::class);
            $table->timestamps();
        });
        Schema::create('activity_contract_meeting_point', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(ActivityContract::class);
            $table->foreignIdFor(MeetingPoint::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_points');
        Schema::dropIfExists('activity_meeting_point');
    }
};