@startuml Travel Platform - Use Case Diagram
!theme plain

' Configuration du style pour correspondre à l'image
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #FFFFFF
    BorderColor #000000
    FontColor #000000
}
skinparam usecase {
    BackgroundColor #87CEEB
    BorderColor #4682B4
    FontColor #000000
}
skinparam arrow {
    Color #000000
}

title Travel Platform - Use Case Diagram

' Acteurs principaux
:Utilisateur Invité: as Guest
:Utilisateur Enregistré: as User
:Administrateur: as Admin
:Syst<PERSON> Chatbot: as Chatbot

' Héritage entre acteurs
Guest <|-- User

' Cas d'usage principaux pour Utilisateur Invité
(Consulter page d'accueil) as UC1
(Explorer destinations) as UC2
(Rechercher hôtels) as UC3
(Rechercher activités) as UC4
(Rechercher transferts) as UC5
(Utiliser chatbot) as UC7
(Contacter support) as UC8
(S'inscrire) as UC9
(Se connecter) as UC10

' Cas d'usage avec extensions pour l'authentification
(Connexion Google OAuth) as UC11
(Récupérer mot de passe) as UC12

' Cas d'usage pour Utilisateur Enregistré
(Gérer profil) as UC13
(Ajouter aux favoris) as UC16
(Consulter favoris) as UC18
(Consulter mes voyages) as UC20

' Extensions du profil
(Modifier informations) as UC14
(Changer mot de passe) as UC15

' Extensions des favoris
(Supprimer des favoris) as UC17
(Organiser favoris) as UC19

' Cas d'usage pour Administrateur
(Gérer hôtels) as UC22
(Gérer activités) as UC23
(Gérer transferts) as UC24
(Gérer destinations) as UC25
(Gérer utilisateurs) as UC27
(Gérer support) as UC29
(Consulter statistiques) as UC30

' Extensions admin
(Gérer contrats) as UC26
(Gérer permissions) as UC28
(Configurer chatbot) as UC31

' Cas d'usage pour Système Chatbot
(Analyser messages) as UC32
(Détecter intentions) as UC33
(Générer réponses) as UC35

' Relations Utilisateur Invité
Guest --> UC1
Guest --> UC2
Guest --> UC3
Guest --> UC4
Guest --> UC5
Guest --> UC7
Guest --> UC8
Guest --> UC9
Guest --> UC10

' Relations Utilisateur Enregistré (spécifiques)
User --> UC13
User --> UC16
User --> UC18
User --> UC20

' Relations Administrateur
Admin --> UC22
Admin --> UC23
Admin --> UC24
Admin --> UC25
Admin --> UC27
Admin --> UC29
Admin --> UC30

' Relations Système Chatbot
Chatbot --> UC32
Chatbot --> UC33
Chatbot --> UC35

' Relations Include (lignes pointillées)
UC10 ..> UC11 : <<include>>
UC10 ..> UC12 : <<include>>
UC13 ..> UC14 : <<include>>
UC13 ..> UC15 : <<include>>
UC7 ..> UC32 : <<include>>
UC7 ..> UC33 : <<include>>
UC16 ..> UC10 : <<include>>

' Relations Extend (lignes pointillées)
UC11 ..> UC10 : <<extend>>
UC12 ..> UC10 : <<extend>>
UC14 ..> UC13 : <<extend>>
UC15 ..> UC13 : <<extend>>
UC17 ..> UC18 : <<extend>>
UC19 ..> UC18 : <<extend>>
UC26 ..> UC22 : <<extend>>
UC26 ..> UC23 : <<extend>>
UC26 ..> UC24 : <<extend>>
UC28 ..> UC27 : <<extend>>
UC31 ..> UC29 : <<extend>>

@enduml
