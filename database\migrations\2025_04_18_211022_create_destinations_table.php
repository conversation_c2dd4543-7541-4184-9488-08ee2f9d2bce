<?php

use App\Models\Destination;
use App\Models\TransferContract;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('destinations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('country')->nullable();
            $table->string('continent')->nullable();
            $table->string('zone')->nullable();
            
            $table->timestamps();
        });

        Schema::create('destination_transfer_contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(TransferContract::class);
            $table->foreignIdFor(Destination::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('destinations');
        Schema::dropIfExists('destination_transfer_contract');
    }
};