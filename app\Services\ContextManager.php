<?php

namespace App\Services;

use App\Models\ChatSession;
use Illuminate\Support\Facades\Log;

class ContextManager
{
    public function updateContext(ChatSession $session, array $intentData): void
    {
        try {
            $currentContext = $session->context_data ?? [];
            
            // Mettre à jour le contexte avec les nouvelles informations
            $updatedContext = $this->mergeContextData($currentContext, $intentData);
            
            // Nettoyer le contexte (garder seulement les données récentes)
            $cleanedContext = $this->cleanOldContext($updatedContext);
            
            // Sauvegarder le contexte mis à jour
            $session->update(['context_data' => $cleanedContext]);
            
        } catch (\Exception $e) {
            Log::error('Context update failed', [
                'session_id' => $session->session_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getContextForIntent(ChatSession $session, string $intent): array
    {
        $context = $session->context_data ?? [];
        
        // Retourner les données contextuelles pertinentes pour l'intention
        return [
            'current_intent' => $intent,
            'previous_intent' => $context['last_intent'] ?? null,
            'persistent_entities' => $this->getPersistentEntities($context),
            'conversation_flow' => $this->getConversationFlow($context),
        ];
    }

    private function mergeContextData(array $currentContext, array $intentData): array
    {
        $timestamp = now()->timestamp;
        
        // Initialiser la structure si nécessaire
        if (!isset($currentContext['intents'])) {
            $currentContext['intents'] = [];
        }
        if (!isset($currentContext['entities'])) {
            $currentContext['entities'] = [];
        }
        
        // Ajouter la nouvelle intention
        $currentContext['intents'][] = [
            'intent' => $intentData['intent'],
            'confidence' => $intentData['confidence'],
            'timestamp' => $timestamp
        ];
        
        // Fusionner les entités
        foreach ($intentData['entities'] as $key => $value) {
            $currentContext['entities'][$key] = [
                'value' => $value,
                'timestamp' => $timestamp,
                'intent' => $intentData['intent']
            ];
        }
        
        // Mettre à jour les métadonnées
        $currentContext['last_intent'] = $intentData['intent'];
        $currentContext['last_update'] = $timestamp;
        
        return $currentContext;
    }

    private function cleanOldContext(array $context): array
    {
        $oneHourAgo = now()->subHour()->timestamp;
        
        // Nettoyer les intentions anciennes
        if (isset($context['intents'])) {
            $context['intents'] = array_filter($context['intents'], function ($intent) use ($oneHourAgo) {
                return $intent['timestamp'] > $oneHourAgo;
            });
        }
        
        // Nettoyer les entités anciennes
        if (isset($context['entities'])) {
            $context['entities'] = array_filter($context['entities'], function ($entity) use ($oneHourAgo) {
                return $entity['timestamp'] > $oneHourAgo;
            });
        }
        
        return $context;
    }

    private function getPersistentEntities(array $context): array
    {
        $persistent = [];
        
        if (!isset($context['entities'])) {
            return $persistent;
        }
        
        // Entités qui persistent dans la conversation
        $persistentKeys = ['location', 'prix'];
        
        foreach ($persistentKeys as $key) {
            if (isset($context['entities'][$key])) {
                $persistent[$key] = $context['entities'][$key]['value'];
            }
        }
        
        return $persistent;
    }

    private function getConversationFlow(array $context): array
    {
        $flow = [];
        
        if (!isset($context['intents'])) {
            return $flow;
        }
        
        // Analyser le flux de conversation
        $recentIntents = array_slice($context['intents'], -5); // 5 dernières intentions
        
        foreach ($recentIntents as $intentData) {
            $flow[] = $intentData['intent'];
        }
        
        return [
            'sequence' => $flow,
            'pattern' => $this->detectConversationPattern($flow),
        ];
    }

    private function detectConversationPattern(array $intentSequence): string
    {
        if (empty($intentSequence)) {
            return 'new_conversation';
        }
        
        $lastIntent = end($intentSequence);
        $uniqueIntents = array_unique($intentSequence);
        
        // Patterns de conversation
        if (count($uniqueIntents) === 1) {
            return 'focused_search'; // L'utilisateur se concentre sur un type de service
        }
        
        if (in_array('voir_hotel', $intentSequence) && 
            in_array('voir_activité', $intentSequence)) {
            return 'trip_planning'; // L'utilisateur planifie un voyage complet
        }
        
        if (count($intentSequence) > 3 && $lastIntent === 'autre') {
            return 'clarification_needed'; // L'utilisateur a besoin d'aide
        }
        
        return 'exploration'; // L'utilisateur explore les options
    }

    public function shouldAskForClarification(ChatSession $session, array $intentData): bool
    {
        $context = $session->context_data ?? [];
        
        // Demander clarification si :
        // 1. Confiance faible
        if ($intentData['confidence'] < 0.7) {
            return true;
        }
        
        // 2. Entités manquantes pour l'intention
        if ($this->hasMissingCriticalEntities($intentData)) {
            return true;
        }
        
        // 3. Intention ambiguë dans le contexte
        if ($this->isIntentAmbiguous($context, $intentData)) {
            return true;
        }
        
        return false;
    }

    private function hasMissingCriticalEntities(array $intentData): bool
    {
        $intent = $intentData['intent'];
        $entities = $intentData['entities'];
        
        // Définir les entités critiques par intention
        $criticalEntities = [
            'voir_hotel' => ['location'],
            'voir_activité' => ['location'],
            'voir_transfert' => ['location'],
        ];
        
        if (!isset($criticalEntities[$intent])) {
            return false;
        }
        
        foreach ($criticalEntities[$intent] as $required) {
            if (empty($entities[$required])) {
                return true;
            }
        }
        
        return false;
    }

    private function isIntentAmbiguous(array $context, array $intentData): bool
    {
        // Vérifier si l'intention est cohérente avec le contexte récent
        if (!isset($context['intents']) || empty($context['intents'])) {
            return false;
        }
        
        $recentIntents = array_slice($context['intents'], -3);
        $currentIntent = $intentData['intent'];
        
        // Si l'intention change drastiquement sans transition logique
        foreach ($recentIntents as $recent) {
            if ($recent['intent'] !== $currentIntent && 
                $recent['intent'] !== 'autre' && 
                $currentIntent !== 'autre') {
                // Changement d'intention sans entités communes
                return true;
            }
        }
        
        return false;
    }
}
