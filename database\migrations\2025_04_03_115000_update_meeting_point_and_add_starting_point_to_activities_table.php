<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateMeetingPointAndAddStartingPointToActivitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('activities', function (Blueprint $table) {
            // Modify 'meeting_point' column to be a string
            $table->string('meeting_point')->default('')->change();
            
            // Add new 'starting_point' column
            $table->string('starting_point')->nullable(); // Set nullable() if the field is optional
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
     {
        Schema::table('activities', function (Blueprint $table) {
            // Revert 'meeting_point' column back to boolean
            $table->boolean('meeting_point')->default(false)->change();
            
            // Drop the 'starting_point' column
            $table->dropColumn('starting_point');
        });
    }
}
