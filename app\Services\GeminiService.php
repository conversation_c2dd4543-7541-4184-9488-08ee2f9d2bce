<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiService
{
    private $apiKey;
    private $modelName = "gemini-1.5-flash";

    public function __construct()
    {
        $this->apiKey = config('services.gemini.key');
    }

    public function detectIntentAndEntities(string $message, array $contextHistory = []): array
    {
        $prompt = $this->buildIntentDetectionPrompt($message, $contextHistory);

        $response = $this->callGeminiAPI($prompt, [
            'temperature' => 0.1,
            'maxOutputTokens' => 500
        ]);

        return $this->parseIntentResponse($response);
    }

    public function generateResponse(string $userMessage, array $intentData, array $databaseResults, array $contextHistory): array
    {
        $prompt = $this->buildResponsePrompt($userMessage, $intentData, $databaseResults, $contextHistory);

        $response = $this->callGeminiAPI($prompt, [
            'temperature' => 0.7,
            'maxOutputTokens' => 800
        ]);

        $cleanResponse = $this->cleanResponse($response);

        // Déterminer si on doit afficher des cartes de destinations
        $shouldShowCards = $this->shouldShowDestinationCards($intentData, $databaseResults);

        return [
            'text' => $cleanResponse,
            'show_destination_cards' => $shouldShowCards,
            'destinations' => $shouldShowCards ? $databaseResults : []
        ];
    }

    private function buildIntentDetectionPrompt(string $message, array $contextHistory): string
    {
        $context = $this->formatContextHistory($contextHistory);

        return "Tu es un assistant IA spécialisé dans la détection d'intentions pour un site de réservation touristique.

CONTEXTE DE LA CONVERSATION:
{$context}

INTENTIONS POSSIBLES:
- voir_hotel: L'utilisateur cherche des hôtels
- voir_activité: L'utilisateur cherche des activités
- voir_transfert: L'utilisateur cherche des transferts
- voir_destination: L'utilisateur cherche des destinations de voyage, veut explorer ou découvrir des lieux
- autre: Autre type de demande

ENTITÉS À EXTRAIRE:
- nom_hotel: Nom spécifique d'un hôtel
- location: Ville, pays, région (ex: Paris, Marrakech, Tunisie)
- prix: Plage de prix ou budget (ex: \"moins de 100€\", \"entre 50 et 80€\")
- nom_activité: Nom spécifique d'une activité
- type_activité: Type d'activité (ex: randonnée, spa, musée, plongée)
- transfert_name: Nom spécifique d'un transfert
- vehicule_type: Type de véhicule (ex: voiture, minivan, bus)
- nom_destination: Nom de la destination recherchée (ex: \"Paris\", \"Rome\", \"Lisbonne\")

MESSAGE UTILISATEUR: \"{$message}\"

INSTRUCTIONS:
1. Analyse le message en tenant compte du contexte
2. Détecte l'intention principale
3. Extrait toutes les entités pertinentes
4. Corrige les fautes d'orthographe courantes (ex: \"otel\" → \"hôtel\")

RÉPONSE ATTENDUE (format JSON strict):
{
    \"intent\": \"intention_détectée\",
    \"entities\": {
        \"nom_entité\": \"valeur_extraite\"
    },
    \"confidence\": 0.95
}";
    }

    private function buildResponsePrompt(string $userMessage, array $intentData, array $databaseResults, array $contextHistory): string
    {
        $context = $this->formatContextHistory($contextHistory);
        $dataFormatted = $this->formatDatabaseResults($databaseResults, $intentData['intent']);

        return "Tu es un assistant IA spécialisé dans les réservations touristiques. Tu dois répondre de manière naturelle et personnalisée.

CONTEXTE DE LA CONVERSATION:
{$context}

MESSAGE UTILISATEUR: \"{$userMessage}\"

INTENTION DÉTECTÉE: {$intentData['intent']}
ENTITÉS EXTRAITES: " . json_encode($intentData['entities'], JSON_UNESCAPED_UNICODE) . "

DONNÉES TROUVÉES:
{$dataFormatted}

INSTRUCTIONS:
1. Réponds de manière naturelle et conversationnelle
2. Utilise les données trouvées pour donner des informations précises
3. Si aucune donnée n'est trouvée, propose des alternatives
4. Reste dans le contexte du voyage et du tourisme
5. Sois concis mais informatif
6. Utilise un ton amical et professionnel
7. Si l'utilisateur demande des clarifications, pose des questions pertinentes
8. Pour les destinations :
   - Si c'est une destination spécifique (DESTINATION SÉLECTIONNÉE), présente brièvement la destination puis propose UNIQUEMENT de choisir entre hôtels ou activités
   - Si c'est une liste, propose des suggestions basées sur les préférences (climat, budget, activités)
9. Croise les informations avec l'historique de conversation pour des recommandations personnalisées
10. IMPORTANT: Quand une destination est sélectionnée, ne donne PAS d'informations détaillées, propose seulement hôtels OU activités
11. Utilise un ton direct et commercial pour guider l'utilisateur vers la réservation

RÉPONSE:";
    }

    private function formatContextHistory(array $contextHistory): string
    {
        if (empty($contextHistory)) {
            return "Début de conversation";
        }

        $formatted = [];
        foreach (array_slice($contextHistory, -5) as $msg) {
            $sender = $msg['sender'] === 'user' ? 'Utilisateur' : 'Assistant';
            $formatted[] = "{$sender}: {$msg['text']}";
        }

        return implode("\n", $formatted);
    }

    private function formatDatabaseResults(array $results, string $intent): string
    {
        if (empty($results)) {
            return "Aucune donnée trouvée dans la base de données.";
        }

        $formatted = [];

        switch ($intent) {
            case 'voir_hotel':
                foreach ($results as $hotel) {
                    $formatted[] = "- {$hotel['name']} ({$hotel['city']}) - {$hotel['rating']} étoiles";
                }
                break;

            case 'voir_activité':
                foreach ($results as $activity) {
                    $formatted[] = "- {$activity['title']} ({$activity['city']}) - {$activity['activity_type']}";
                }
                break;

            case 'voir_transfert':
                foreach ($results as $transfer) {
                    $formatted[] = "- {$transfer['name']} - {$transfer['vehicle_type']} (capacité: {$transfer['max_capacity']})";
                }
                break;

            case 'voir_destination':
                foreach ($results as $destination) {
                    if (count($results) === 1) {
                        // Destination spécifique - proposer des options
                        $formatted[] = "DESTINATION SÉLECTIONNÉE: {$destination['name']} ({$destination['country']})";
                        $formatted[] = "Description: {$destination['description']}";
                        $formatted[] = "INSTRUCTION SPÉCIALE: L'utilisateur a sélectionné cette destination. Propose-lui maintenant de choisir entre :";
                        $formatted[] = "1. Voir les hôtels disponibles à {$destination['name']}";
                        $formatted[] = "2. Découvrir les activités à {$destination['name']}";
                        $formatted[] = "Sois bref et direct dans ta proposition.";
                    } else {
                        // Liste de destinations
                        $description = isset($destination['description']) ? ' - ' . substr($destination['description'], 0, 100) . '...' : '';
                        $formatted[] = "- {$destination['name']} ({$destination['country']}){$description}";
                    }
                }
                break;
        }

        return implode("\n", $formatted);
    }

    private function callGeminiAPI(string $prompt, array $config = []): string
    {
        $defaultConfig = [
            'temperature' => 0.7,
            'maxOutputTokens' => 800
        ];

        $config = array_merge($defaultConfig, $config);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post("https://generativelanguage.googleapis.com/v1/models/{$this->modelName}:generateContent?key={$this->apiKey}", [
            'contents' => [
                [
                    'role' => 'user',
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => $config
        ]);

        if (!$response->successful()) {
            Log::error('Gemini API failed', ['response' => $response->body()]);
            throw new \Exception('Erreur API Gemini');
        }

        $responseData = $response->json();
        return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? '';
    }

    private function parseIntentResponse(string $response): array
    {
        // Nettoyer la réponse pour extraire le JSON
        $response = trim($response);
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);

        $decoded = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::warning('Failed to parse intent response', ['response' => $response]);
            return [
                'intent' => 'autre',
                'entities' => [],
                'confidence' => 0.0
            ];
        }

        return [
            'intent' => $decoded['intent'] ?? 'autre',
            'entities' => $decoded['entities'] ?? [],
            'confidence' => $decoded['confidence'] ?? 0.0
        ];
    }

    private function cleanResponse(string $response): string
    {
        // Nettoyer la réponse de Gemini
        $response = trim($response);
        $response = preg_replace('/^\*\*.*?\*\*\s*/', '', $response); // Supprimer les titres en gras
        return $response;
    }

    private function shouldShowDestinationCards(array $intentData, array $databaseResults): bool
    {
        // Afficher les cartes si :
        // 1. L'intention est voir_destination
        // 2. Il y a des résultats de destinations
        // 3. Il y a plus d'une destination (pour permettre le choix)
        // 4. Ce n'est pas une sélection spécifique (pas d'ID de destination)

        return $intentData['intent'] === 'voir_destination'
            && !empty($databaseResults)
            && count($databaseResults) > 1
            && isset($databaseResults[0]['type'])
            && $databaseResults[0]['type'] === 'destination'
            && empty($intentData['entities']['destination_id']); // Ne pas afficher si ID spécifique
    }
}
