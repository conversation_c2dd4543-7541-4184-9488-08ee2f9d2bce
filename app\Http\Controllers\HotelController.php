<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Hotel;
use App\Models\Room;

class HotelController extends Controller
{
    public function show($id)
    {
        $hotel = Hotel::findOrFail($id);

        // Get active rooms for this hotel through hotel contracts
        $activeRooms = collect();

        if ($hotel->hotelcontracts) {
            $activeRooms = Room::where('hotel_contract_id', $hotel->hotelcontracts->id)
                ->where('status', 'active')
                ->get()
                ->map(function ($room) {
                    // Extract price and availability from allocation data
                    $allocation = $room->allocation;
                    $price = null;
                    $availability = null;

                    if (is_array($allocation) && !empty($allocation)) {
                        // Get the first allocation entry for price and release
                        $firstAllocation = $allocation[0] ?? null;
                        if ($firstAllocation) {
                            $price = $firstAllocation['price'] ?? null;
                            $availability = $firstAllocation['release'] ?? null;
                        }
                    }

                    return [
                        'id' => $room->id,
                        'name' => $room->name,
                        'price' => $price,
                        'availability' => $availability,
                        'status' => $room->status
                    ];
                });
        }

        return view('hotels.show', compact('hotel', 'activeRooms'));
    }

    public function getSeasonalPricing(Request $request)
    {
        $roomId = $request->input('room_id');
        $checkIn = $request->input('check_in');
        $checkOut = $request->input('check_out');

        // Find the room
        $room = Room::findOrFail($roomId);

        // Get allocation data
        $allocation = $room->allocation;

        if (!is_array($allocation) || empty($allocation)) {
            return response()->json([
                'success' => false,
                'message' => 'No pricing data available for this room.'
            ]);
        }

        // Find the season that matches the check-in date
        $matchingSeason = null;
        foreach ($allocation as $season) {
            $seasonStart = $season['start_date_allocation'] ?? null;
            $seasonEnd = $season['end_date_allocation'] ?? null;

            if ($seasonStart && $seasonEnd) {
                // Check if check-in date falls within this season
                if ($checkIn >= $seasonStart && $checkIn <= $seasonEnd) {
                    $matchingSeason = $season;
                    break;
                }
            }
        }

        if (!$matchingSeason) {
            return response()->json([
                'success' => false,
                'message' => 'No pricing available for the selected dates.'
            ]);
        }

        // Calculate number of nights
        $checkInDate = new \DateTime($checkIn);
        $checkOutDate = new \DateTime($checkOut);
        $nights = $checkInDate->diff($checkOutDate)->days;

        // Calculate total price
        $pricePerNight = $matchingSeason['price'] ?? 0;
        $totalPrice = $pricePerNight * $nights;

        return response()->json([
            'success' => true,
            'data' => [
                'season' => $matchingSeason['season'] ?? 'Unknown Season',
                'price_per_night' => $pricePerNight,
                'formatted_price_per_night' => formatPrice($pricePerNight),
                'nights' => $nights,
                'total_price' => $totalPrice,
                'formatted_total_price' => formatPrice($totalPrice),
                'availability' => $matchingSeason['release'] ?? 0,
                'allocation_type' => $matchingSeason['allocation_type'] ?? 'Unknown',
                'season_start' => $matchingSeason['start_date_allocation'] ?? null,
                'season_end' => $matchingSeason['end_date_allocation'] ?? null,
            ]
        ]);
    }
}
