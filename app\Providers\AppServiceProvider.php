<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON>zhanSalleh\PanelSwitch\PanelSwitch;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        PanelSwitch::configureUsing(function (PanelSwitch $panelSwitch) {
            $panelSwitch
            ->modalWidth('sm')
            ->icons([
                'admin' => 'heroicon-o-building-office',
                'contracts' => 'heroicon-o-clipboard-document-check' ,
            ], $asImage = false)
            ->iconSize(24);
        });
    }
}