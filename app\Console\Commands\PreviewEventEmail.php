<?php

namespace App\Console\Commands;

use App\Mail\EventCreatedNotification;
use App\Models\Event;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class PreviewEventEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:preview-event 
                            {--event= : Event ID to preview}
                            {--user= : User ID to preview for}
                            {--send= : Email address to send preview to}
                            {--save : Save HTML to file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Preview the event notification email template';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get event
        $eventId = $this->option('event');
        if ($eventId) {
            $event = Event::find($eventId);
            if (!$event) {
                $this->error("Event with ID {$eventId} not found.");
                return 1;
            }
        } else {
            $event = Event::with('destination')->latest()->first();
            if (!$event) {
                $this->error('No events found. Please create an event first.');
                return 1;
            }
        }

        // Get user
        $userId = $this->option('user');
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return 1;
            }
        } else {
            $user = User::first();
            if (!$user) {
                $this->error('No users found.');
                return 1;
            }
        }

        $this->info("Previewing email for:");
        $this->line("Event: {$event->name}");
        $this->line("Destination: {$event->destination->name}");
        $this->line("User: {$user->name} ({$user->email})");
        $this->line('');

        // Create the mailable
        $mailable = new EventCreatedNotification($event, $user);

        // Handle different preview options
        if ($sendTo = $this->option('send')) {
            $this->sendPreview($mailable, $sendTo);
        } elseif ($this->option('save')) {
            $this->savePreview($mailable, $event, $user);
        } else {
            $this->displayPreview($mailable);
        }

        return 0;
    }

    /**
     * Send preview email
     */
    private function sendPreview($mailable, $email)
    {
        try {
            Mail::to($email)->send($mailable);
            $this->info("Preview email sent to: {$email}");
        } catch (\Exception $e) {
            $this->error("Failed to send preview email: " . $e->getMessage());
        }
    }

    /**
     * Save preview to HTML file
     */
    private function savePreview($mailable, $event, $user)
    {
        try {
            $html = $mailable->render();
            $filename = 'email-preview-' . $event->id . '-' . date('Y-m-d-H-i-s') . '.html';
            $filepath = storage_path('app/email-previews/' . $filename);
            
            // Create directory if it doesn't exist
            if (!is_dir(dirname($filepath))) {
                mkdir(dirname($filepath), 0755, true);
            }
            
            file_put_contents($filepath, $html);
            
            $this->info("Email preview saved to: {$filepath}");
            $this->info("Open this file in your browser to view the email.");
            
        } catch (\Exception $e) {
            $this->error("Failed to save preview: " . $e->getMessage());
        }
    }

    /**
     * Display preview information
     */
    private function displayPreview($mailable)
    {
        $this->info('Email Preview Information:');
        $this->line('==========================');
        
        // Get envelope
        $envelope = $mailable->envelope();
        $this->info("Subject: {$envelope->subject}");
        $this->info("From: {$envelope->from[0]->address}");
        
        if (!empty($envelope->tags)) {
            $this->info("Tags: " . implode(', ', $envelope->tags));
        }
        
        $this->line('');
        $this->info('To view the full email:');
        $this->line('1. Use --save option to save HTML file');
        $this->line('2. Use --send=<EMAIL> to send preview');
        $this->line('');
        
        $this->info('Example commands:');
        $this->line("php artisan email:preview-event --save");
        $this->line("php artisan email:preview-event --send=<EMAIL>");
    }
}
