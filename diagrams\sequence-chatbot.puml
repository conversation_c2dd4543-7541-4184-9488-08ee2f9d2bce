@startuml Travel Platform - Chatbot Sequence Diagram
!theme plain
skinparam backgroundColor #FFFFFF
skinparam participant {
    BackgroundColor #E8F5E8
    BorderColor #4CAF50
    FontColor #1A1A1A
}
skinparam actor {
    BackgroundColor #E91E63
    BorderColor #C2185B
    FontColor #FFFFFF
}

title Travel Platform - Diagramme de Séquence Chatbot

actor User as U
participant "Livewire\nChatbot" as LC
participant "ChatbotService" as CS
participant "IntentDetectionService" as IDS
participant "GeminiService" as GS
participant "ContextManager" as CM
participant "DatabaseQueryService" as DQS
participant "Database" as DB

== Initialisation de la conversation ==
U -> LC: Envoie message "Je cherche un hôtel à Paris"
activate LC

LC -> CS: processMessage(message, sessionId)
activate CS

CS -> CM: getContext(sessionId)
activate CM
CM -> CM: Vérifier session active
CM --> CS: context[]
deactivate CM

== Détection d'intention ==
CS -> IDS: detectIntent(message)
activate IDS

IDS -> IDS: correctSpelling(message)
IDS -> GS: generateContent(prompt, temperature=0.1)
activate GS
GS -> GS: Appel API Gemini
GS --> IDS: intention + entités
deactivate GS

IDS -> IDS: calculateConfidence(result)
IDS -> IDS: validateEntities(entities, intent)
IDS --> CS: {intent: "voir_hotel", entities: {location: "Paris"}, confidence: 0.9}
deactivate IDS

== Recherche en base de données ==
CS -> DQS: searchHotels({location: "Paris"})
activate DQS

DQS -> DB: SELECT * FROM hotels WHERE location LIKE '%Paris%'
activate DB
DB --> DQS: Collection<Hotel>
deactivate DB

DQS --> CS: hotels[]
deactivate DQS

== Génération de réponse ==
CS -> GS: generateResponse(context + results)
activate GS
GS -> GS: Formater réponse avec résultats
GS --> CS: response_text
deactivate GS

== Mise à jour du contexte ==
CS -> CM: updateContext(sessionId, {intent, entities, results})
activate CM
CM -> CM: Ajouter au contexte
CM -> DB: Sauvegarder session
CM --> CS: success
deactivate CM

CS --> LC: {response: text, results: hotels[], intent: "voir_hotel"}
deactivate CS

== Affichage de la réponse ==
LC -> LC: Afficher typing indicator
LC -> LC: Formater réponse + cartes hôtels
LC --> U: Réponse + liste d'hôtels
deactivate LC

== Interaction utilisateur avec les résultats ==
U -> LC: Clique sur "♥" d'un hôtel
activate LC

LC -> LC: toggleFavorite(hotelId)
LC -> DB: INSERT/DELETE favorite
LC --> U: Animation cœur + feedback
deactivate LC

== Conversation continue ==
U -> LC: "Montre-moi des activités aussi"
activate LC

LC -> CS: processMessage(message, sessionId)
activate CS

CS -> CM: getContext(sessionId)
activate CM
CM --> CS: context[] (avec historique Paris)
deactivate CM

CS -> IDS: detectIntent(message, context)
activate IDS
IDS --> CS: {intent: "voir_activité", entities: {location: "Paris"}}
deactivate IDS

CS -> DQS: searchActivities({location: "Paris"})
activate DQS
DQS -> DB: SELECT activities...
DQS --> CS: activities[]
deactivate DQS

CS -> GS: generateResponse(context + new_results)
activate GS
GS --> CS: contextual_response
deactivate GS

CS -> CM: updateContext(sessionId, new_data)
activate CM
CM --> CS: success
deactivate CM

CS --> LC: {response: text, results: activities[]}
deactivate CS

LC --> U: Réponse contextuelle + activités Paris
deactivate LC

@enduml
