@startuml Delete Activity Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor <PERSON><PERSON> as A
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over A, M : Delete Activity Operation - MVC Architecture

A -> V : Navigate to admin panel
V -> C : GET /admin/activities
C -> M : Get all activities
M -> M : Query database for activities
M -> C : Return activities data
C -> V : Return activities list view
V -> A : Display activities management page

A -> V : Click "Delete" on specific activity
V -> C : Show delete confirmation dialog
V -> A : Display confirmation with\nactivity details and warnings

A -> V : Click "Confirm Delete"
V -> C : DELETE /admin/activities/{id}
C -> M : Find activity by ID
M -> M : Query database for activity

alt Activity found
    M -> C : Return activity object
    C -> M : Check for dependencies
    M -> M : Query for contracts,\nbookings, favorites
    
    alt No active contracts
        M -> C : Check for bookings
        M -> M : Query for existing bookings
        
        alt No existing bookings
            M -> C : Check for favorites
            M -> M : Query for favorites relationships
            
            alt Safe to delete
                M -> C : Dependencies checked - safe to delete
                C -> M : Delete favorites relationships
                M -> M : Remove favorites from database
                
                C -> C : Delete activity images
                C -> M : Delete activity record
                M -> M : Remove activity from database
                
                alt Deletion successful
                    M -> C : Deletion confirmed
                    C -> C : Log deletion action
                    C -> V : Return success response
                    V -> A : Display "Activity deleted successfully"
                else Database error
                    M -> C : Deletion failed
                    C -> V : Return error response
                    V -> A : Display "Failed to delete activity"
                end
                
            else Favorites exist
                M -> C : Favorites found - proceed with cleanup
                note over C : Continue with deletion process
            end
            
        else Bookings exist
            M -> C : Active bookings found
            C -> V : Return error response
            V -> A : Display "Cannot delete activity\nwith existing bookings"
        end
        
    else Active contracts exist
        M -> C : Active contracts found
        C -> V : Return error response
        V -> A : Display "Cannot delete activity\nwith active contracts"
    end
    
else Activity not found
    M -> C : Activity not found
    C -> V : Return 404 error response
    V -> A : Display "Activity not found"
end

note over A, M : Activity deletion with comprehensive dependency checking

@enduml
