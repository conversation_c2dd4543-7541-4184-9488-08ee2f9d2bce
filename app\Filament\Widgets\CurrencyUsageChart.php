<?php

namespace App\Filament\Widgets;

use App\Models\Booking;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class CurrencyUsageChart extends ChartWidget
{
    protected static ?string $heading = '💱 Currency Usage Analytics';
    protected static ?int $sort = 9;
    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        // Get currency usage from bookings (assuming you store currency in bookings)
        // For now, we'll simulate data based on your supported currencies
        $currencies = ['EUR', 'USD', 'TND', 'GBP', 'JPY'];
        
        // Simulate currency distribution (you can replace this with actual data)
        $currencyData = [
            'EUR' => Booking::count() * 0.45, // 45% EUR
            'USD' => Booking::count() * 0.25, // 25% USD
            'TND' => Booking::count() * 0.15, // 15% TND
            'GBP' => Booking::count() * 0.10, // 10% GBP
            'JPY' => Booking::count() * 0.05, // 5% JPY
        ];

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($currencyData as $currency => $count) {
            if ($count > 0) {
                $labels[] = match($currency) {
                    'EUR' => '🇪🇺 Euro (EUR)',
                    'USD' => '🇺🇸 US Dollar (USD)',
                    'TND' => '🇹🇳 Tunisian Dinar (TND)',
                    'GBP' => '🇬🇧 British Pound (GBP)',
                    'JPY' => '🇯🇵 Japanese Yen (JPY)',
                    default => $currency,
                };
                $data[] = round($count);
                $colors[] = match($currency) {
                    'EUR' => '#3b82f6',
                    'USD' => '#10b981',
                    'TND' => '#f59e0b',
                    'GBP' => '#8b5cf6',
                    'JPY' => '#ef4444',
                    default => '#6b7280',
                };
            }
        }

        return [
            'datasets' => [
                [
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => '#ffffff',
                    'borderWidth' => 2,
                    'hoverBorderWidth' => 3,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                    'labels' => [
                        'usePointStyle' => true,
                        'padding' => 15,
                        'font' => [
                            'size' => 11,
                        ],
                    ],
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ": " + value + " bookings (" + percentage + "%)";
                        }'
                    ]
                ],
            ],
            'cutout' => '50%',
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }

    public function getDescription(): ?string
    {
        $totalBookings = Booking::count();
        $mostPopularCurrency = 'EUR'; // You can calculate this dynamically
        
        return "Total Bookings: {$totalBookings} | Most Popular: {$mostPopularCurrency}";
    }
}
