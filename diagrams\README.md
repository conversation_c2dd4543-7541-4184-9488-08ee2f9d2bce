# 📊 Travel Platform - Diagrammes UML

Ce dossier contient tous les diagrammes UML du projet Travel Platform, générés en PlantUML.

## 📁 Fichiers disponibles

### 🎯 **use-case-diagram.puml**
- **Description** : Diagramme de cas d'usage complet
- **Acteurs** : Utilisateur Invité, Utilisateur Enregistré, Administrateur, Système Chatbot
- **Fonctionnalités** : Navigation, Authentification, Gestion favoris, Administration, IA

### 🏗️ **class-diagram.puml**
- **Description** : Diagramme de classes détaillé
- **Packages** : User Management, Hotel Management, Activity Management, Transfer Management, Destination Management, Favorites System, Support System, Chatbot System, Market Management
- **Relations** : Associations, Héritage, Polymorphisme

### ⚙️ **services-diagram.puml**
- **Description** : Architecture des services métier
- **Services** : ChatbotService, IntentDetectionService, ContextManager, DatabaseQueryService, GeminiService
- **Patterns** : Repository Pattern, Service Layer, Dependency Injection

### 🔄 **sequence-chatbot.puml**
- **Description** : Diagramme de séquence pour l'interaction chatbot
- **Flux** : Message utilisateur → Détection intention → Recherche BDD → Génération réponse → Affichage
- **Acteurs** : User, Livewire Chatbot, Services, Database

## 🛠️ Comment visualiser les diagrammes

### **Option 1 : PlantUML Online**
1. Allez sur [PlantUML Online Server](http://www.plantuml.com/plantuml/uml/)
2. Copiez le contenu d'un fichier `.puml`
3. Cliquez sur "Submit" pour générer l'image

### **Option 2 : VS Code Extension**
1. Installez l'extension "PlantUML" dans VS Code
2. Ouvrez un fichier `.puml`
3. Utilisez `Ctrl+Shift+P` → "PlantUML: Preview Current Diagram"

### **Option 3 : PlantUML Local**
```bash
# Installation avec Java
java -jar plantuml.jar diagrams/use-case-diagram.puml

# Génération PNG
java -jar plantuml.jar -tpng diagrams/*.puml

# Génération SVG
java -jar plantuml.jar -tsvg diagrams/*.puml
```

### **Option 4 : Docker**
```bash
# Générer tous les diagrammes
docker run --rm -v $(pwd)/diagrams:/data plantuml/plantuml:latest -tpng /data/*.puml
```

## 🎨 Personnalisation des couleurs

Les diagrammes utilisent la palette de couleurs Travel Platform :
- **Rose principal** : #E91E63 (boutons, acteurs)
- **Rose clair** : #F8BBD9 (arrière-plans)
- **Bleu** : #0277BD (services)
- **Vert** : #388E3C (packages)

## 📋 Légende des relations

### **Diagramme de Classes :**
- `||--o{` : One-to-Many (1:N)
- `}o--o{` : Many-to-Many (N:N)
- `}o--||` : Many-to-One (N:1)
- `..|>` : Implements (Interface)
- `<|--` : Extends (Héritage)

### **Diagramme de Cas d'Usage :**
- `-->` : Association
- `<<include>>` : Inclusion obligatoire
- `<<extend>>` : Extension conditionnelle
- `<|--` : Généralisation (héritage)

## 🔄 Mise à jour des diagrammes

Pour maintenir les diagrammes à jour :

1. **Après ajout de nouvelles fonctionnalités** : Mettre à jour le diagramme de cas d'usage
2. **Après modification des modèles** : Mettre à jour le diagramme de classes
3. **Après refactoring des services** : Mettre à jour le diagramme de services
4. **Après modification du chatbot** : Mettre à jour le diagramme de séquence

## 📊 Statistiques du projet

### **Cas d'usage identifiés :** 36
- Utilisateur Invité : 12 cas d'usage
- Utilisateur Enregistré : +9 cas d'usage spécifiques
- Administrateur : 10 cas d'usage
- Système Chatbot : 5 cas d'usage

### **Classes principales :** 15
- Modèles métier : 10 classes
- Services : 5 classes principales
- Relations : 20+ associations

### **Services architecturaux :** 8
- Services chatbot : 4 services
- Services externes : 2 services
- Repositories : 4 repositories
- Services métier : 3 services

## 🎯 Utilisation recommandée

1. **Pour les développeurs** : Consultez le diagramme de classes avant de modifier les modèles
2. **Pour les chefs de projet** : Utilisez le diagramme de cas d'usage pour les spécifications
3. **Pour l'architecture** : Référez-vous au diagramme de services pour les dépendances
4. **Pour les tests** : Le diagramme de séquence aide à comprendre les flux complexes

## 📝 Notes importantes

- Les diagrammes sont synchronisés avec le code source actuel
- Toute modification majeure doit être reflétée dans les diagrammes
- Les couleurs respectent la charte graphique Travel Platform
- Les diagrammes sont optimisés pour l'impression et la présentation

---

**Généré automatiquement pour Travel Platform** 🌟
*Dernière mise à jour : Décembre 2024*
