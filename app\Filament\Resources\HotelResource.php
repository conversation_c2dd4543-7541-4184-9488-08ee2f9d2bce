<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HotelResource\Pages;
use App\Models\Hotel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Mokhosh\FilamentRating\Components\Rating;
use Mokhosh\FilamentRating\Columns\RatingColumn;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use App\Filament\Forms\Components\LeafletMap;
use App\Models\Room;
use Carbon\Carbon;
use Filament\Forms\Components\Radio;
use Filament\Tables\Columns\ViewColumn;
use Rupadana\FilamentSlider\Components\InputSlider;
use Rupadana\FilamentSlider\Components\InputSliderGroup;

use Mansoor\UnsplashPicker\Actions\UnsplashPickerAction;

use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Closure;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use PhpParser\Node\Stmt\Label;

class HotelResource extends Resource
{
    protected static ?string $model = Hotel::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->columnSpan('full')
                    ->tabs([
                        Tabs\Tab::make('Hotel')
                        ->columns(3)
                            ->schema([
                                TextInput::make('name')->required()->columnSpan(2),
                                TextInput::make('slug')->required()->columnSpan(1),
                                TextInput::make('Type')->columnSpan(1),
                                TextInput::make('chaine')->label('Hotel Chaine'),
                                TextInput::make('gimmonix'),
                                TextInput::make('giata'),
                                Rating::make('rating'),
                                Forms\Components\Textarea::make('short_description')->columnSpanFull(),
                                Forms\Components\FileUpload::make('image')
                                    ->image()
                                    ->imageEditorAspectRatios([null, '16:9', '4:3', '1:1'])
                                            
                                    ->hintAction(
                                        UnsplashPickerAction::make()
                                        ->thumbnail()
                                        ->perPage(10)
                                        ->useSquareDisplay(true),
                                    
                                    ),

                                InputSliderGroup::make()
                                    ->sliders([
                                        InputSlider::make('sustainability_score')
                                        ->default(5),
                                    ]) 
                                    ->min(0)->step(1)
                                   
                                    ->max(10)
                                    ->label('Sustainability Score') ,
                                    
                                    
                                    
                                    
                                Forms\Components\Toggle::make('sustainability_certification')->required(),
                                Forms\Components\Textarea::make('description')->columnSpanFull(),
                                Radio::make('status')->required()
                                ->options([
                                    'draft' => 'Draft',
                                    'published'=> 'Published',
                                    'archived' => 'Archived'
                                ]),
                            ]),

                        Tabs\Tab::make('Information & Contact')
                            ->schema([
                                TextInput::make('roomcount')
                                
                                ->label('Room Count')
                                ->numeric(),
                                
                                Fieldset::make('Check-In Time')
                                    ->schema([
                                        TimePicker::make('from_check_in_time')
                                            ->label('From')
                                            ->withoutSeconds(),
                                        TimePicker::make('to_check_in_time')
                                            ->label('To')
                                            ->withoutSeconds()
                                            ->rule(function (Get $get) {
                                                return function (string $attribute, $value, Closure $fail) use ($get) {
                                                    $from = $get('from_check_in_time');
                                                    if ($from && $value) {
                                                        $fromTime = Carbon::parse($from);
                                                        $toTime = Carbon::parse($value);
                        
                                                        if ($toTime->lessThan($fromTime)) {
                                                            $fail('The "To" time must be after the "From" time.');
                                                        }
                                                    }
                                                };
                                            }),
                                    ]),

                                Fieldset::make('Check-Out Time')
                                    ->schema([
                                        TimePicker::make('from_check_out_time')
                                            ->label('From')
                                            ->withoutSeconds(),
                                        TimePicker::make('to_check_out_time')
                                            ->label('To')
                                            ->withoutSeconds()
                                            ->rule(function (Get $get) {
                                                return function (string $attribute, $value, Closure $fail) use ($get) {
                                                    $from = $get('from_check_out_time');
                                                    if ($from && $value) {
                                                        $fromTime = Carbon::parse($from);
                                                        $toTime = Carbon::parse($value);
                        
                                                        if ($toTime->lessThan($fromTime)) {
                                                            $fail('The "To" time must be after the "From" time.');
                                                        }
                                                    }
                                                };
                                            }),
                        
                                    ]),
                                TextInput::make('email')->required(),
                                TextInput::make('phone')->required(),
                                TextInput::make('website')->required(),
                            ]),
                            

                        Tabs\Tab::make('Address')
                        ->columns(3)
                            ->schema([
                                TextInput::make('map_address')
                                ->columnSpanFull()
                                ->maxLength(255)
                                ->string(),
                            TextInput::make('address')
                                ->label('Map address')
                                ->columnSpanFull(),
                            Map::make('location')
                                ->mapControls([
                                    'mapTypeControl' => true,
                                    'scaleControl' => true,
                                    //'streetViewControl' => true,
                                    //'rotateControl' => true,
                                    'fullscreenControl' => true,
                                    'zoomControl' => true,
                                ])
                                ->columnSpanFull()
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                    $set('latitude', $state['lat']);
                                    $set('longitude', $state['lng']);
                                })->lazy()
                                ->debug()
                                ->geolocate()
                                ->autocomplete('address', ['establishment']) // field on form to use as Places geocompletion field
                                ->autocompleteReverse(true) // reverse geocode marker location to autocomplete field
                                ->reverseGeocode([
                                    'street' => '%n %S',
                                    'city' => '%L',
                                    'state' => '%A1',
                                    'zip' => '%z',
                                ])->debug(),
                            TextInput::make('street')->columnSpan(1),
                            TextInput::make('city')->columnSpan(1),
                            TextInput::make('state')->columnSpan(1),
                            TextInput::make('zip')->columnSpan(1),
                            TextInput::make('latitude')
                                ->readOnly()->lazy(),
                            TextInput::make('longitude')
                                ->readOnly()->lazy(),
                        ])->columns(2),
                            ]),
                        ]);
            
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image'),
                Tables\Columns\TextColumn::make('name')->searchable(),
                RatingColumn::make('rating'),
                
    
                Tables\Columns\TextColumn::make('sustainability_score')->numeric()->sortable(),
                Tables\Columns\IconColumn::make('sustainability_certification')->boolean(),
                Tables\Columns\TextColumn::make('status')->searchable(),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHotels::route('/'),
            'create' => Pages\CreateHotel::route('/create'),
            'edit' => Pages\EditHotel::route('/{record}/edit'),
        ];
    }
    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
            'publish'
        ];
    }
}