<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Room extends Model
{

    protected $fillable = [
        'name',
        'status',
        'occupancy',
        'allocation',
        'price',
        'hotel_contract_id',
    ];
    protected $casts = [
        'occupancy' => 'array',
        'allocation' => 'array',

    ];

    public function hotelContract()
    {
        return $this->belongsTo(Hotel_Contract::class, 'hotel_contract_id');
    }
}