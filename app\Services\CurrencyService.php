<?php

namespace App\Services;

use App\Enums\Currency;

class CurrencyService
{
    // Exchange rates relative to EUR (base currency)
    private static array $exchangeRates = [
        'EUR' => 1.0,
        'USD' => 1.08,
        'TND' => 3.35,
        'GBP' => 0.86,
        'JPY' => 161.50,
    ];

    /**
     * Convert price from EUR to target currency
     */
    public static function convert(float $priceInEur, string $targetCurrency): float
    {
        if (!isset(self::$exchangeRates[$targetCurrency])) {
            return $priceInEur; // Return original if currency not found
        }

        return $priceInEur * self::$exchangeRates[$targetCurrency];
    }

    /**
     * Format price with currency symbol
     */
    public static function format(float $price, string $currency): string
    {
        $currencyEnum = Currency::tryFrom($currency);
        if (!$currencyEnum) {
            return number_format($price, 2);
        }

        $symbol = $currencyEnum->getIcon();
        $formattedPrice = number_format($price, 2);

        return match ($currency) {
            'EUR' => "€{$formattedPrice}",
            'USD' => "\${$formattedPrice}",
            'GBP' => "£{$formattedPrice}",
            'TND' => "{$formattedPrice} DT",
            'JPY' => "¥" . number_format($price, 0), // JPY doesn't use decimals
            default => "{$symbol}{$formattedPrice}",
        };
    }

    /**
     * Convert and format price in one step
     */
    public static function convertAndFormat(float $priceInEur, string $targetCurrency): string
    {
        $convertedPrice = self::convert($priceInEur, $targetCurrency);
        return self::format($convertedPrice, $targetCurrency);
    }

    /**
     * Get all available currencies
     */
    public static function getAllCurrencies(): array
    {
        return [
            'EUR' => Currency::EUR->getLabel(),
            'USD' => Currency::USD->getLabel(),
            'TND' => Currency::TND->getLabel(),
            'GBP' => Currency::GBP->getLabel(),
            'JPY' => Currency::JPY->getLabel(),
        ];
    }

    /**
     * Get current selected currency from session
     */
    public static function getCurrentCurrency(): string
    {
        return session('currency', 'EUR');
    }

    /**
     * Set currency in session
     */
    public static function setCurrency(string $currency): void
    {
        if (array_key_exists($currency, self::$exchangeRates)) {
            session(['currency' => $currency]);
        }
    }
}
