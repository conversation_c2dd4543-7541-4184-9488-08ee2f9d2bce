<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum ContractStatus: string implements HasLabel, HasColor, HasIcon
{
    case draft = 'draft';
    case in_progress = 'in_progress';
    case signed = 'signed';
    case cancelled = 'cancelled';
    case lost = 'lost';

    public function getLabel(): string
    {
        return match ($this) {
            self::draft => 'draft',
            self::in_progress => 'in progress',
            self::signed => 'Signed',
            self::cancelled => 'cancelled',
            self::lost => 'lost',
        };
    }
    public function getColor(): string|array|null
    {
        return match ($this) {

            self::draft => 'warning',
            self::in_progress => 'info',
            self::signed => 'success',
            self::cancelled => 'danger',
            self::lost => 'danger',
        };
    }
    public function getIcon(): string
    {
        return match ($this) {

            self::draft => 'heroicon-o-pencil',
            self::in_progress => 'heroicon-o-pencil',
            self::signed => 'heroicon-o-pencil',
            self::cancelled => 'heroicon-o-pencil',
            self::lost => 'heroicon-o-pencil',
        };
    }
}