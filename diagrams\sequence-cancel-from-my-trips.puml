@startuml Cancel from My Trips Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor User as U
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over U, M : Cancel from My Trips Operation - MVC Architecture

U -> V : Navigate to "My Trips" page
V -> C : GET /my-trips
C -> C : Check user authentication

alt User authenticated
    C -> M : Get user's bookings
    M -> M : Query bookings table\n(user_id, status)
    M -> C : Return user's bookings list
    C -> V : Return my trips view with bookings
    V -> U : Display trip cards with\nbooking details and "Cancel" buttons
    
    U -> V : Click "Cancel Booking" button\non specific trip card
    V -> V : Show cancellation confirmation dialog
    V -> U : Display booking details and\ncancellation policy warning
    
    U -> V : Click "Confirm Cancellation"
    V -> C : DELETE /bookings/{id}/cancel
    C -> M : Find booking by ID
    M -> M : Query booking record\n(id, user_id, status)
    
    alt Booking found and belongs to user
        M -> C : Return booking object
        C -> M : Check cancellation eligibility
        M -> M : Validate booking status\nand cancellation policies
        
        alt Cancellation allowed
            M -> C : Cancellation eligible
            C -> M : Check cancellation policies
            M -> M : Query activity contracts\nfor cancellation rules
            M -> C : Return policy information\n(fees, deadlines)
            
            C -> M : Update booking status
            M -> M : UPDATE bookings\nSET status = 'cancelled'\nWHERE id = ?
            
            alt Cancellation successful
                M -> C : Return updated booking
                C -> M : Process refund (if applicable)
                M -> M : Calculate refund amount\nbased on cancellation policy
                M -> C : Return refund information
                
                C -> C : Log cancellation action
                C -> V : Return success response\nwith refund details
                V -> V : Update trip card status\nto "Cancelled"
                V -> V : Show success message
                V -> U : Display "Booking cancelled successfully"\nwith refund information
                
            else Database error
                M -> C : Return update error
                C -> V : Return error response
                V -> U : Display "Unable to cancel booking.\nPlease contact support"
            end
            
        else Cancellation not allowed
            M -> C : Cancellation not eligible\n(past deadline, policy restrictions)
            C -> V : Return policy violation error
            V -> U : Display "This booking cannot be cancelled\ndue to policy restrictions"
        end
        
    else Booking not found or unauthorized
        M -> C : Booking not found or\ndoesn't belong to user
        C -> V : Return not found error
        V -> U : Display "Booking not found"
    end
    
else User not authenticated
    C -> V : Return authentication error
    V -> U : Redirect to login page
end

note over U, M : Booking cancelled with policy validation and refund processing

@enduml
