<div class="w-full max-w-4xl mx-auto shadow-lg rounded-xl border p-6 flex flex-col gap-6 mb-16">
    <!-- Avatar -->
    <div class="flex justify-center mb-4">
        <div class="w-40 h-40 rounded-full overflow-hidden border-4 border-pink-200 shadow-xl">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="AI Assistant" class="object-cover w-full h-full">
        </div>
    </div>

    <!-- Messages -->
    <div class="flex flex-col gap-4 max-h-[600px] md:max-h-[700px] lg:max-h-[800px] overflow-y-auto" id="chat-container">
        <!-- Initial welcome messages - Always visible -->
        <div class="flex items-start gap-3">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="AI" class="w-8 h-8 rounded-full">
            <div class="bg-pink-100 text-base p-4 rounded-xl rounded-tl-none shadow-md max-w-3xl">
                Bonjour ! Je suis votre assistant voyage intelligent. Je peux vous aider à trouver des hôtels, des
                activités et des transferts.
            </div>
        </div>

        <div class="flex items-start gap-3">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="AI" class="w-8 h-8 rounded-full">
            <div class="bg-pink-100 text-base p-4 rounded-xl rounded-tl-none shadow-md max-w-3xl">
                Exemples de questions :<br>
                <em>"Montre-moi les hôtels à Paris"</em><br>
                <em>"Quelles activités à Marrakech ?"</em><br>
                <em>"Je cherche un transfert en minivan"</em><br>
                <em>"Suggère-moi des destinations"</em>
            </div>
        </div>

        <!-- Dynamic chat messages -->
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $chat; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!--[if BLOCK]><![endif]--><?php if($message['sender'] === 'user'): ?>
                <div class="flex items-start gap-3 justify-end">
                    <div class="bg-pink-500 text-white text-base p-4 rounded-xl rounded-tr-none shadow-md max-w-2xl">
                        <?php echo e($message['text']); ?>

                    </div>
                </div>
            <?php else: ?>
                <div class="flex items-start gap-3">
                    <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="AI" class="w-8 h-8 rounded-full">
                    <div class="flex flex-col gap-4 flex-1">
                        <!-- Message texte -->
                        <div class="bg-pink-100 text-base p-4 rounded-xl rounded-tl-none shadow-md max-w-3xl">
                            <?php echo nl2br(e($message['text'])); ?>

                        </div>

                        <!-- Cartes de destinations si présentes -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($message['show_destination_cards']) && $message['show_destination_cards'] && !empty($message['destinations'])): ?>
                            <div class="max-w-4xl">
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('destination-cards', ['destinations' => $message['destinations'],'title' => 'Select the city or cities you\'d like to visit']);

$__html = app('livewire')->mount($__name, $__params, 'dest-cards-' . $loop->index, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Typing indicator (controlled by JavaScript) -->
        <div class="flex items-start gap-3 hidden" id="typing-indicator">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="AI" class="w-8 h-8 rounded-full">
            <div class="bg-pink-100 text-base p-4 rounded-xl rounded-tl-none shadow-md">
                <div class="flex items-center gap-3">
                    <span class="text-gray-600">L'assistant écrit</span>
                    <div class="flex gap-1">
                        <div class="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style="animation-delay: 0ms;">
                        </div>
                        <div class="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style="animation-delay: 150ms;">
                        </div>
                        <div class="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style="animation-delay: 300ms;">
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- User Input -->
    <form wire:submit="send" class="flex items-center gap-4 mt-6 p-4 bg-gray-50 rounded-xl" id="chat-form">
        <img src="<?php echo e(asset('storage/user.png')); ?>" alt="User"
            class="w-10 h-10 rounded-full border-2 border-pink-200">
        <input type="text" wire:model="message" id="message-input"
            placeholder="Posez-moi une question sur votre voyage..." wire:loading.attr="disabled"
            class="flex-1 border-2 rounded-full px-6 py-3 text-base focus:outline-none focus:ring-2 focus:ring-pink-300 focus:border-pink-300 disabled:opacity-50 disabled:cursor-not-allowed" />
        <button type="submit" id="send-button"
            class="bg-pink-500 text-white p-3 rounded-full hover:bg-pink-600 transition-colors shadow-md hover:shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                stroke="currentColor" id="send-icon">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
            <svg class="w-4 h-4 animate-spin hidden" fill="none" viewBox="0 0 24 24" id="loading-icon">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                </circle>
                <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
            </svg>
        </button>
    </form>

    <script>
        document.addEventListener('livewire:init', () => {
            const typingIndicator = document.getElementById('typing-indicator');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const sendIcon = document.getElementById('send-icon');
            const loadingIcon = document.getElementById('loading-icon');
            const chatForm = document.getElementById('chat-form');

            // Auto-scroll to bottom when new messages are added
            function scrollToBottom() {
                const chatContainer = document.getElementById('chat-container');
                if (chatContainer) {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
            }

            // Show typing indicator
            function showTyping() {
                if (typingIndicator) {
                    typingIndicator.classList.remove('hidden');
                }
                if (messageInput) {
                    messageInput.disabled = true;
                    messageInput.classList.add('bg-gray-100', 'cursor-not-allowed');
                    messageInput.placeholder = "L'assistant écrit...";
                }
                if (sendButton) {
                    sendButton.disabled = true;
                    sendButton.classList.add('opacity-50', 'cursor-not-allowed');
                }
                if (sendIcon) sendIcon.classList.add('hidden');
                if (loadingIcon) loadingIcon.classList.remove('hidden');
                scrollToBottom();
            }

            // Hide typing indicator
            function hideTyping() {
                if (typingIndicator) {
                    typingIndicator.classList.add('hidden');
                }
                if (messageInput) {
                    messageInput.disabled = false;
                    messageInput.classList.remove('bg-gray-100', 'cursor-not-allowed');
                    messageInput.placeholder = "Posez-moi une question sur votre voyage...";
                }
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
                if (sendIcon) sendIcon.classList.remove('hidden');
                if (loadingIcon) loadingIcon.classList.add('hidden');
                scrollToBottom();
            }

            // Listen for events from Livewire
            Livewire.on('message-sent', () => {
                // Clear input field immediately
                const messageInput = document.getElementById('message-input');
                if (messageInput) {
                    messageInput.value = '';
                }

                // Small delay to ensure user message is visible first
                setTimeout(() => {
                    showTyping();
                    setTimeout(scrollToBottom, 50);
                }, 100);
            });

            Livewire.on('message-processed', () => {
                hideTyping();
                setTimeout(scrollToBottom, 100);
            });

            // Handle delayed processing to show typing indicator
            Livewire.on('process-with-delay', (event) => {
                // Wait a bit to ensure typing indicator is visible, then process
                setTimeout(() => {
                    window.Livewire.find('<?php echo e($_instance->getId()); ?>').processDelayedMessage(event.message);
                }, 500); // 500ms delay to show typing indicator
            });



            // Immediate scroll when form is submitted
            if (chatForm) {
                chatForm.addEventListener('submit', function(e) {
                    // Clear input immediately on submit
                    const messageInput = document.getElementById('message-input');
                    if (messageInput && messageInput.value.trim()) {
                        // Store the value for processing, then clear
                        setTimeout(() => {
                            messageInput.value = '';
                        }, 10);
                    }

                    // Scroll to show the user message that will appear
                    setTimeout(scrollToBottom, 50);
                });
            }

            // Auto-scroll on page load
            setTimeout(scrollToBottom, 100);

            // Auto-scroll when the component updates
            Livewire.hook('morph.updated', () => {
                setTimeout(scrollToBottom, 100);
            });
        });
    </script>
</div>
<?php /**PATH C:\laragon\www\travel-platform\resources\views/livewire/chatbot.blade.php ENDPATH**/ ?>