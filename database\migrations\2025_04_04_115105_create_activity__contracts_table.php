<?php

use App\Models\Activity;
use App\Models\ActivityContract;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_contracts', function (Blueprint $table) {
            $table->id();

            $table->string('contract_number');
            $table->string('type');

            $table->boolean('direct_supplier')->default(true);
            $table->string('main_supplier');
            $table->string('intermediate_supplier')->nullable();

            $table->enum('type_of_service', ['Private', 'Shared']);
            $table->string('markets')->nullable();

            $table->text('description');
            $table->json('whats_included')->nullable();
            $table->json('whats_not_included')->nullable();

            $table->timestamps();
        });
        Schema::create('activity_activity_contract', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Activity::class);
            $table->foreignIdFor(ActivityContract::class);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_contracts');
        Schema::dropIfExists('activity_activity_contract');
    }
};