@startuml Add to Project Sequence Diagram
!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor User as U
participant "View" as V
participant "Controller" as C
participant "Model" as M

note over U, M : Add to Project Operation - MVC Architecture

U -> V : Navigate to activity detail page
V -> C : GET /activities/{slug}
C -> M : Find activity by slug
M -> C : Return activity data
C -> V : Return activity detail view
V -> U : Display activity details with\n"Add to my project" button

U -> V : Click "Add to my project" button
V -> V : Open booking modal (Step 1)
V -> U : Display date and time selection

U -> V : Select date from calendar
V -> V : Update selected date
V -> V : Load available time slots
V -> U : Display available times

U -> V : Select preferred time slot
V -> V : Store selected time
U -> V : Click "Next" button
V -> V : Navigate to Step 2
V -> U : Display person selection form

U -> V : Adjust number of adults\nusing +/- buttons
V -> V : Update adults count
V -> V : Calculate total price
V -> U : Display updated price

U -> V : Adjust number of children\nusing +/- buttons
V -> V : Update children count
V -> V : Recalculate total price
V -> U : Display final price

U -> V : Click "Add to Project" button
V -> C : POST /bookings (booking data)
C -> C : Check user authentication

alt User authenticated
    C -> C : Validate booking data\n(date, time, participants)
    
    alt Valid booking data
        C -> M : Calculate total price
        M -> M : Query activity contracts\nfor pricing information
        M -> C : Return pricing data
        
        C -> M : Create booking record
        M -> M : INSERT INTO bookings\n(user_id, activity_id, date, time, adults, children, price, status='confirmed')
        
        alt Booking created successfully
            M -> C : Return booking object
            C -> C : Log booking action
            C -> V : Return success response
            V -> V : Close booking modal
            V -> V : Show success message
            V -> U : Display "Activity added to your trips successfully!"
            
        else Database error
            M -> C : Return creation error
            C -> V : Return error response
            V -> U : Display "Failed to add to project.\nPlease try again"
        end
        
    else Invalid booking data
        C -> V : Return validation errors
        V -> U : Display "Please complete all booking details"
    end
    
else User not authenticated
    C -> V : Return authentication error
    V -> U : Redirect to login page with\nmessage "Please login to book activities"
end

note over U, M : Activity added to user's trip list with confirmed status

@enduml
