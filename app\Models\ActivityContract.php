<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Enums\ContractStatus;
use App\Enums\Currency;

class ActivityContract extends Model
{
    use HasFactory;

    protected $fillable = [
        'contract_number',
        'type',
        'direct_supplier',
        'main_supplier',
        'intermediate_supplier',

        'type_of_service',
        'markets',
        'description',
        'whats_included',
        'whats_not_included',
        'contract_status',
        'signed_date',
        'start_date',
        'end_date',
        'price',
        'inventory',
    ];

    protected $casts = [
        'direct_supplier' => 'boolean',
        'whats_included' => 'array',
        'whats_not_included' => 'array',
        'price' => 'array',
        'inventory' => 'array',
        'policies' => 'array',
        'supplements_and_special_offers' => 'array',
        'contract_status' => ContractStatus::class,

    ];

    public function activities()
    {
        return $this->belongsToMany(Activity::class);
    }
    public function meetingpoint()
    {
        return $this->belongsToMany(MeetingPoint::class,);
    }

    public function markets()
    {
        return $this->belongsToMany(Markets::class, 'activity_contract_market');
    }

    /**
     * Get the adult price from the price JSON field
     */
    public function getAdultPriceAttribute()
    {
        if (is_array($this->price) && isset($this->price['adult'])) {
            return $this->price['adult'];
        }

        // Fallback to a default price if not found
        return $this->price['price'] ?? 299;
    }
}